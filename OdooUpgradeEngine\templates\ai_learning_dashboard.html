{% extends "base.html" %}
{% set title = "AI Learning Dashboard" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-brain me-3"></i>
            AI Learning Dashboard
        </h1>
        <p class="lead">Monitor AI performance, track learning progress, and analyze provider effectiveness</p>
    </div>
</div>

<!-- Learning Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title mb-0">Total Interactions</h6>
                        <h3 class="mb-0" id="total-interactions">-</h3>
                    </div>
                    <i class="fas fa-comments fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title mb-0">Helpfulness Rate</h6>
                        <h3 class="mb-0" id="helpfulness-rate">-</h3>
                    </div>
                    <i class="fas fa-thumbs-up fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title mb-0">Average Accuracy</h6>
                        <h3 class="mb-0" id="average-accuracy">-</h3>
                    </div>
                    <i class="fas fa-bullseye fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="card-title mb-0">Learned Patterns</h6>
                        <h3 class="mb-0" id="learned-patterns">-</h3>
                    </div>
                    <i class="fas fa-lightbulb fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Provider Performance Comparison -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Provider Performance Comparison
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="provider-performance-table">
                        <thead>
                            <tr>
                                <th>Provider</th>
                                <th>Model</th>
                                <th>Interactions</th>
                                <th>Helpfulness Rate</th>
                                <th>Average Accuracy</th>
                                <th>Response Time</th>
                                <th>Recommendation</th>
                            </tr>
                        </thead>
                        <tbody id="provider-performance-tbody">
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Loading provider performance data...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Learning Insights -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Most Common Input Types
                </h5>
            </div>
            <div class="card-body">
                <canvas id="input-types-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Common Failure Reasons
                </h5>
            </div>
            <div class="card-body">
                <div id="failure-reasons-list">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading failure analysis...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Feedback Interface -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comment-dots me-2"></i>
                    Provide AI Feedback
                </h5>
            </div>
            <div class="card-body">
                <form id="ai-feedback-form">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="interaction-id" class="form-label">Interaction ID</label>
                                <input type="text" class="form-control" id="interaction-id" required>
                                <div class="form-text">Enter the AI interaction ID to provide feedback</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="was-helpful" class="form-label">Was Helpful?</label>
                                <select class="form-select" id="was-helpful" required>
                                    <option value="">Select...</option>
                                    <option value="true">Yes, it was helpful</option>
                                    <option value="false">No, it was not helpful</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="accuracy-score" class="form-label">Accuracy Score</label>
                                <input type="range" class="form-range" id="accuracy-score" min="0" max="1" step="0.1" value="0.5">
                                <div class="d-flex justify-content-between">
                                    <small>0% (Poor)</small>
                                    <small id="accuracy-display">50%</small>
                                    <small>100% (Perfect)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="feedback-notes" class="form-label">Feedback Notes</label>
                        <textarea class="form-control" id="feedback-notes" rows="3" placeholder="Describe what was good or what could be improved..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has-correction">
                            <label class="form-check-label" for="has-correction">
                                I have a correction to provide
                            </label>
                        </div>
                    </div>
                    <div class="mb-3" id="correction-section" style="display: none;">
                        <label for="correction-data" class="form-label">Correction Data (JSON)</label>
                        <textarea class="form-control" id="correction-data" rows="4" placeholder='{"corrected_field": "corrected_value"}'></textarea>
                        <div class="form-text">Provide the corrected response in JSON format</div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>Submit Feedback
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let learningData = {};

// Load learning insights on page load
document.addEventListener('DOMContentLoaded', function() {
    loadLearningInsights();
    setupFeedbackForm();
});

function loadLearningInsights() {
    fetch('/api/ai-learning-insights')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                learningData = data;
                updateDashboard(data.insights);
                updateProviderPerformance(data.provider_performance);
            } else {
                console.error('Failed to load learning insights:', data.error);
                showError('Failed to load AI learning data');
            }
        })
        .catch(error => {
            console.error('Error loading learning insights:', error);
            showError('Error loading AI learning data');
        });
}

function updateDashboard(insights) {
    // Update overview cards
    document.getElementById('total-interactions').textContent = insights.total_interactions || 0;
    document.getElementById('helpfulness-rate').textContent = 
        ((insights.helpfulness_rate || 0) * 100).toFixed(1) + '%';
    document.getElementById('average-accuracy').textContent = 
        ((insights.average_accuracy || 0) * 100).toFixed(1) + '%';
    document.getElementById('learned-patterns').textContent = insights.learned_patterns_count || 0;
    
    // Update failure reasons
    updateFailureReasons(insights.common_patterns?.common_failure_reasons || {});
    
    // Create input types chart
    createInputTypesChart(insights.common_patterns?.most_common_input_types || {});
}

function updateProviderPerformance(providerPerformance) {
    const tbody = document.getElementById('provider-performance-tbody');
    
    if (Object.keys(providerPerformance).length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>No provider performance data available yet</p>
                    <p class="small">Start using AI features to see performance metrics</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = Object.entries(providerPerformance).map(([key, performance]) => {
        const [provider, model] = key.split('_');
        const helpfulnessRate = (performance.helpfulness_rate * 100).toFixed(1);
        const averageAccuracy = (performance.average_accuracy * 100).toFixed(1);
        const responseTime = performance.response_time_avg.toFixed(2);
        
        return `
            <tr>
                <td><strong>${provider}</strong></td>
                <td>${model}</td>
                <td>${performance.total_interactions}</td>
                <td>
                    <span class="badge bg-${helpfulnessRate > 70 ? 'success' : helpfulnessRate > 50 ? 'warning' : 'danger'}">
                        ${helpfulnessRate}%
                    </span>
                </td>
                <td>
                    <span class="badge bg-${averageAccuracy > 70 ? 'success' : averageAccuracy > 50 ? 'warning' : 'danger'}">
                        ${averageAccuracy}%
                    </span>
                </td>
                <td>${responseTime}s</td>
                <td>
                    <small class="text-muted">
                        ${getPerformanceRecommendation(performance)}
                    </small>
                </td>
            </tr>
        `;
    }).join('');
}

function updateFailureReasons(failureReasons) {
    const container = document.getElementById('failure-reasons-list');
    
    if (Object.keys(failureReasons).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                <p>No failure patterns detected</p>
                <p class="small">This is good news!</p>
            </div>
        `;
        return;
    }
    
    const sortedReasons = Object.entries(failureReasons)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
    
    container.innerHTML = sortedReasons.map(([reason, count]) => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>${reason.replace('_', ' ').toUpperCase()}</span>
            <span class="badge bg-warning">${count}</span>
        </div>
    `).join('');
}

function createInputTypesChart(inputTypes) {
    const ctx = document.getElementById('input-types-chart').getContext('2d');
    
    if (Object.keys(inputTypes).length === 0) {
        ctx.font = '16px Arial';
        ctx.fillStyle = '#6c757d';
        ctx.textAlign = 'center';
        ctx.fillText('No data available', ctx.canvas.width / 2, ctx.canvas.height / 2);
        return;
    }
    
    // Simple pie chart implementation
    const total = Object.values(inputTypes).reduce((sum, count) => sum + count, 0);
    const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1'];
    
    let currentAngle = 0;
    const centerX = ctx.canvas.width / 2;
    const centerY = ctx.canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;
    
    Object.entries(inputTypes).forEach(([type, count], index) => {
        const sliceAngle = (count / total) * 2 * Math.PI;
        
        // Draw slice
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fillStyle = colors[index % colors.length];
        ctx.fill();
        
        // Draw label
        const labelAngle = currentAngle + sliceAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
        
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${type}\n${count}`, labelX, labelY);
        
        currentAngle += sliceAngle;
    });
}

function setupFeedbackForm() {
    // Accuracy score display
    const accuracyScore = document.getElementById('accuracy-score');
    const accuracyDisplay = document.getElementById('accuracy-display');
    
    accuracyScore.addEventListener('input', function() {
        accuracyDisplay.textContent = Math.round(this.value * 100) + '%';
    });
    
    // Correction section toggle
    const hasCorrection = document.getElementById('has-correction');
    const correctionSection = document.getElementById('correction-section');
    
    hasCorrection.addEventListener('change', function() {
        correctionSection.style.display = this.checked ? 'block' : 'none';
    });
    
    // Form submission
    document.getElementById('ai-feedback-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitFeedback();
    });
}

function submitFeedback() {
    const formData = {
        interaction_id: document.getElementById('interaction-id').value,
        was_helpful: document.getElementById('was-helpful').value === 'true',
        accuracy_score: parseFloat(document.getElementById('accuracy-score').value),
        feedback_notes: document.getElementById('feedback-notes').value,
        correction_data: null
    };
    
    if (document.getElementById('has-correction').checked) {
        try {
            formData.correction_data = JSON.parse(document.getElementById('correction-data').value);
        } catch (e) {
            showError('Invalid JSON in correction data');
            return;
        }
    }
    
    fetch('/api/record-ai-feedback', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Feedback submitted successfully!', 'success');
            document.getElementById('ai-feedback-form').reset();
            document.getElementById('correction-section').style.display = 'none';
            document.getElementById('accuracy-display').textContent = '50%';
            
            // Reload insights to reflect new feedback
            setTimeout(loadLearningInsights, 1000);
        } else {
            showError('Failed to submit feedback: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error submitting feedback:', error);
        showError('Error submitting feedback');
    });
}

function getPerformanceRecommendation(performance) {
    const accuracy = performance.average_accuracy;
    const helpfulness = performance.helpfulness_rate;
    
    if (accuracy > 0.8 && helpfulness > 0.8) {
        return "Excellent - Recommended";
    } else if (accuracy > 0.6 && helpfulness > 0.6) {
        return "Good - Suitable for most tasks";
    } else if (accuracy > 0.4) {
        return "Moderate - Use with caution";
    } else {
        return "Poor - Consider alternatives";
    }
}

function showError(message) {
    // Use the notification system from real-time-updates.js
    if (typeof showNotification === 'function') {
        showNotification(message, 'error');
    } else {
        alert('Error: ' + message);
    }
}
</script>

{% endblock %}
