#!/usr/bin/env python3
"""
Test Semantic Analysis Integration in Migration Workflow
Week 4 Integration Test

This test verifies that semantic analysis is properly integrated
into the migration workflow and produces meaningful results.
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from semantic_analyzer import SemanticAnalyzer, analyze_transformation_semantics

def test_semantic_analysis_integration():
    """Test the semantic analysis integration with sample transformations"""
    
    print("🧪 Testing Semantic Analysis Integration...")
    
    # Create sample original and transformed files
    original_files = {
        "models.py": """
from odoo import models, fields, api

class TestModel(models.Model):
    _name = 'test.model'
    _description = 'Test Model'
    
    name = fields.Char(string='Name')
    
    @api.one
    def old_method(self):
        return self.name
        
    @api.multi
    def multi_method(self):
        for record in self:
            record.name = 'updated'
""",
        "__manifest__.py": """
{
    'name': 'Test Module',
    'version': '********.0',
    'depends': ['base'],
    'data': [
        'views/test_view.xml',
    ],
    'installable': True,
}
"""
    }
    
    transformed_files = {
        "models.py": """
from odoo import models, fields, api

class TestModel(models.Model):
    _name = 'test.model'
    _description = 'Test Model'
    
    name = fields.Char(string='Name')
    
    def old_method(self):
        return self.name
        
    def multi_method(self):
        for record in self:
            record.name = 'updated'
""",
        "__manifest__.py": """
{
    'name': 'Test Module',
    'version': '********.0',
    'depends': ['base'],
    'data': [
        'views/test_view.xml',
    ],
    'installable': True,
}
"""
    }
    
    # Create context for the transformation
    context = {
        'source_version': '15.0',
        'target_version': '17.0',
        'module_name': 'test_module',
        'transformation_type': 'api_decorator_removal',
        'rules_applied': 2,
        'python_transformations': 2
    }
    
    try:
        # Test the semantic analysis
        print("   Running semantic analysis...")
        results = analyze_transformation_semantics(original_files, transformed_files, context)
        
        print("   ✅ Semantic analysis completed successfully!")
        print(f"   📊 Overall Quality Score: {results.get('overall_quality_score', 'N/A')}")
        print(f"   🔍 Confidence Level: {results.get('confidence_level', 'N/A')}")
        
        # Verify expected structure
        required_keys = [
            'overall_quality_score',
            'code_maintainability',
            'business_logic_integrity',
            'integration_quality',
            'confidence_level',
            'analysis_summary'
        ]
        
        missing_keys = [key for key in required_keys if key not in results]
        if missing_keys:
            print(f"   ⚠️  Missing keys in results: {missing_keys}")
        else:
            print("   ✅ All required keys present in results")
        
        # Check for semantic issues
        semantic_issues = results.get('semantic_issues', [])
        print(f"   🔍 Semantic issues found: {len(semantic_issues)}")
        
        if semantic_issues:
            for issue in semantic_issues[:3]:  # Show first 3 issues
                print(f"      - {issue.get('description', 'N/A')} (severity: {issue.get('severity', 'N/A')})")
        
        # Test SemanticAnalyzer directly
        print("\n   Testing SemanticAnalyzer class directly...")
        analyzer = SemanticAnalyzer()
        
        # Test direct analysis
        direct_results = analyzer.analyze_transformation_quality(
            original_files, transformed_files, context
        )
        
        print(f"   📊 Direct analysis quality score: {direct_results.overall_quality_score}")
        print(f"   🔍 Direct analysis confidence: {direct_results.confidence_level}")
        
        print("\n🎉 Semantic Analysis Integration Test PASSED!")
        return True
        
    except Exception as e:
        print(f"   ❌ Semantic analysis failed: {str(e)}")
        print(f"   📋 Error details: {type(e).__name__}")
        return False

def test_semantic_analysis_in_migration_context():
    """Test semantic analysis within migration job context"""
    
    print("\n🧪 Testing Semantic Analysis in Migration Context...")
    
    try:
        # Test the semantic analysis method that would be used in migration orchestrator
        from semantic_analyzer import SemanticAnalyzer
        
        analyzer = SemanticAnalyzer()
        
        # Test with minimal data (fallback scenario)
        context = {
            'source_version': '16.0',
            'target_version': '17.0',
            'module_name': 'test_module',
            'transformation_type': 'comprehensive_migration'
        }
        
        # Test fallback analysis
        result = analyzer._create_default_result("Test fallback analysis")
        print(f"   📊 Fallback analysis created: {result.overall_quality_score}")
        print(f"   🔍 Fallback confidence: {result.confidence_level}")
        
        # Test error handling
        error_result = analyzer._create_error_result("Test error handling")
        print(f"   ❌ Error result created: {error_result.overall_quality_score}")
        print(f"   🔍 Error confidence: {error_result.confidence_level}")
        
        print("   ✅ Migration context testing completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Migration context test failed: {str(e)}")
        return False

def main():
    """Run all semantic analysis integration tests"""
    
    print("🚀 Starting Semantic Analysis Integration Tests")
    print("=" * 50)
    
    # Test results
    test_results = []
    
    # Test 1: Basic semantic analysis integration
    test_results.append(test_semantic_analysis_integration())
    
    # Test 2: Migration context testing
    test_results.append(test_semantic_analysis_in_migration_context())
    
    # Summary
    print("\n" + "=" * 50)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All semantic analysis integration tests PASSED!")
        return True
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)