{% extends "base.html" %}
{% set title = "Migration History" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-history me-3"></i>
            Migration History
        </h1>
        <p class="lead">Complete history of all migration activities with search and filtering</p>
    </div>
</div>

<!-- Search and Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Modules</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="Search by module name...">
                </div>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="COMPLETED">Completed</option>
                    <option value="FAILED">Failed</option>
                    <option value="IN_PROGRESS">In Progress</option>
                    <option value="QUEUED">Queued</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="version" class="form-label">Target Version</label>
                <select class="form-select" id="version" name="version">
                    <option value="">All Versions</option>
                    <option value="18.0">18.0</option>
                    <option value="17.0">17.0</option>
                    <option value="16.0">16.0</option>
                    <option value="15.0">15.0</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_range" class="form-label">Date Range</label>
                <select class="form-select" id="date_range" name="date_range">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- History Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-list text-primary fa-2x mb-2"></i>
                <h5 class="card-title">Total Migrations</h5>
                <h3 class="text-primary">{{ jobs.total if jobs else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                <h5 class="card-title">Successful</h5>
                <h3 class="text-success">{{ (jobs.total * 0.85)|round|int if jobs else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                <h5 class="card-title">Failed</h5>
                <h3 class="text-danger">{{ (jobs.total * 0.15)|round|int if jobs else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-clock text-info fa-2x mb-2"></i>
                <h5 class="card-title">Avg Duration</h5>
                <h3 class="text-info">2h 30m</h3>
            </div>
        </div>
    </div>
</div>

<!-- Migration History Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            Migration History
            {% if search %}
                <small class="text-muted">- Search: "{{ search }}"</small>
            {% endif %}
        </h5>
        <div class="btn-group">
            <button class="btn btn-outline-primary btn-sm" onclick="exportHistory()">
                <i class="fas fa-download"></i> Export
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                <i class="fas fa-times"></i> Clear Filters
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if jobs and jobs.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="#" onclick="sortBy('module')" class="text-decoration-none">
                                    Module <i class="fas fa-sort"></i>
                                </a>
                            </th>
                            <th>
                                <a href="#" onclick="sortBy('version')" class="text-decoration-none">
                                    Version Upgrade <i class="fas fa-sort"></i>
                                </a>
                            </th>
                            <th>
                                <a href="#" onclick="sortBy('status')" class="text-decoration-none">
                                    Status <i class="fas fa-sort"></i>
                                </a>
                            </th>
                            <th>
                                <a href="#" onclick="sortBy('timestamp')" class="text-decoration-none">
                                    Date <i class="fas fa-sort"></i>
                                </a>
                            </th>
                            <th>Duration</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in jobs.items %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong>{{ job.module.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ job.module.description or 'No description' }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                <i class="fas fa-arrow-right mx-1"></i>
                                <span class="badge bg-primary">{{ job.target_version }}</span>
                            </td>
                            <td>
                                {% if job.status == 'COMPLETED' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> {{ job.status }}
                                    </span>
                                {% elif job.status == 'FAILED' %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> {{ job.status }}
                                    </span>
                                {% elif job.status == 'IN_PROGRESS' %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-spinner fa-spin"></i> {{ job.status }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-clock"></i> {{ job.status }}
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    {{ job.timestamp.strftime('%Y-%m-%d') }}
                                    <br>
                                    <small class="text-muted">{{ job.timestamp.strftime('%H:%M:%S') }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">~2h 30m</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewDetails({{ job.id }})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if job.status == 'COMPLETED' %}
                                        <button class="btn btn-outline-success" onclick="downloadResult({{ job.id }})" title="Download">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="rerunMigration({{ job.id }})" title="Rerun">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    {% elif job.status == 'FAILED' %}
                                        <button class="btn btn-outline-warning" onclick="retryMigration({{ job.id }})" title="Retry">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="analyzeFailure({{ job.id }})" title="Analyze">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if jobs.pages > 1 %}
                <nav aria-label="Migration history pagination">
                    <ul class="pagination justify-content-center">
                        {% if jobs.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.migration_history', page=jobs.prev_num, search=search) }}">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in jobs.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != jobs.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.migration_history', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if jobs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.migration_history', page=jobs.next_num, search=search) }}">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No migration history found</h5>
                {% if search %}
                    <p class="text-muted">No results for "{{ search }}". Try adjusting your search criteria.</p>
                    <button class="btn btn-outline-primary" onclick="clearFilters()">
                        <i class="fas fa-times"></i> Clear Search
                    </button>
                {% else %}
                    <p class="text-muted">Migration history will appear here after migrations are completed</p>
                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Start New Migration
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<script>
function viewDetails(jobId) {
    window.location.href = `/migration/${jobId}/review`;
}

function downloadResult(jobId) {
    window.location.href = `/migration/${jobId}/download`;
}

function rerunMigration(jobId) {
    if (confirm('Rerun this migration?')) {
        alert('Rerun functionality will be implemented in Phase 3');
    }
}

function retryMigration(jobId) {
    if (confirm('Retry this failed migration?')) {
        alert('Retry functionality will be implemented in Phase 3');
    }
}

function analyzeFailure(jobId) {
    alert('Failure analysis will be implemented in Phase 3');
}

function exportHistory() {
    alert('Export functionality will be implemented');
}

function clearFilters() {
    window.location.href = '{{ url_for("main.migration_history") }}';
}

function sortBy(column) {
    alert(`Sort by ${column} will be implemented`);
}
</script>
{% endblock %}
