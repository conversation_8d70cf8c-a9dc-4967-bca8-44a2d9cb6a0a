# 📚 API Reference - Odoo Upgrade Engine

**Version:** 3.0  
**Last Updated:** December 12, 2024

## 🎯 Overview

The Odoo Upgrade Engine provides a comprehensive REST API for programmatic access to all migration and analysis functionality. All endpoints return JSON responses and follow RESTful conventions.

## 🔗 Base URL

```
http://localhost:5000
```

## 📋 API Endpoints

### 🏠 **Core Application**

#### `GET /`
**Dashboard - Main application interface**
- **Description:** Returns the main dashboard with system overview
- **Response:** HTML page with navigation and status information

#### `GET /health`
**Health Check**
- **Description:** System health status for monitoring
- **Response:** 
```json
{
  "status": "healthy",
  "timestamp": "2024-12-12T10:30:00Z",
  "version": "3.0"
}
```

---

### 📦 **Module Management**

#### `GET /upload_modules`
**Upload Interface**
- **Description:** Returns the module upload interface
- **Response:** HTML page with drag-and-drop upload functionality

#### `POST /api/upload`
**Upload Module Files**
- **Description:** Upload module files for analysis and migration
- **Content-Type:** `multipart/form-data`
- **Parameters:**
  - `files`: Module files (ZIP, TAR, TAR.GZ)
- **Response:**
```json
{
  "success": true,
  "message": "Files uploaded successfully",
  "modules": [
    {
      "id": 1,
      "name": "module_name",
      "filename": "module.zip",
      "size": 1024000
    }
  ]
}
```

#### `GET /analyze_modules`
**Module Analysis Interface**
- **Description:** Returns interface for viewing and analyzing uploaded modules
- **Response:** HTML page with module list and analysis options

---

### 🔍 **GitHub Integration**

#### `GET /github_integration`
**GitHub Integration Interface**
- **Description:** Returns GitHub repository integration interface
- **Response:** HTML page with repository scanning and module pulling options

#### `POST /api/github/scan`
**Scan GitHub Repository**
- **Description:** Scan a GitHub repository for Odoo modules
- **Content-Type:** `application/json`
- **Body:**
```json
{
  "repository_url": "https://github.com/OCA/server-tools",
  "target_version": "17.0"
}
```
- **Response:**
```json
{
  "success": true,
  "repository": "OCA/server-tools",
  "modules_found": 150,
  "modules": [
    {
      "name": "module_name",
      "path": "module_path",
      "version": "16.0"
    }
  ]
}
```

#### `POST /api/github/pull-modules`
**Pull Modules from Repository**
- **Description:** Pull selected modules from GitHub repository
- **Content-Type:** `application/json`
- **Body:**
```json
{
  "repository_url": "https://github.com/OCA/server-tools",
  "target_version": "17.0",
  "migration_mode": "pipeline"
}
```
- **Response:**
```json
{
  "success": true,
  "message": "Successfully pulled 5 modules",
  "modules": [
    {
      "name": "module_name",
      "migration_job_id": 123,
      "status": "pulled_successfully"
    }
  ],
  "github_stats": {
    "detected": 150,
    "downloaded": 5,
    "processed": 5
  }
}
```

---

### 🤖 **AI Provider Management**

#### `GET /ai_providers`
**AI Providers Configuration Interface**
- **Description:** Returns AI provider configuration interface
- **Response:** HTML page with AI provider settings and status

#### `GET /api/ai-provider-status`
**Get AI Provider Status**
- **Description:** Get current AI provider configuration and status
- **Response:**
```json
{
  "success": true,
  "provider": {
    "name": "ollama",
    "model": "deepseek-r1:8b",
    "status": "Active"
  },
  "settings": {
    "confidence_threshold": 80,
    "enable_auto_approval": true,
    "require_low_risk": true
  }
}
```

#### `POST /api/ai-providers/configure`
**Configure AI Provider**
- **Description:** Configure AI provider settings
- **Content-Type:** `application/json`
- **Body:**
```json
{
  "provider": "ollama",
  "model": "deepseek-r1:8b",
  "api_key": "optional",
  "settings": {
    "confidence_threshold": 80,
    "enable_auto_approval": true
  }
}
```

---

### ⚙️ **Migration Management**

#### `GET /migration_orchestrator`
**Migration Orchestrator Interface**
- **Description:** Returns migration job management interface
- **Response:** HTML page with migration job list and controls

#### `POST /api/migration-jobs`
**Create Migration Job**
- **Description:** Create a new migration job for a module
- **Content-Type:** `application/json`
- **Body:**
```json
{
  "module_id": 1,
  "target_version": "17.0",
  "migration_type": "pipeline"
}
```
- **Response:**
```json
{
  "success": true,
  "job_id": 123,
  "status": "QUEUED",
  "message": "Migration job created successfully"
}
```

#### `GET /api/migration-jobs/{job_id}`
**Get Migration Job Status**
- **Description:** Get detailed status of a migration job
- **Response:**
```json
{
  "job_id": 123,
  "module_name": "test_module",
  "status": "RUNNING",
  "progress": 65,
  "target_version": "17.0",
  "created_at": "2024-12-12T10:00:00Z",
  "updated_at": "2024-12-12T10:05:00Z",
  "log": "Processing module transformation..."
}
```

#### `POST /api/migration-jobs/{job_id}/approve`
**Approve Migration Job**
- **Description:** Approve a migration job that's awaiting approval
- **Response:**
```json
{
  "success": true,
  "message": "Migration job approved and resumed"
}
```

#### `POST /api/migration-jobs/{job_id}/cancel`
**Cancel Migration Job**
- **Description:** Cancel a running or queued migration job
- **Response:**
```json
{
  "success": true,
  "message": "Migration job cancelled"
}
```

---

### 📊 **Reports and Analysis**

#### `GET /api/visual-diff/{job_id}`
**Get Visual Diff Report**
- **Description:** Get HTML visual diff report for a migration job
- **Response:** HTML content showing line-by-line code changes

#### `GET /api/download-result/{job_id}`
**Download Migration Result**
- **Description:** Download the migrated module as a ZIP file
- **Response:** Binary ZIP file with migrated module

---

### 🔧 **Bulk Migration**

#### `GET /bulk_migration`
**Bulk Migration Interface**
- **Description:** Returns bulk migration management interface
- **Response:** HTML page with database connection and bulk processing options

#### `POST /api/test-db-connection`
**Test Database Connection**
- **Description:** Test connection to external Odoo database
- **Content-Type:** `application/json`
- **Body:**
```json
{
  "host": "localhost",
  "port": 5432,
  "database": "odoo_db",
  "username": "odoo",
  "password": "password"
}
```

#### `POST /api/discover-modules`
**Discover Installed Modules**
- **Description:** Discover modules in connected Odoo database
- **Response:**
```json
{
  "success": true,
  "modules_found": 250,
  "modules": [
    {
      "name": "sale",
      "version": "********.0",
      "state": "installed"
    }
  ]
}
```

---

## 🔒 Authentication

Currently, the API uses session-based authentication through the web interface. API key authentication is planned for future releases.

## 📝 Response Format

All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

## 🚀 Rate Limiting

- **Current:** No rate limiting implemented
- **Planned:** 100 requests per minute per IP address

## 📋 Status Codes

- **200 OK** - Request successful
- **400 Bad Request** - Invalid request parameters
- **404 Not Found** - Resource not found
- **500 Internal Server Error** - Server error

## 🔧 SDK and Examples

### Python Example
```python
import requests

# Upload a module
files = {'files': open('module.zip', 'rb')}
response = requests.post('http://localhost:5000/api/upload', files=files)
print(response.json())

# Create migration job
data = {
    'module_id': 1,
    'target_version': '17.0',
    'migration_type': 'pipeline'
}
response = requests.post('http://localhost:5000/api/migration-jobs', json=data)
print(response.json())
```

### JavaScript Example
```javascript
// Scan GitHub repository
const response = await fetch('/api/github/scan', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        repository_url: 'https://github.com/OCA/server-tools',
        target_version: '17.0'
    })
});
const data = await response.json();
console.log(data);
```

---

**For more examples and detailed usage, see the web interface at http://localhost:5000**
