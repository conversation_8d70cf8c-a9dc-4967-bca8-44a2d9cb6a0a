#!/usr/bin/env python3
"""
Comprehensive Menu Pages and Features Analysis
Examines each HTML template to catalog all buttons, forms, and features
"""

import os
import re
from bs4 import BeautifulSoup

def analyze_template(template_path):
    """Analyze a template file for buttons, forms, and features"""
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # Find buttons
        buttons = []
        for button in soup.find_all(['button', 'input']):
            if button.name == 'input' and button.get('type') not in ['submit', 'button']:
                continue
            
            text = button.get_text(strip=True) or button.get('value', '') or button.get('title', '')
            onclick = button.get('onclick', '')
            btn_class = button.get('class', [])
            
            if text or onclick:
                buttons.append({
                    'text': text,
                    'onclick': onclick,
                    'class': ' '.join(btn_class) if isinstance(btn_class, list) else btn_class,
                    'type': button.name
                })
        
        # Find links that look like buttons
        for link in soup.find_all('a'):
            link_class = link.get('class', [])
            if isinstance(link_class, list):
                link_class = ' '.join(link_class)
            
            if 'btn' in link_class:
                text = link.get_text(strip=True)
                href = link.get('href', '')
                if text:
                    buttons.append({
                        'text': text,
                        'href': href,
                        'class': link_class,
                        'type': 'link-button'
                    })
        
        # Find forms
        forms = []
        for form in soup.find_all('form'):
            action = form.get('action', '')
            method = form.get('method', 'GET')
            form_id = form.get('id', '')
            
            # Find inputs in this form
            inputs = []
            for inp in form.find_all(['input', 'select', 'textarea']):
                inp_name = inp.get('name', '')
                inp_type = inp.get('type', inp.name)
                inp_id = inp.get('id', '')
                if inp_name or inp_id:
                    inputs.append({
                        'name': inp_name,
                        'type': inp_type,
                        'id': inp_id
                    })
            
            forms.append({
                'action': action,
                'method': method,
                'id': form_id,
                'inputs': inputs
            })
        
        # Find key features/sections
        features = []
        
        # Look for cards
        cards = soup.find_all('div', class_=re.compile(r'card'))
        for card in cards:
            title_elem = card.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            if title_elem:
                features.append(f"Card: {title_elem.get_text(strip=True)}")
        
        # Look for modals
        modals = soup.find_all('div', class_=re.compile(r'modal'))
        for modal in modals:
            modal_id = modal.get('id', '')
            title_elem = modal.find(class_=re.compile(r'modal-title'))
            title = title_elem.get_text(strip=True) if title_elem else modal_id
            if title:
                features.append(f"Modal: {title}")
        
        # Look for tables
        tables = soup.find_all('table')
        for table in tables:
            headers = [th.get_text(strip=True) for th in table.find_all('th')]
            if headers:
                features.append(f"Table: {', '.join(headers[:3])}{'...' if len(headers) > 3 else ''}")
        
        return {
            'buttons': buttons,
            'forms': forms,
            'features': features
        }
        
    except Exception as e:
        return {'error': str(e)}

def main():
    print("📋 COMPREHENSIVE MENU PAGES AND FEATURES ANALYSIS")
    print("=" * 80)
    
    # Define menu pages from the sidebar
    menu_pages = [
        # CORE WORKFLOW
        ('Dashboard', 'templates/index.html'),
        ('Migration Orchestrator', 'templates/migration_orchestrator.html'),
        ('Migration Types', 'templates/migration_types.html'),
        ('Migration Jobs', 'templates/migration_jobs.html'),
        
        # MIGRATION TOOLS
        ('Upload Modules', 'templates/upload_modules.html'),
        ('Upload Sync From Github', 'templates/github_integration.html'),
        ('Analyze Modules & Migrate', 'templates/analyze_modules.html'),
        ('Bulk Migration', 'templates/bulk_migration.html'),
        ('Automated Migrations', 'templates/automation_dashboard.html'),
        ('Contribute Modules', 'templates/contributor_upload.html'),
        
        # TESTING & ENVIRONMENTS
        ('Testing Dashboard', 'templates/testing/dashboard.html'),
        ('Manual Interventions', 'templates/manual_interventions.html'),
        ('Docker Environments', 'templates/docker_environments.html'),
        
        # SYSTEM & CONFIGURATION
        ('Health Monitor', 'templates/health_dashboard.html'),
        ('AI Settings', 'templates/ai_providers.html'),
    ]
    
    total_pages = 0
    total_buttons = 0
    total_forms = 0
    total_features = 0
    
    for page_name, template_path in menu_pages:
        print(f"\n📄 {page_name}")
        print("-" * 60)
        
        if not os.path.exists(template_path):
            print(f"❌ Template not found: {template_path}")
            continue
        
        analysis = analyze_template(template_path)
        
        if 'error' in analysis:
            print(f"❌ Error analyzing template: {analysis['error']}")
            continue
        
        total_pages += 1
        
        # Buttons
        buttons = analysis['buttons']
        total_buttons += len(buttons)
        print(f"🔘 Buttons/Actions ({len(buttons)}):")
        for i, btn in enumerate(buttons[:10]):  # Show first 10
            text = btn['text'][:40] + '...' if len(btn['text']) > 40 else btn['text']
            print(f"   {i+1:2d}. {text}")
        if len(buttons) > 10:
            print(f"   ... and {len(buttons)-10} more buttons")
        
        # Forms
        forms = analysis['forms']
        total_forms += len(forms)
        if forms:
            print(f"📝 Forms ({len(forms)}):")
            for i, form in enumerate(forms):
                action = form['action'] or 'No action'
                method = form['method']
                inputs_count = len(form['inputs'])
                print(f"   {i+1}. {method} {action} ({inputs_count} inputs)")
        
        # Features
        features = analysis['features']
        total_features += len(features)
        if features:
            print(f"⚙️  Key Features ({len(features)}):")
            for i, feature in enumerate(features[:5]):  # Show first 5
                print(f"   {i+1}. {feature}")
            if len(features) > 5:
                print(f"   ... and {len(features)-5} more features")
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY STATISTICS")
    print("=" * 80)
    print(f"📄 Total Active Pages: {total_pages}")
    print(f"🔘 Total Buttons/Actions: {total_buttons}")
    print(f"📝 Total Forms: {total_forms}")
    print(f"⚙️  Total Features: {total_features}")
    print(f"📈 Average per page: {total_buttons/total_pages:.1f} buttons, {total_forms/total_pages:.1f} forms")

if __name__ == "__main__":
    main()
