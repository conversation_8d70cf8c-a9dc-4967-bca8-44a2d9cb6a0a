# extensions.py
from flask_sqlalchemy import SQLAlchemy
import os

db = SQLAlchemy()

# Initialize Celery only if Redis is available
try:
    import redis
    # Test Redis connection
    redis_client = redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=1)
    redis_client.ping()

    # Redis is available, initialize Celery
    from celery import Celery
    celery = Celery(__name__, broker='redis://localhost:6379/0', backend='redis://localhost:6379/0')
    CELERY_AVAILABLE = True
    print("✅ Redis connection successful - Celery background processing enabled")

except (ImportError, redis.ConnectionError, redis.TimeoutError) as e:
    # Redis not available, create a mock Celery for development
    print(f"⚠️  Redis not available ({e}) - Using synchronous processing for development")

    class MockCelery:
        def __init__(self):
            self.conf = {}

        def update(self, config):
            self.conf.update(config)

    class MockTask:
        def __init__(self, func):
            self.func = func

        def delay(self, *args, **kwargs):
            # Execute synchronously instead of async
            print(f"🔄 Executing task synchronously: {self.func.__name__}")
            return self.func(*args, **kwargs)

        def apply_async(self, *args, **kwargs):
            return self.delay(*args, **kwargs)

    celery = MockCelery()
    CELERY_AVAILABLE = False