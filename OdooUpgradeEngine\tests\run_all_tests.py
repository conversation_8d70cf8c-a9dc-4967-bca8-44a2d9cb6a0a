#!/usr/bin/env python3
"""
Master Test Runner for Odoo Upgrade Engine
Runs comprehensive system validation and generates final report
"""

import subprocess
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any

class MasterTestRunner:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.test_results = {}
        self.start_time = datetime.now()
        
    def run_test_script(self, script_name: str, test_name: str) -> Dict[str, Any]:
        """Run a test script and capture results"""
        print(f"🚀 Running {test_name}...")
        print("-" * 40)
        
        try:
            # Run the test script
            result = subprocess.run(
                [sys.executable, script_name, self.base_url],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            success = result.returncode == 0
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            return {
                'success': success,
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'execution_time': time.time()
            }
            
        except subprocess.TimeoutExpired:
            print(f"❌ {test_name} timed out after 5 minutes")
            return {
                'success': False,
                'returncode': -1,
                'stdout': '',
                'stderr': 'Test timed out',
                'execution_time': time.time()
            }
        except Exception as e:
            print(f"❌ Error running {test_name}: {e}")
            return {
                'success': False,
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'execution_time': time.time()
            }
    
    def check_server_availability(self) -> bool:
        """Check if the server is available before running tests"""
        print("🔍 Checking server availability...")
        
        try:
            import requests
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                print(f"✅ Server is available at {self.base_url}")
                return True
            else:
                print(f"❌ Server returned HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return False
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🎯 ODOO UPGRADE ENGINE - COMPREHENSIVE TEST SUITE")
        print("=" * 60)
        print(f"🕒 Started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Testing URL: {self.base_url}")
        print("=" * 60)
        
        # Check server availability first
        if not self.check_server_availability():
            print("\n🚨 CRITICAL ERROR: Server is not available")
            print("Please ensure the Odoo Upgrade Engine is running before running tests.")
            print("\nTo start the server:")
            print("  python start_application.py --with-worker")
            return False
        
        print("\n")
        
        # Define test suites
        test_suites = [
            ('comprehensive_system_test.py', 'Comprehensive System Test'),
            ('user_workflow_test.py', 'User Workflow Test'),
            ('performance_test.py', 'Performance Test')
        ]
        
        # Run each test suite
        for script, name in test_suites:
            print(f"\n{'='*60}")
            self.test_results[name] = self.run_test_script(script, name)
            print(f"{'='*60}")
            
            # Add delay between tests
            time.sleep(2)
        
        # Generate final report
        self.generate_final_report()
        
        # Return overall success
        return all(result['success'] for result in self.test_results.values())
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        print("\n" + "=" * 60)
        print("📊 FINAL TEST REPORT")
        print("=" * 60)
        
        # Test summary
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"🕒 Total Duration: {total_duration.total_seconds():.2f} seconds")
        print(f"📈 Test Suites: {passed_tests}/{total_tests} passed ({(passed_tests/total_tests)*100:.1f}%)")
        
        # Individual test results
        print("\n📋 TEST SUITE RESULTS:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"  {status} {test_name}")
            if not result['success']:
                print(f"       Error: {result['stderr'][:100]}...")
        
        # Load detailed reports if available
        self.load_and_summarize_detailed_reports()
        
        # Overall system assessment
        print("\n🎯 OVERALL SYSTEM ASSESSMENT:")
        
        if passed_tests == total_tests:
            print("🎉 SYSTEM STATUS: PRODUCTION READY")
            print("✅ All test suites passed successfully")
            print("✅ System is ready for production deployment")
            print("✅ All features are working correctly")
            print("✅ Performance is within acceptable limits")
            
        elif passed_tests >= total_tests * 0.8:
            print("⚠️  SYSTEM STATUS: MOSTLY READY")
            print("🔧 Most test suites passed with minor issues")
            print("🔧 System is functional but may need minor fixes")
            print("🔧 Consider addressing failed tests before production")
            
        else:
            print("🚨 SYSTEM STATUS: NEEDS ATTENTION")
            print("❌ Multiple test suites failed")
            print("❌ System may not be ready for production")
            print("❌ Significant issues need to be addressed")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        if passed_tests == total_tests:
            print("  • System is ready for production deployment")
            print("  • Consider setting up monitoring and alerting")
            print("  • Document deployment procedures")
            print("  • Plan for user training and onboarding")
        else:
            print("  • Review failed test details in individual reports")
            print("  • Fix critical issues before production deployment")
            print("  • Re-run tests after fixes are applied")
            print("  • Consider additional testing in staging environment")
        
        # Save master report
        self.save_master_report(total_duration, passed_tests, total_tests)
    
    def load_and_summarize_detailed_reports(self):
        """Load and summarize detailed test reports"""
        report_files = [
            ('comprehensive_test_report.json', 'System Test'),
            ('workflow_test_report.json', 'Workflow Test'),
            ('performance_test_report.json', 'Performance Test')
        ]
        
        print("\n📄 DETAILED REPORT SUMMARY:")
        
        for report_file, report_name in report_files:
            try:
                with open(report_file, 'r') as f:
                    report_data = json.load(f)
                
                if 'test_summary' in report_data:
                    summary = report_data['test_summary']
                    
                    if report_name == 'System Test':
                        total = summary.get('total_tests', 0)
                        passed = summary.get('passed_tests', 0)
                        print(f"  🔧 {report_name}: {passed}/{total} individual tests passed")
                        
                    elif report_name == 'Workflow Test':
                        total_steps = summary.get('total_steps', 0)
                        passed_steps = summary.get('passed_steps', 0)
                        print(f"  🔄 {report_name}: {passed_steps}/{total_steps} workflow steps passed")
                        
                    elif report_name == 'Performance Test':
                        if 'performance_score' in summary:
                            score = summary['performance_score']
                            assessment = summary.get('overall_assessment', 'Unknown')
                            print(f"  ⚡ {report_name}: {score}/100 performance score ({assessment})")
                
            except FileNotFoundError:
                print(f"  ⚠️  {report_name}: Detailed report not found")
            except Exception as e:
                print(f"  ❌ {report_name}: Error reading report - {e}")
    
    def save_master_report(self, duration, passed_tests, total_tests):
        """Save master test report"""
        report = {
            'test_execution': {
                'start_time': self.start_time.isoformat(),
                'end_time': datetime.now().isoformat(),
                'duration_seconds': duration.total_seconds(),
                'base_url': self.base_url
            },
            'test_summary': {
                'total_test_suites': total_tests,
                'passed_test_suites': passed_tests,
                'failed_test_suites': total_tests - passed_tests,
                'success_rate': (passed_tests / total_tests) * 100,
                'overall_status': 'PRODUCTION_READY' if passed_tests == total_tests else 'MOSTLY_READY' if passed_tests >= total_tests * 0.8 else 'NEEDS_ATTENTION'
            },
            'test_suite_results': self.test_results
        }
        
        with open('master_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Master test report saved to: master_test_report.json")
        
        # Also create a simple summary file
        with open('test_summary.txt', 'w') as f:
            f.write(f"Odoo Upgrade Engine Test Summary\n")
            f.write(f"================================\n\n")
            f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Test URL: {self.base_url}\n")
            f.write(f"Duration: {duration.total_seconds():.2f} seconds\n\n")
            f.write(f"Results: {passed_tests}/{total_tests} test suites passed\n")
            f.write(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%\n\n")
            
            if passed_tests == total_tests:
                f.write("Status: PRODUCTION READY ✅\n")
            elif passed_tests >= total_tests * 0.8:
                f.write("Status: MOSTLY READY ⚠️\n")
            else:
                f.write("Status: NEEDS ATTENTION 🚨\n")
        
        print(f"📄 Test summary saved to: test_summary.txt")

def main():
    """Main test execution"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    runner = MasterTestRunner(base_url)
    success = runner.run_all_tests()
    
    print(f"\n🏁 Test execution completed. Overall result: {'SUCCESS' if success else 'FAILURE'}")
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
