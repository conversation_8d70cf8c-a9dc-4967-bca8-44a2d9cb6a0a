from odoo import models, fields, api


class TestModel(models.Model):
    _name = 'test.model'
    _inherit = 'mail.thread'
    name = fields.Char('Name')
    amount = fields.Float('Amount')

    def compute_total(self):
        for record in self:
            self.total = record.amount * 2
            self.write({'total': self.total})

    @api.multi
    def old_method(self):
        return self.search([])
