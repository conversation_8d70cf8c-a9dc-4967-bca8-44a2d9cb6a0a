
import os
import json
import logging
from datetime import datetime
from flask import Blueprint, jsonify, render_template
from pathlib import Path

health_blueprint = Blueprint('health', __name__, url_prefix='/health')
logger = logging.getLogger(__name__)

def check_system_health():
    """Comprehensive system health check"""
    health_status = {
        'timestamp': datetime.now().isoformat(),
        'overall_status': 'healthy',
        'services': {},
        'errors': [],
        'warnings': []
    }
    
    # Check automation system
    try:
        from automation_system import OdooModuleAutomationSystem
        automation_system = OdooModuleAutomationSystem()
        health_status['services']['automation'] = {
            'status': 'healthy',
            'last_run': None,
            'config_valid': True
        }
    except Exception as e:
        health_status['services']['automation'] = {
            'status': 'error',
            'error': str(e)
        }
        health_status['errors'].append(f"Automation system error: {e}")
    
    # Check Docker availability
    try:
        import docker
        client = docker.from_env()
        client.ping()
        health_status['services']['docker'] = {
            'status': 'healthy',
            'available': True
        }
    except Exception as e:
        health_status['services']['docker'] = {
            'status': 'unavailable',
            'error': str(e)
        }
        health_status['warnings'].append(f"Docker not available: {e}")
    
    # Check testing engine
    try:
        from module_testing_engine import ModuleTestingEngine
        testing_engine = ModuleTestingEngine()
        health_status['services']['testing'] = {
            'status': 'healthy',
            'docker_enabled': testing_engine.config.get('docker', {}).get('enabled', False),
            'ai_enabled': testing_engine.ai_client is not None
        }
    except Exception as e:
        health_status['services']['testing'] = {
            'status': 'error',
            'error': str(e)
        }
        health_status['errors'].append(f"Testing engine error: {e}")
    
    # Check scheduler
    try:
        from hourly_scheduler import get_scheduler
        scheduler = get_scheduler()
        status = scheduler.get_status()
        health_status['services']['scheduler'] = {
            'status': 'healthy' if status.get('running', False) else 'stopped',
            'last_run': status.get('last_run'),
            'next_run': status.get('next_run')
        }
    except Exception as e:
        health_status['services']['scheduler'] = {
            'status': 'error',
            'error': str(e)
        }
        health_status['errors'].append(f"Scheduler error: {e}")
    
    # Check log files
    log_dir = Path('automation_logs')
    if log_dir.exists():
        log_files = list(log_dir.glob('*.log'))
        health_status['services']['logging'] = {
            'status': 'healthy',
            'log_files': len(log_files),
            'recent_logs': [f.name for f in sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]]
        }
    else:
        health_status['services']['logging'] = {
            'status': 'warning',
            'error': 'Log directory not found'
        }
        health_status['warnings'].append("Log directory not found")
    
    # Determine overall status
    if health_status['errors']:
        health_status['overall_status'] = 'error'
    elif health_status['warnings']:
        health_status['overall_status'] = 'warning'
    
    return health_status

@health_blueprint.route('/')
def health_check():
    """Health check endpoint"""
    return jsonify(check_system_health())

@health_blueprint.route('/dashboard')
def health_dashboard():
    """Health dashboard page"""
    health_data = check_system_health()
    return render_template('health_dashboard.html', health=health_data)

@health_blueprint.route('/fix-common-issues', methods=['POST'])
def fix_common_issues():
    """Attempt to fix common issues automatically"""
    fixes_applied = []
    
    try:
        # Fix git clone conflict
        modules_dir = 'odoo_modules'
        if os.path.exists(modules_dir):
            import shutil
            shutil.rmtree(modules_dir)
            fixes_applied.append("Removed conflicting odoo_modules directory")
        
        # Ensure log directories exist
        log_dirs = ['automation_logs', 'testing/logs', 'testing/results']
        for log_dir in log_dirs:
            Path(log_dir).mkdir(parents=True, exist_ok=True)
            fixes_applied.append(f"Created log directory: {log_dir}")
        
        # Restart scheduler
        try:
            from hourly_scheduler import start_scheduler
            start_scheduler()
            fixes_applied.append("Restarted scheduler")
        except Exception as e:
            logger.error(f"Failed to restart scheduler: {e}")
        
        return jsonify({
            'success': True,
            'fixes_applied': fixes_applied,
            'message': f'Applied {len(fixes_applied)} fixes'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })
