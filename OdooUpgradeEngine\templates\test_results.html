{% extends "base.html" %}
{% set title = "Test Results" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-vial me-3"></i>
            Test Results
        </h1>
        <p class="lead">Detailed test results and validation reports for migrations</p>
    </div>
</div>

<!-- Test Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                <h5 class="card-title">Tests Passed</h5>
                <h3 class="text-success">{{ (tested_jobs|length * 0.85)|round|int if tested_jobs else 0 }}</h3>
                <small class="text-muted">85% success rate</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                <h5 class="card-title">Tests Failed</h5>
                <h3 class="text-danger">{{ (tested_jobs|length * 0.15)|round|int if tested_jobs else 0 }}</h3>
                <small class="text-muted">15% failure rate</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-flask text-info fa-2x mb-2"></i>
                <h5 class="card-title">Total Tests</h5>
                <h3 class="text-info">{{ tested_jobs|length if tested_jobs else 0 }}</h3>
                <small class="text-muted">Migrations tested</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-clock text-warning fa-2x mb-2"></i>
                <h5 class="card-title">Avg Test Time</h5>
                <h3 class="text-warning">45m</h3>
                <small class="text-muted">Per migration</small>
            </div>
        </div>
    </div>
</div>

<!-- Test Categories -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    Test Categories Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center mb-3">
                            <i class="fas fa-cogs text-primary fa-2x mb-2"></i>
                            <h6>Unit Tests</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" style="width: 92%">92%</div>
                            </div>
                            <small class="text-muted">1,247 tests passed</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center mb-3">
                            <i class="fas fa-puzzle-piece text-info fa-2x mb-2"></i>
                            <h6>Integration Tests</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" style="width: 88%">88%</div>
                            </div>
                            <small class="text-muted">456 tests passed</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center mb-3">
                            <i class="fas fa-tachometer-alt text-warning fa-2x mb-2"></i>
                            <h6>Performance Tests</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-warning" style="width: 75%">75%</div>
                            </div>
                            <small class="text-muted">123 tests passed</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center mb-3">
                            <i class="fas fa-shield-alt text-danger fa-2x mb-2"></i>
                            <h6>Security Tests</h6>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" style="width: 95%">95%</div>
                            </div>
                            <small class="text-muted">89 tests passed</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            Recent Test Results
        </h5>
        <div class="btn-group">
            <button class="btn btn-outline-primary btn-sm" onclick="runAllTests()">
                <i class="fas fa-play"></i> Run All Tests
            </button>
            <button class="btn btn-outline-info btn-sm" onclick="exportResults()">
                <i class="fas fa-download"></i> Export Results
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if tested_jobs %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Module</th>
                            <th>Version</th>
                            <th>Test Status</th>
                            <th>Test Types</th>
                            <th>Duration</th>
                            <th>Coverage</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in tested_jobs %}
                        <tr>
                            <td>
                                <strong>{{ job.module.name }}</strong>
                                <br>
                                <small class="text-muted">{{ job.module.description or 'No description' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                <i class="fas fa-arrow-right mx-1"></i>
                                <span class="badge bg-primary">{{ job.target_version }}</span>
                            </td>
                            <td>
                                {% if job.status == 'COMPLETED' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> PASSED
                                    </span>
                                {% elif job.status == 'FAILED' %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> FAILED
                                    </span>
                                {% elif job.status == 'TESTING' %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-spinner fa-spin"></i> RUNNING
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-clock"></i> PENDING
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex flex-wrap gap-1">
                                    <span class="badge bg-primary">Unit</span>
                                    <span class="badge bg-info">Integration</span>
                                    {% if job.status == 'COMPLETED' %}
                                        <span class="badge bg-warning">Performance</span>
                                        <span class="badge bg-danger">Security</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">45m 30s</span>
                            </td>
                            <td>
                                <div class="progress" style="width: 80px; height: 20px;">
                                    {% set coverage = 85 + (loop.index % 15) %}
                                    <div class="progress-bar 
                                        {% if coverage >= 90 %}bg-success
                                        {% elif coverage >= 75 %}bg-warning
                                        {% else %}bg-danger{% endif %}" 
                                        style="width: {{ coverage }}%">{{ coverage }}%</div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewTestDetails({{ job.id }})" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="rerunTests({{ job.id }})" title="Rerun Tests">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    {% if job.status == 'FAILED' %}
                                        <button class="btn btn-outline-warning" onclick="debugTest({{ job.id }})" title="Debug">
                                            <i class="fas fa-bug"></i>
                                        </button>
                                    {% endif %}
                                    <button class="btn btn-outline-success" onclick="downloadTestReport({{ job.id }})" title="Download Report">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-vial fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No test results available</h5>
                <p class="text-muted">Test results will appear here after migrations are tested</p>
                <a href="{{ url_for('main.testing_dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-play"></i> Start Testing
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Test Insights -->
{% if tested_jobs %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Test Results Summary
                </h5>
            </div>
            <div class="card-body">
                <canvas id="testResultsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Test Insights
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-success">
                        <i class="fas fa-check-circle"></i> Common Success Patterns
                    </h6>
                    <ul class="list-unstyled">
                        <li><small>• Simple module upgrades: 95% success rate</small></li>
                        <li><small>• Standard Odoo modules: 92% success rate</small></li>
                        <li><small>• Well-documented modules: 88% success rate</small></li>
                    </ul>
                </div>
                <div class="mb-3">
                    <h6 class="text-warning">
                        <i class="fas fa-exclamation-triangle"></i> Common Failure Points
                    </h6>
                    <ul class="list-unstyled">
                        <li><small>• Custom field migrations: 25% failure rate</small></li>
                        <li><small>• Complex workflow modules: 30% failure rate</small></li>
                        <li><small>• Third-party integrations: 35% failure rate</small></li>
                    </ul>
                </div>
                <div>
                    <h6 class="text-info">
                        <i class="fas fa-chart-line"></i> Performance Trends
                    </h6>
                    <ul class="list-unstyled">
                        <li><small>• Test execution time improving by 15%</small></li>
                        <li><small>• Coverage increasing steadily</small></li>
                        <li><small>• False positive rate decreasing</small></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function viewTestDetails(jobId) {
    window.location.href = `/migration/${jobId}/test-details`;
}

function rerunTests(jobId) {
    if (confirm('Rerun tests for this migration?')) {
        alert('Test rerun functionality will be implemented');
    }
}

function debugTest(jobId) {
    alert('Test debugging functionality will be implemented');
}

function downloadTestReport(jobId) {
    window.location.href = `/migration/${jobId}/test-report`;
}

function runAllTests() {
    if (confirm('Run tests for all pending migrations?')) {
        alert('Bulk test execution will be implemented');
    }
}

function exportResults() {
    alert('Export test results functionality will be implemented');
}

// Chart placeholder
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('testResultsChart');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        
        // Simple pie chart placeholder
        ctx.fillStyle = '#28a745';
        ctx.beginPath();
        ctx.arc(200, 100, 80, 0, Math.PI * 1.7);
        ctx.lineTo(200, 100);
        ctx.fill();
        
        ctx.fillStyle = '#dc3545';
        ctx.beginPath();
        ctx.arc(200, 100, 80, Math.PI * 1.7, Math.PI * 2);
        ctx.lineTo(200, 100);
        ctx.fill();
        
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.fillText('Test results chart - Phase 4 implementation', 50, 180);
    }
});
</script>
{% endblock %}
