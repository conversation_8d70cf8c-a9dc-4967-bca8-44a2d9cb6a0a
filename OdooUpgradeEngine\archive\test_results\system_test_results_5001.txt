🎯 Testing Odoo Upgrade Engine at: http://localhost:5001
⏳ Please ensure the application is running...

🚀 Starting Comprehensive System Test
==================================================
❌ FAIL Basic Connectivity: HTTP 500

❌ FAIL Page Load: Dashboard: HTTP 500
✅ PASS Page Load: Upload Modules: Page loads correctly
✅ PASS Page Load: GitHub Integration: Page loads correctly
✅ PASS Page Load: Migration Jobs: Page loads correctly
✅ PASS Page Load: Migration Orchestrator: Page loads correctly
✅ PASS Page Load: Manual Interventions: Page loads correctly
✅ PASS Page Load: Analyze Modules: Page loads correctly
✅ PASS Page Load: Migration Results: Page loads correctly
✅ PASS Page Load: Review Queue: Page loads correctly
✅ PASS Page Load: Completed Migrations: Page loads correctly
✅ PASS Page Load: Success Reports: Page loads correctly
✅ PASS Page Load: Performance Analytics: Page loads correctly
✅ PASS Page Load: Migration History: Page loads correctly
✅ PASS Page Load: Testing Dashboard: Page loads correctly
✅ PASS Page Load: Docker Environments: Page loads correctly
✅ PASS Page Load: Test Results: Page loads correctly
✅ PASS Page Load: AI Providers: Page loads correctly
✅ PASS Page Load: AI Learning Dashboard: Page loads correctly
✅ PASS Page Load: Health Monitor: Page loads correctly
✅ PASS Page Load: System Settings: Page loads correctly
❌ FAIL All Pages Load: 19/20 pages loaded successfully

❌ FAIL API: Dashboard Data API: HTTP 500
❌ FAIL API: Migration Status API: HTTP 500
✅ PASS API: Migration Jobs API: API responds correctly
✅ PASS API: AI Learning Insights API: API responds correctly
❌ FAIL All APIs Work: 2/4 APIs working correctly

❌ FAIL AI Provider Test: deepseek: HTTP 500
❌ FAIL AI Provider Test: ollama: HTTP 500
❌ FAIL AI Provider Testing: 0/2 provider tests working

✅ PASS JavaScript: /static/js/main.js: File accessible
✅ PASS JavaScript: /static/js/ai-integration.js: File accessible
✅ PASS JavaScript: /static/js/real-time-updates.js: File accessible
✅ PASS JavaScript: /static/js/search-filter.js: File accessible
✅ PASS JavaScript Files: 4/4 JS files accessible

✅ PASS Database Connectivity: Database operations working

❌ FAIL Menu Navigation: HTTP 500

❌ FAIL Responsive Design: HTTP 500

==================================================
📊 COMPREHENSIVE TEST SUMMARY
==================================================
🕒 Test Duration: 0.30 seconds
📈 Overall Success Rate: 2/8 (25.0%)
🔍 Individual Tests: 27/38 (71.1%)

❌ FAILED TESTS:
   - Basic Connectivity: HTTP 500
   - Page Load: Dashboard: HTTP 500
   - All Pages Load: 19/20 pages loaded successfully
   - API: Dashboard Data API: HTTP 500
   - API: Migration Status API: HTTP 500
   - All APIs Work: 2/4 APIs working correctly
   - AI Provider Test: deepseek: HTTP 500
   - AI Provider Test: ollama: HTTP 500
   - AI Provider Testing: 0/2 provider tests working
   - Menu Navigation: HTTP 500
   - Responsive Design: HTTP 500

🚨 SYSTEM STATUS: NEEDS ATTENTION
❌ Major issues detected, system may not be ready

📄 Detailed report saved to: comprehensive_test_report.json
