"""
Setup Verification System

This module verifies that all components of the OdooUpgradeEngine
are properly configured and working together.
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import traceback

# Import our system components
try:
    from module_sync_manager import Mo<PERSON>leS<PERSON>Manager
    from hourly_scheduler import get_scheduler
    from automation_system import OdooModuleAutomationSystem
    from database_migration_engine import DatabaseMigrationEngine
    from app import db
    import git
except ImportError as e:
    print(f"Import error: {e}")

class SetupVerification:
    """Complete system setup verification"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "checking",
            "components": {},
            "recommendations": [],
            "critical_issues": [],
            "warnings": []
        }
        
    def verify_complete_setup(self) -> Dict[str, Any]:
        """Run complete setup verification"""
        print("🎯 Starting comprehensive OdooUpgradeEngine setup verification...")
        
        # Test each component
        self._verify_environment()
        self._verify_database()
        self._verify_github_integration()
        self._verify_sync_manager()
        self._verify_scheduler()
        self._verify_automation_system()
        self._verify_directories()
        self._verify_web_application()
        
        # Determine overall status
        critical_count = len(self.results["critical_issues"])
        warning_count = len(self.results["warnings"])
        
        if critical_count == 0:
            self.results["overall_status"] = "✅ READY FOR PRODUCTION"
        elif critical_count <= 2:
            self.results["overall_status"] = "⚠️ MOSTLY READY - FIX CRITICAL ISSUES"
        else:
            self.results["overall_status"] = "❌ NEEDS CONFIGURATION"
        
        self._generate_summary()
        return self.results
    
    def _verify_environment(self):
        """Verify environment variables and secrets"""
        print("🔧 Checking environment configuration...")
        
        status = {"status": "checking", "details": []}
        
        # Check required environment variables
        required_vars = ["DATABASE_URL", "GITHUB_TOKEN"]
        missing_vars = []
        
        for var in required_vars:
            if os.getenv(var):
                status["details"].append(f"✅ {var} configured")
            else:
                missing_vars.append(var)
                status["details"].append(f"❌ {var} missing")
        
        if missing_vars:
            self.results["critical_issues"].append(f"Missing environment variables: {', '.join(missing_vars)}")
            status["status"] = "error"
        else:
            status["status"] = "success"
            
        self.results["components"]["environment"] = status
    
    def _verify_database(self):
        """Verify database connectivity and schema"""
        print("🗄️ Checking database connection...")
        
        status = {"status": "checking", "details": []}
        
        try:
            # Test database connection
            with db.engine.connect() as conn:
                result = conn.execute(db.text("SELECT 1"))
                if result.fetchone():
                    status["details"].append("✅ Database connection successful")
                    
            # Check if tables exist
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            expected_tables = ["uploaded_module", "module_analysis", "odoo_installation"]
            missing_tables = [t for t in expected_tables if t not in tables]
            
            if missing_tables:
                status["details"].append(f"⚠️ Missing tables: {', '.join(missing_tables)}")
                self.results["warnings"].append(f"Database tables missing: {', '.join(missing_tables)}")
            else:
                status["details"].append("✅ All required database tables exist")
            
            status["status"] = "success"
            
        except Exception as e:
            status["status"] = "error"
            status["details"].append(f"❌ Database error: {str(e)}")
            self.results["critical_issues"].append(f"Database connection failed: {str(e)}")
        
        self.results["components"]["database"] = status
    
    def _verify_github_integration(self):
        """Verify GitHub token and repository access"""
        print("🐙 Checking GitHub integration...")
        
        status = {"status": "checking", "details": []}
        
        github_token = os.getenv("GITHUB_TOKEN")
        if not github_token:
            status["status"] = "error"
            status["details"].append("❌ GitHub token not configured")
            self.results["critical_issues"].append("GitHub token required for automation")
            self.results["components"]["github"] = status
            return
        
        try:
            # Test git operations
            import requests
            headers = {"Authorization": f"token {github_token}"}
            response = requests.get("https://api.github.com/user", headers=headers, timeout=10)
            
            if response.status_code == 200:
                user_data = response.json()
                status["details"].append(f"✅ GitHub authentication successful for user: {user_data.get('login', 'unknown')}")
                
                # Test repository access
                repo_url = "https://api.github.com/repos/yerenwgventures/OdooUpgradeEngine"
                repo_response = requests.get(repo_url, headers=headers, timeout=10)
                
                if repo_response.status_code == 200:
                    status["details"].append("✅ Repository access confirmed")
                    status["status"] = "success"
                elif repo_response.status_code == 404:
                    status["details"].append("⚠️ Repository not found - may need to be created")
                    self.results["warnings"].append("GitHub repository may need to be created")
                    status["status"] = "warning"
                else:
                    status["details"].append(f"⚠️ Repository access issue: {repo_response.status_code}")
                    self.results["warnings"].append("Repository access may be limited")
                    status["status"] = "warning"
            else:
                status["status"] = "error"
                status["details"].append(f"❌ GitHub authentication failed: {response.status_code}")
                self.results["critical_issues"].append("GitHub token authentication failed")
                
        except Exception as e:
            status["status"] = "error"
            status["details"].append(f"❌ GitHub integration error: {str(e)}")
            self.results["critical_issues"].append(f"GitHub integration failed: {str(e)}")
        
        self.results["components"]["github"] = status
    
    def _verify_sync_manager(self):
        """Verify module sync manager functionality"""
        print("🔄 Checking module sync manager...")
        
        status = {"status": "checking", "details": []}
        
        try:
            sync_manager = ModuleSyncManager()
            
            # Test configuration loading
            if hasattr(sync_manager, 'config'):
                status["details"].append("✅ Sync manager configuration loaded")
            
            # Test directory creation
            sync_manager._ensure_directories()
            status["details"].append("✅ Required directories verified")
            
            # Test sync status
            sync_status = sync_manager.get_sync_status()
            if isinstance(sync_status, dict):
                status["details"].append("✅ Sync status retrieval working")
                status["details"].append(f"📊 Upload modules: {sync_status.get('upload_dir_modules', 0)}")
            
            status["status"] = "success"
            
        except Exception as e:
            status["status"] = "error"
            status["details"].append(f"❌ Sync manager error: {str(e)}")
            self.results["critical_issues"].append(f"Module sync manager failed: {str(e)}")
        
        self.results["components"]["sync_manager"] = status
    
    def _verify_scheduler(self):
        """Verify hourly scheduler functionality"""
        print("⏰ Checking hourly scheduler...")
        
        status = {"status": "checking", "details": []}
        
        try:
            scheduler = get_scheduler()
            
            # Test scheduler status
            scheduler_status = scheduler.get_status()
            
            if scheduler_status.get("running"):
                status["details"].append("✅ Scheduler is running")
            else:
                status["details"].append("⚠️ Scheduler not running - will start automatically")
                self.results["warnings"].append("Scheduler not currently running")
            
            if scheduler_status.get("github_token_configured"):
                status["details"].append("✅ GitHub integration configured for scheduler")
            else:
                status["details"].append("⚠️ GitHub token not available to scheduler")
            
            next_run = scheduler_status.get("next_run")
            if next_run:
                status["details"].append(f"📅 Next scheduled run: {next_run}")
            
            status["status"] = "success"
            
        except Exception as e:
            status["status"] = "error"
            status["details"].append(f"❌ Scheduler error: {str(e)}")
            self.results["critical_issues"].append(f"Hourly scheduler failed: {str(e)}")
        
        self.results["components"]["scheduler"] = status
    
    def _verify_automation_system(self):
        """Verify automation system functionality"""
        print("🤖 Checking automation system...")
        
        status = {"status": "checking", "details": []}
        
        try:
            if os.getenv("GITHUB_TOKEN"):
                automation = OdooModuleAutomationSystem()
                
                # Test configuration
                if hasattr(automation, 'config'):
                    status["details"].append("✅ Automation system configuration loaded")
                
                # Test status report generation
                report = automation.generate_status_report()
                if isinstance(report, dict):
                    status["details"].append("✅ Status report generation working")
                    
                    # Add some key metrics
                    if "discovered_modules" in report:
                        total_modules = sum(len(modules) for modules in report["discovered_modules"].values())
                        status["details"].append(f"📊 Total modules discovered: {total_modules}")
                
                status["status"] = "success"
            else:
                status["status"] = "warning"
                status["details"].append("⚠️ Automation system requires GitHub token")
                self.results["warnings"].append("Automation system needs GitHub token to function fully")
                
        except Exception as e:
            status["status"] = "error"
            status["details"].append(f"❌ Automation system error: {str(e)}")
            self.results["critical_issues"].append(f"Automation system failed: {str(e)}")
        
        self.results["components"]["automation_system"] = status
    
    def _verify_directories(self):
        """Verify required directory structure"""
        print("📁 Checking directory structure...")
        
        status = {"status": "checking", "details": []}
        
        required_dirs = [
            "uploads",
            "automation_logs", 
            "automation_modules",
            "static",
            "templates",
            "config"
        ]
        
        missing_dirs = []
        created_dirs = []
        
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if not dir_path.exists():
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(dir_name)
                except Exception as e:
                    missing_dirs.append(f"{dir_name} ({str(e)})")
            else:
                status["details"].append(f"✅ {dir_name}/ exists")
        
        if created_dirs:
            status["details"].append(f"✅ Created missing directories: {', '.join(created_dirs)}")
        
        if missing_dirs:
            status["status"] = "error"
            status["details"].append(f"❌ Could not create: {', '.join(missing_dirs)}")
            self.results["critical_issues"].append(f"Directory creation failed: {', '.join(missing_dirs)}")
        else:
            status["status"] = "success"
        
        self.results["components"]["directories"] = status
    
    def _verify_web_application(self):
        """Verify web application components"""
        print("🌐 Checking web application...")
        
        status = {"status": "checking", "details": []}
        
        try:
            # Check if Flask app is properly configured
            from app import app
            
            if app:
                status["details"].append("✅ Flask application configured")
                
                # Check blueprints
                blueprint_names = [bp.name for bp in app.blueprints.values()]
                if "automation" in blueprint_names:
                    status["details"].append("✅ Automation blueprint registered")
                else:
                    status["details"].append("⚠️ Automation blueprint not found")
                    self.results["warnings"].append("Automation blueprint may not be registered")
                
                # Check template directory
                template_dir = Path("templates")
                if template_dir.exists():
                    templates = list(template_dir.glob("*.html"))
                    status["details"].append(f"✅ {len(templates)} templates found")
                else:
                    status["details"].append("⚠️ Templates directory missing")
                    self.results["warnings"].append("Templates directory not found")
            
            status["status"] = "success"
            
        except Exception as e:
            status["status"] = "error"
            status["details"].append(f"❌ Web application error: {str(e)}")
            self.results["critical_issues"].append(f"Web application verification failed: {str(e)}")
        
        self.results["components"]["web_application"] = status
    
    def _generate_summary(self):
        """Generate final summary and recommendations"""
        working_components = sum(1 for comp in self.results["components"].values() if comp["status"] == "success")
        total_components = len(self.results["components"])
        
        print(f"\n🎯 VERIFICATION COMPLETE")
        print(f"📊 Components working: {working_components}/{total_components}")
        print(f"⚠️ Critical issues: {len(self.results['critical_issues'])}")
        print(f"🔔 Warnings: {len(self.results['warnings'])}")
        
        # Generate recommendations
        if len(self.results["critical_issues"]) == 0:
            self.results["recommendations"].append("✅ System is ready for production use")
            self.results["recommendations"].append("🚀 You can start uploading modules and running automation")
            self.results["recommendations"].append("⏰ Hourly scheduler will automatically sync and process modules")
            
        if "GITHUB_TOKEN" in str(self.results["critical_issues"]):
            self.results["recommendations"].append("🔑 Configure GitHub token for full automation features")
            
        if any("database" in issue.lower() for issue in self.results["critical_issues"]):
            self.results["recommendations"].append("🗄️ Verify database configuration and connectivity")
            
        if len(self.results["warnings"]) > 0:
            self.results["recommendations"].append("⚠️ Review warnings for optimal performance")
    
    def save_verification_report(self):
        """Save verification report to file"""
        report_file = Path("automation_logs") / f"setup_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"📄 Verification report saved to: {report_file}")
        return report_file

def main():
    """Run complete setup verification"""
    print("🎯 OdooUpgradeEngine Setup Verification")
    print("=" * 50)
    
    verifier = SetupVerification()
    results = verifier.verify_complete_setup()
    
    # Save report
    report_file = verifier.save_verification_report()
    
    # Print summary
    print(f"\n🎯 FINAL STATUS: {results['overall_status']}")
    
    if results["recommendations"]:
        print("\n📋 RECOMMENDATIONS:")
        for rec in results["recommendations"]:
            print(f"  {rec}")
    
    if results["critical_issues"]:
        print("\n❌ CRITICAL ISSUES:")
        for issue in results["critical_issues"]:
            print(f"  {issue}")
    
    if results["warnings"]:
        print("\n⚠️ WARNINGS:")
        for warning in results["warnings"]:
            print(f"  {warning}")
    
    return results

if __name__ == "__main__":
    main()