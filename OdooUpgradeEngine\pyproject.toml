[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "astor>=0.8.1",
    "bandit>=1.8.5",
    "beautifulsoup4>=4.13.4",
    "docker>=7.1.0",
    "email-validator>=2.2.0",
    "flask>=3.1.1",
    "flask-sqlalchemy>=3.1.1",
    "gitpython>=3.1.44",
    "gunicorn>=23.0.0",
    "lxml>=6.0.0",
    "openai>=1.93.0",
    "packaging>=25.0",
    "psycopg2-binary>=2.9.10",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.41",
    "werkzeug>=3.1.3",
]
