// Main JavaScript for Odoo 18 Module Analyzer

// Initialize tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Bootstrap popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize file upload drag and drop
    initializeFileUpload();
    
    // Initialize auto-refresh for certain pages
    initializeAutoRefresh();
});

// File upload functionality
function initializeFileUpload() {
    const fileInput = document.getElementById('files');
    const uploadArea = document.querySelector('.file-upload-area');
    
    if (!fileInput || !uploadArea) return;

    // Drag and drop events
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        fileInput.files = files;
        
        // Trigger change event
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
    });

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
}

// Auto-refresh functionality
function initializeAutoRefresh() {
    // Only auto-refresh for specific pages with active processing
    // Look for specific indicators that processing is happening
    const isAnalyzingPage = document.querySelector('[data-status="analyzing"]');
    const isProcessingPage = document.querySelector('.processing-indicator');
    
    // Only refresh if we're on a page that's actively processing something
    if (isAnalyzingPage || isProcessingPage) {
        setTimeout(function() {
            location.reload();
        }, 8000);
    }
}

// Utility functions
function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}

// API calls
function getModuleStatus(moduleId) {
    return fetch(`/api/module_status/${moduleId}`)
        .then(response => response.json())
        .catch(error => {
            console.error('Error fetching module status:', error);
            return null;
        });
}

function analyzeModule(moduleId) {
    const button = document.querySelector(`[data-module-id="${moduleId}"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Analyzing...';
    }
    
    fetch(`/analyze_module/${moduleId}`)
        .then(response => {
            if (response.ok) {
                showToast('Module analysis started', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showToast('Failed to start analysis', 'danger');
            }
        })
        .catch(error => {
            console.error('Error starting analysis:', error);
            showToast('Error starting analysis', 'danger');
        })
        .finally(() => {
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-play me-1"></i>Analyze';
            }
        });
}

// Progress tracking
function updateProgress(elementId, progress, text = '') {
    const element = document.getElementById(elementId);
    if (element) {
        const progressBar = element.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
            if (text) {
                progressBar.textContent = text;
            }
        }
    }
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Copied to clipboard', 'success');
    }, function(err) {
        console.error('Could not copy text: ', err);
        showToast('Failed to copy to clipboard', 'danger');
    });
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Debounce function for search inputs
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        
        if (callNow) func.apply(context, args);
    };
}

// Search functionality
function initializeSearch(inputId, targetSelector) {
    const searchInput = document.getElementById(inputId);
    if (!searchInput) return;
    
    const debouncedSearch = debounce(function() {
        const query = searchInput.value.toLowerCase();
        const targets = document.querySelectorAll(targetSelector);
        
        targets.forEach(target => {
            const text = target.textContent.toLowerCase();
            const shouldShow = query === '' || text.includes(query);
            target.style.display = shouldShow ? '' : 'none';
        });
    }, 300);
    
    searchInput.addEventListener('input', debouncedSearch);
}

// Export functions for global use
window.ModuleAnalyzer = {
    showToast,
    getModuleStatus,
    analyzeModule,
    updateProgress,
    validateForm,
    copyToClipboard,
    formatFileSize,
    formatDate,
    initializeSearch
};
