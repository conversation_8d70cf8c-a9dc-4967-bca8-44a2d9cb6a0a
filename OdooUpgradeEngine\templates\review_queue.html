{% extends "base.html" %}
{% set title = "Review Queue" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-list-check me-3"></i>
            Review Queue
        </h1>
        <p class="lead">Unified queue of all items needing your attention</p>
    </div>
</div>

<!-- Queue Summary -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-clock text-warning fa-2x mb-2"></i>
                <h5 class="card-title">Pending Approvals</h5>
                <h3 class="text-warning">{{ pending_approvals|length }}</h3>
                <p class="text-muted">Awaiting your review</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-robot text-info fa-2x mb-2"></i>
                <h5 class="card-title">AI Suggestions</h5>
                <h3 class="text-info">3</h3>
                <p class="text-muted">AI recommendations available</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-2x mb-2"></i>
                <h5 class="card-title">Critical Issues</h5>
                <h3 class="text-danger">1</h3>
                <p class="text-muted">Require immediate attention</p>
            </div>
        </div>
    </div>
</div>

<!-- Pending Approvals -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-gavel me-2"></i>
            Pending Approvals
        </h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-success" onclick="approveAll()">
                <i class="fas fa-check-double"></i> Approve All
            </button>
            <button class="btn btn-outline-info" onclick="bulkAIAnalysis()">
                <i class="fas fa-robot"></i> AI Analyze All
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if pending_approvals %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Module</th>
                            <th>Migration</th>
                            <th>Priority</th>
                            <th>Waiting Since</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in pending_approvals %}
                        <tr>
                            <td>
                                <input type="checkbox" class="review-checkbox" value="{{ job.id }}">
                            </td>
                            <td>
                                <strong>{{ job.module.name }}</strong>
                                <br>
                                <small class="text-muted">{{ job.module.description or 'No description' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                <i class="fas fa-arrow-right mx-1"></i>
                                <span class="badge bg-primary">{{ job.target_version }}</span>
                            </td>
                            <td>
                                <span class="badge bg-warning">Medium</span>
                            </td>
                            <td>
                                <small class="text-muted">{{ job.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                                <br>
                                <small class="text-warning">2 hours ago</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="reviewMigration({{ job.id }})">
                                        <i class="fas fa-eye"></i> Review
                                    </button>
                                    <button class="btn btn-outline-success" onclick="approveMigration({{ job.id }})">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="rejectMigration({{ job.id }})">
                                        <i class="fas fa-times"></i> Reject
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-success">No pending approvals</h5>
                <p class="text-muted">All migrations are up to date</p>
                <a href="{{ url_for('main.migration_orchestrator') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Start New Migration
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- AI Suggestions -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-lightbulb me-2"></i>
            AI Suggestions
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card border-info">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-robot text-info"></i>
                            Performance Optimization
                        </h6>
                        <p class="card-text">AI detected 3 modules that could benefit from performance improvements.</p>
                        <button class="btn btn-sm btn-outline-info">View Details</button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-warning">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-shield-alt text-warning"></i>
                            Security Review
                        </h6>
                        <p class="card-text">Security scan suggests reviewing 2 modules for potential vulnerabilities.</p>
                        <button class="btn btn-sm btn-outline-warning">Review Security</button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-code text-success"></i>
                            Code Quality
                        </h6>
                        <p class="card-text">AI suggests code quality improvements for better maintainability.</p>
                        <button class="btn btn-sm btn-outline-success">Apply Suggestions</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-bolt me-2"></i>
            Quick Actions
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <button class="btn btn-outline-primary w-100 mb-2" onclick="reviewAll()">
                    <i class="fas fa-eye"></i><br>
                    Review All Pending
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-success w-100 mb-2" onclick="approveAll()">
                    <i class="fas fa-check-double"></i><br>
                    Approve All Low-Risk
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-info w-100 mb-2" onclick="bulkAIAnalysis()">
                    <i class="fas fa-robot"></i><br>
                    AI Analyze All
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-warning w-100 mb-2" onclick="exportQueue()">
                    <i class="fas fa-download"></i><br>
                    Export Queue
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.review-checkbox');
    checkboxes.forEach(cb => cb.checked = selectAll.checked);
}

function reviewMigration(jobId) {
    window.location.href = `/migration/${jobId}/review`;
}

function approveMigration(jobId) {
    if (confirm('Approve this migration?')) {
        fetch(`/migration/${jobId}/approve`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'}
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Migration approved successfully!');
                location.reload();
            } else {
                alert('Failed to approve migration: ' + data.error);
            }
        });
    }
}

function rejectMigration(jobId) {
    if (confirm('Reject this migration?')) {
        alert('Reject functionality will be implemented');
    }
}

function approveAll() {
    const selected = document.querySelectorAll('.review-checkbox:checked');
    if (selected.length === 0) {
        alert('Please select migrations to approve');
        return;
    }
    
    if (confirm(`Approve ${selected.length} selected migrations?`)) {
        alert('Bulk approve functionality will be implemented');
    }
}

function bulkAIAnalysis() {
    alert('Bulk AI analysis functionality will be implemented in Phase 1');
}

function reviewAll() {
    alert('Review all functionality will be implemented');
}

function exportQueue() {
    alert('Export queue functionality will be implemented');
}
</script>
{% endblock %}
