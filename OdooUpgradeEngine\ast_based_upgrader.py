"""
AST-Based Code Upgrader - Professional Python Code Transformation

This module uses Python's Abstract Syntax Tree (AST) to safely transform
Odoo module code, replacing the dangerous regex-based approach.
"""

import ast
import os
import sys
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import logging

class ApiOneRemover(ast.NodeTransformer):
    """
    AST transformer to safely remove @api.one decorators and rewrite methods.
    
    This provides surgical precision compared to regex-based replacement.
    """
    
    def __init__(self):
        self.changes_made = []
        self.logger = logging.getLogger(__name__)
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> ast.FunctionDef:
        """Visit function definitions to find and transform @api.one methods"""
        
        # Check if this function has @api.one decorator
        api_one_decorators = []
        other_decorators = []
        
        for decorator in node.decorator_list:
            if self._is_api_one_decorator(decorator):
                api_one_decorators.append(decorator)
                self.changes_made.append(f"Removed @api.one from {node.name}")
            else:
                other_decorators.append(decorator)
        
        if api_one_decorators:
            # Remove @api.one decorators
            node.decorator_list = other_decorators
            
            # Transform method body to loop over self
            new_body = self._transform_api_one_method(node)
            node.body = new_body
            
            self.logger.info(f"Transformed @api.one method: {node.name}")
        
        return self.generic_visit(node)
    
    def _is_api_one_decorator(self, decorator: ast.AST) -> bool:
        """Check if decorator is @api.one"""
        if isinstance(decorator, ast.Attribute):
            if (isinstance(decorator.value, ast.Name) and 
                decorator.value.id == 'api' and 
                decorator.attr == 'one'):
                return True
        return False
    
    def _transform_api_one_method(self, node: ast.FunctionDef) -> List[ast.stmt]:
        """Transform method body to loop over self records"""
        
        # Create: for record in self:
        loop_var = ast.Name(id='record', ctx=ast.Store())
        loop_iter = ast.Name(id='self', ctx=ast.Load())
        
        # Transform method body - replace self.field with record.field
        # but keep self.method_calls() intact
        transformed_body = []
        for stmt in node.body:
            transformed_stmt = SelfToRecordTransformer().visit(stmt)
            transformed_body.append(transformed_stmt)
        
        # Create the for loop
        for_loop = ast.For(
            target=loop_var,
            iter=loop_iter,
            body=transformed_body,
            orelse=[]
        )
        
        return [for_loop]

class SelfToRecordTransformer(ast.NodeTransformer):
    """
    Transformer to intelligently replace self.field with record.field
    while preserving self.method_calls()
    """
    
    def visit_Attribute(self, node: ast.Attribute) -> ast.Attribute:
        """Visit attribute access to transform self.field to record.field"""
        
        # Only transform if it's self.something
        if (isinstance(node.value, ast.Name) and 
            node.value.id == 'self'):
            
            # Check if this is a field access or method call
            parent = getattr(node, 'parent', None)
            
            # If parent is a Call node, this is likely a method call - keep as self
            if isinstance(parent, ast.Call):
                return node
            
            # If it's a common field name pattern, transform to record
            if self._is_likely_field(node.attr):
                node.value.id = 'record'
                return node
        
        return self.generic_visit(node)
    
    def _is_likely_field(self, attr_name: str) -> bool:
        """
        Determine if attribute is likely a field vs method.
        This uses heuristics - can be improved with Odoo field analysis.
        """
        # Common Odoo field patterns
        field_patterns = [
            'name', 'state', 'active', 'date', 'amount', 'quantity',
            'partner_id', 'user_id', 'company_id', 'currency_id',
            'description', 'notes', 'reference', 'sequence'
        ]
        
        # Common method patterns to preserve
        method_patterns = [
            'search', 'browse', 'create', 'write', 'unlink', 'copy',
            'read', 'exists', 'ensure_one', 'with_context', 'sudo',
            'mapped', 'filtered', 'sorted'
        ]
        
        if attr_name in method_patterns:
            return False
        
        if attr_name in field_patterns:
            return True
        
        # Default heuristic: if it ends with common field suffixes
        field_suffixes = ['_id', '_ids', '_date', '_datetime', '_state']
        if any(attr_name.endswith(suffix) for suffix in field_suffixes):
            return True
        
        return False

class ProfessionalASTUpgrader:
    """
    Professional AST-based upgrader for Odoo modules.
    
    This replaces the dangerous regex-based approach with surgical AST transformations.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.changes_made = []
    
    def upgrade_python_file(self, file_path: str) -> Dict[str, Any]:
        """
        Upgrade a single Python file using AST transformations.
        
        Returns:
            Dict with upgrade results and any issues found
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Parse the source code into AST
            tree = ast.parse(source_code)
            
            # Apply transformations
            tree = self._apply_transformations(tree)
            
            # Convert back to source code
            import astor  # For converting AST back to source
            new_source = astor.to_source(tree)
            
            # Write the upgraded file
            backup_path = file_path + '.backup'
            os.rename(file_path, backup_path)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_source)
            
            return {
                'success': True,
                'changes': self.changes_made,
                'backup_created': backup_path
            }
            
        except Exception as e:
            self.logger.error(f"Failed to upgrade {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'changes': []
            }
    
    def _apply_transformations(self, tree: ast.AST) -> ast.AST:
        """Apply all AST transformations"""
        
        # Remove @api.one decorators
        api_one_remover = ApiOneRemover()
        tree = api_one_remover.visit(tree)
        self.changes_made.extend(api_one_remover.changes_made)
        
        # Add more transformations here as needed
        # - @api.multi removal
        # - Import path updates
        # - Field parameter fixes
        
        return tree
    
    def upgrade_module(self, module_path: str) -> Dict[str, Any]:
        """
        Upgrade entire module using AST-based transformations.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            Comprehensive upgrade report
        """
        module_path = Path(module_path)
        results = {
            'module_name': module_path.name,
            'files_processed': 0,
            'files_upgraded': 0,
            'errors': [],
            'changes_summary': []
        }
        
        # Find all Python files
        python_files = list(module_path.glob('**/*.py'))
        
        for py_file in python_files:
            results['files_processed'] += 1
            
            file_result = self.upgrade_python_file(str(py_file))
            
            if file_result['success']:
                results['files_upgraded'] += 1
                results['changes_summary'].extend(file_result['changes'])
            else:
                results['errors'].append({
                    'file': str(py_file),
                    'error': file_result['error']
                })
        
        return results

def main():
    """Test the AST-based upgrader"""
    if len(sys.argv) < 2:
        print("Usage: python ast_based_upgrader.py <module_path>")
        sys.exit(1)
    
    module_path = sys.argv[1]
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create upgrader and run
    upgrader = ProfessionalASTUpgrader()
    results = upgrader.upgrade_module(module_path)
    
    print(f"Upgrade Results for {results['module_name']}:")
    print(f"Files processed: {results['files_processed']}")
    print(f"Files upgraded: {results['files_upgraded']}")
    print(f"Errors: {len(results['errors'])}")
    
    if results['changes_summary']:
        print("\nChanges made:")
        for change in results['changes_summary']:
            print(f"  - {change}")
    
    if results['errors']:
        print("\nErrors:")
        for error in results['errors']:
            print(f"  - {error['file']}: {error['error']}")

if __name__ == "__main__":
    main()