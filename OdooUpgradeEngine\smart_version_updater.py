"""
Smart Version Updater for Odoo Module Manifests

This module provides intelligent version detection and updating for Odoo module
manifests, handling various version formats and edge cases that the standard
migration rules miss.

Key Features:
- Detects source version from manifest content
- Handles both single and double quotes
- Supports non-standard version formats (e.g., '1.0', '2.5.1')
- Updates to proper Odoo version format (e.g., '17.0.1.0.0')
- Preserves manifest structure and formatting
- Provides detailed logging of version changes
"""

import os
import re
import ast
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime


class SmartVersionUpdater:
    """
    Intelligent version updater for Odoo module manifests that handles
    various version formats and edge cases.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Standard Odoo version format patterns
        self.odoo_version_patterns = [
            r'"version":\s*"(\d+\.\d+(?:\.\d+)*)"',  # Double quotes
            r"'version':\s*'(\d+\.\d+(?:\.\d+)*)'",  # Single quotes
            r'"version":\s*\'(\d+\.\d+(?:\.\d+)*)\'', # Mixed quotes
            r"'version':\s*\"(\d+\.\d+(?:\.\d+)*)\""  # Mixed quotes reverse
        ]
        
        # Non-standard version patterns (simple versions like '1.0', '2.5.1')
        self.simple_version_patterns = [
            r'"version":\s*"(\d+(?:\.\d+)*)"',
            r"'version':\s*'(\d+(?:\.\d+)*)'",
            r'"version":\s*\'(\d+(?:\.\d+)*)\'',
            r"'version':\s*\"(\d+(?:\.\d+)*)\""
        ]
    
    def detect_and_update_version(self, manifest_path: str, target_version: str) -> Dict[str, Any]:
        """
        Detect current version and update to target version.
        
        Args:
            manifest_path: Path to __manifest__.py file
            target_version: Target Odoo version (e.g., '17.0')
            
        Returns:
            Dictionary with update results and details
        """
        if not os.path.exists(manifest_path):
            return {
                'success': False,
                'error': f'Manifest file not found: {manifest_path}',
                'original_version': None,
                'updated_version': None
            }
        
        try:
            # Read original content
            with open(manifest_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Detect current version
            detection_result = self._detect_current_version(original_content)
            
            if not detection_result['found']:
                return {
                    'success': False,
                    'error': 'Could not detect version in manifest',
                    'original_version': None,
                    'updated_version': None,
                    'original_content': original_content
                }
            
            # Generate new version string
            new_version = self._generate_target_version(target_version)
            
            # Update version in content
            updated_content = self._update_version_in_content(
                original_content,
                detection_result,
                new_version
            )
            
            # Write updated content
            with open(manifest_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            self.logger.info(f"Updated version: {detection_result['version']} → {new_version}")
            
            return {
                'success': True,
                'original_version': detection_result['version'],
                'updated_version': new_version,
                'pattern_used': detection_result['pattern'],
                'quote_style': detection_result['quote_style'],
                'changes_made': 1
            }
            
        except Exception as e:
            self.logger.error(f"Error updating version in {manifest_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'original_version': None,
                'updated_version': None
            }
    
    def _detect_current_version(self, content: str) -> Dict[str, Any]:
        """
        Detect current version from manifest content using multiple patterns.
        """
        # Try standard Odoo patterns first
        for pattern in self.odoo_version_patterns:
            match = re.search(pattern, content)
            if match:
                version = match.group(1)
                quote_style = self._determine_quote_style(match.group(0))
                return {
                    'found': True,
                    'version': version,
                    'pattern': pattern,
                    'quote_style': quote_style,
                    'match_text': match.group(0),
                    'is_standard': True
                }
        
        # Try simple version patterns
        for pattern in self.simple_version_patterns:
            match = re.search(pattern, content)
            if match:
                version = match.group(1)
                quote_style = self._determine_quote_style(match.group(0))
                return {
                    'found': True,
                    'version': version,
                    'pattern': pattern,
                    'quote_style': quote_style,
                    'match_text': match.group(0),
                    'is_standard': False
                }
        
        return {'found': False}
    
    def _determine_quote_style(self, match_text: str) -> str:
        """Determine if single or double quotes are used."""
        if '"version":' in match_text and match_text.count('"') >= 4:
            return 'double'
        elif "'version':" in match_text and match_text.count("'") >= 4:
            return 'single'
        elif '"version":' in match_text and "'" in match_text:
            return 'mixed_double_key'
        elif "'version':" in match_text and '"' in match_text:
            return 'mixed_single_key'
        else:
            return 'double'  # Default
    
    def _generate_target_version(self, target_version: str) -> str:
        """
        Generate proper target version string.
        
        Args:
            target_version: Target version like '17.0'
            
        Returns:
            Proper version string like '17.0.1.0.0'
        """
        # Ensure we have major.minor format
        if '.' not in target_version:
            target_version = f"{target_version}.0"
        
        # Add standard Odoo version suffix
        parts = target_version.split('.')
        if len(parts) == 2:  # e.g., '17.0'
            return f"{target_version}.1.0.0"
        elif len(parts) >= 3:  # Already has more parts
            return target_version
        else:
            return f"{target_version}.1.0.0"
    
    def _update_version_in_content(self, content: str, detection_result: Dict, new_version: str) -> str:
        """
        Update version in content preserving original quote style.
        """
        old_match = detection_result['match_text']
        quote_style = detection_result['quote_style']
        
        # Generate replacement based on quote style
        if quote_style == 'double':
            new_match = f'"version": "{new_version}"'
        elif quote_style == 'single':
            new_match = f"'version': '{new_version}'"
        elif quote_style == 'mixed_double_key':
            new_match = f'"version": \'{new_version}\''
        elif quote_style == 'mixed_single_key':
            new_match = f"'version': \"{new_version}\""
        else:
            new_match = f'"version": "{new_version}"'  # Default
        
        # Replace in content
        updated_content = content.replace(old_match, new_match)
        
        return updated_content
    
    def validate_version_update(self, manifest_path: str, expected_version: str) -> Dict[str, Any]:
        """
        Validate that version was updated correctly.
        """
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            detection_result = self._detect_current_version(content)
            
            if detection_result['found']:
                current_version = detection_result['version']
                is_correct = current_version.startswith(expected_version)
                
                return {
                    'success': True,
                    'current_version': current_version,
                    'expected_version': expected_version,
                    'is_correct': is_correct
                }
            else:
                return {
                    'success': False,
                    'error': 'Could not detect version after update'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
