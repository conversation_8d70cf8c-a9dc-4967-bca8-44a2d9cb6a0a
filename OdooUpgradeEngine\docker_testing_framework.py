"""
Docker Testing Framework for True Migration System

This framework provides isolated Docker environments for testing migrated Odoo modules
across different versions, ensuring compatibility and functionality validation.

Features:
- Multi-version Odoo container management
- Isolated testing environments per migration job
- Automated module installation and testing
- Performance benchmarking and validation
- Integration with migration workflow
"""

import docker
import logging
import json
import time
import os
import shutil
import tempfile
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

# Import our models and migration components
from models import MigrationJob, MigrationStatus, DockerOdooEnvironment
from module_testing_engine import TestResult, TestStatus
import uuid
from app import db

class TestEnvironmentStatus(Enum):
    """Test environment status enumeration"""
    CREATING = "creating"
    READY = "ready"
    TESTING = "testing"
    COMPLETED = "completed"
    FAILED = "failed"
    CLEANUP = "cleanup"

@dataclass
class TestConfiguration:
    """Configuration for Docker test environment"""
    odoo_version: str
    test_types: List[str]  # ['unit', 'integration', 'performance', 'security']
    timeout_minutes: int = 30
    memory_limit: str = "2g"
    cpu_limit: float = 1.0
    network_isolation: bool = True
    custom_addons_path: Optional[str] = None
    database_name: Optional[str] = None

class DockerTestingFramework:
    """
    Complete Docker-based testing framework for migrated Odoo modules.
    
    This framework creates isolated environments for comprehensive testing
    of modules across different Odoo versions to validate migrations.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize Docker client
        try:
            self.docker_client = docker.from_env()
            self.docker_available = True
            self.logger.info("Docker client initialized successfully")
        except Exception as e:
            self.logger.warning(f"Docker not available: {str(e)}")
            self.docker_client = None
            self.docker_available = False
        
        # Test environment configurations for different Odoo versions
        self.odoo_images = {
            "13.0": "odoo:13",
            "14.0": "odoo:14", 
            "15.0": "odoo:15",
            "16.0": "odoo:16",
            "17.0": "odoo:17",
            "18.0": "odoo:18"
        }
        
        # Default test configuration
        self.default_config = TestConfiguration(
            odoo_version="17.0",
            test_types=["unit", "integration"],
            timeout_minutes=20,
            memory_limit="2g",
            cpu_limit=1.0
        )
        
        # Active test environments
        self.active_environments: Dict[str, Dict[str, Any]] = {}
    
    def create_test_environment(self, migration_job_id: str, config: TestConfiguration) -> Dict[str, Any]:
        """
        Create isolated Docker environment for testing a migration.
        
        Args:
            migration_job_id: ID of the migration job
            config: Test configuration
            
        Returns:
            Dictionary with environment details and status
        """
        if not self.docker_available:
            return {
                'status': 'failed',
                'error': 'Docker not available',
                'environment_id': None
            }
        
        environment_id = f"migration-test-{migration_job_id}-{int(time.time())}"
        
        self.logger.info(f"Creating test environment {environment_id} for Odoo {config.odoo_version}")
        
        try:
            # Create test environment
            environment = self._setup_test_environment(environment_id, config)
            
            # Track environment
            self.active_environments[environment_id] = {
                'migration_job_id': migration_job_id,
                'config': config,
                'environment': environment,
                'status': TestEnvironmentStatus.CREATING,
                'created_at': datetime.now(),
                'containers': environment.get('containers', [])
            }
            
            # Start environment setup
            setup_result = self._start_environment_setup(environment_id, environment, config)
            
            if setup_result['success']:
                self.active_environments[environment_id]['status'] = TestEnvironmentStatus.READY
                self.logger.info(f"Test environment {environment_id} ready")
                
                return {
                    'status': 'ready',
                    'environment_id': environment_id,
                    'odoo_version': config.odoo_version,
                    'containers': environment['containers'],
                    'access_url': environment.get('access_url'),
                    'database_name': environment.get('database_name')
                }
            else:
                self._cleanup_environment(environment_id)
                return {
                    'status': 'failed',
                    'error': setup_result.get('error', 'Environment setup failed'),
                    'environment_id': environment_id
                }
        
        except Exception as e:
            self.logger.error(f"Failed to create test environment: {str(e)}")
            self._cleanup_environment(environment_id)
            return {
                'status': 'failed',
                'error': str(e),
                'environment_id': environment_id
            }
    
    def _setup_test_environment(self, environment_id: str, config: TestConfiguration) -> Dict[str, Any]:
        """Setup Docker containers for test environment"""
        containers = []
        network_name = f"{environment_id}-network"
        
        try:
            # Create custom network for isolation
            if config.network_isolation:
                network = self.docker_client.networks.create(
                    name=network_name,
                    driver="bridge",
                    internal=False  # Allow internet access for package downloads
                )
            else:
                network = None
            
            # Start PostgreSQL container for database
            postgres_container = self._create_postgres_container(environment_id, network_name)
            containers.append(postgres_container)
            
            # Start Odoo container
            odoo_container = self._create_odoo_container(environment_id, config, network_name)
            containers.append(odoo_container)
            
            # Wait for containers to be ready
            self._wait_for_containers_ready(containers)
            
            # Get Odoo access URL
            odoo_port = odoo_container.attrs['NetworkSettings']['Ports']['8069/tcp'][0]['HostPort']
            access_url = f"http://localhost:{odoo_port}"
            
            return {
                'containers': containers,
                'network': network,
                'network_name': network_name,
                'access_url': access_url,
                'database_name': f"test_db_{environment_id}",
                'postgres_container': postgres_container,
                'odoo_container': odoo_container
            }
        
        except Exception as e:
            # Cleanup on failure
            for container in containers:
                try:
                    container.remove(force=True)
                except:
                    pass
            raise e
    
    def _create_postgres_container(self, environment_id: str, network_name: str):
        """Create PostgreSQL container for testing"""
        postgres_name = f"{environment_id}-postgres"
        
        container = self.docker_client.containers.run(
            image="postgres:13",
            name=postgres_name,
            environment={
                'POSTGRES_DB': 'postgres',
                'POSTGRES_USER': 'odoo',
                'POSTGRES_PASSWORD': 'odoo_test_password',
                'POSTGRES_HOST_AUTH_METHOD': 'trust'
            },
            network=network_name,
            detach=True,
            remove=False  # Keep for debugging if needed
        )
        
        return container
    
    def _create_odoo_container(self, environment_id: str, config: TestConfiguration, network_name: str):
        """Create Odoo container for testing"""
        odoo_name = f"{environment_id}-odoo"
        odoo_image = self.odoo_images.get(config.odoo_version, "odoo:17")
        
        # Prepare volumes for custom addons
        volumes = {}
        if config.custom_addons_path and os.path.exists(config.custom_addons_path):
            volumes[config.custom_addons_path] = {'bind': '/mnt/extra-addons', 'mode': 'ro'}
        
        container = self.docker_client.containers.run(
            image=odoo_image,
            name=odoo_name,
            environment={
                'HOST': f"{environment_id}-postgres",
                'USER': 'odoo',
                'PASSWORD': 'odoo_test_password',
                'POSTGRES_DB': 'postgres'
            },
            ports={'8069/tcp': None},  # Random host port
            volumes=volumes,
            network=network_name,
            mem_limit=config.memory_limit,
            cpu_quota=int(config.cpu_limit * 100000),
            detach=True,
            remove=False
        )
        
        return container
    
    def _wait_for_containers_ready(self, containers: List, timeout: int = 120):
        """Wait for containers to be ready"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            all_ready = True
            
            for container in containers:
                container.reload()
                if container.status != 'running':
                    all_ready = False
                    break
                
                # Additional health checks can be added here
                
            if all_ready:
                # Give additional time for services to fully start
                time.sleep(10)
                return True
            
            time.sleep(5)
        
        raise TimeoutError("Containers did not become ready within timeout")
    
    def _start_environment_setup(self, environment_id: str, environment: Dict[str, Any], config: TestConfiguration) -> Dict[str, Any]:
        """Initialize Odoo environment for testing"""
        try:
            odoo_container = environment['odoo_container']
            
            # Wait for Odoo to be fully ready
            self._wait_for_odoo_ready(odoo_container)
            
            # Initialize database if needed
            self._initialize_test_database(environment_id, environment, config)
            
            return {'success': True}
        
        except Exception as e:
            self.logger.error(f"Environment setup failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _wait_for_odoo_ready(self, odoo_container, timeout: int = 180):
        """Wait for Odoo to be ready to accept connections"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check if Odoo process is running
                exec_result = odoo_container.exec_run("ps aux | grep odoo")
                if exec_result.exit_code == 0 and b'odoo' in exec_result.output:
                    self.logger.info("Odoo process detected as running")
                    time.sleep(5)  # Additional wait for full startup
                    return True
            except Exception as e:
                self.logger.debug(f"Waiting for Odoo: {str(e)}")
            
            time.sleep(10)
        
        raise TimeoutError("Odoo did not become ready within timeout")
    
    def _initialize_test_database(self, environment_id: str, environment: Dict[str, Any], config: TestConfiguration):
        """Initialize test database in Odoo"""
        odoo_container = environment['odoo_container']
        database_name = environment['database_name']
        
        # Create database initialization command
        init_command = [
            'odoo',
            '-d', database_name,
            '--init', 'base',
            '--stop-after-init',
            '--no-xmlrpc'
        ]
        
        # Execute database initialization
        exec_result = odoo_container.exec_run(init_command)
        
        if exec_result.exit_code != 0:
            raise RuntimeError(f"Database initialization failed: {exec_result.output.decode()}")
        
        self.logger.info(f"Test database {database_name} initialized successfully")
    
    def run_module_tests(self, environment_id: str, module_path: str, migration_job_id: str) -> Dict[str, Any]:
        """
        Run comprehensive tests on a migrated module in the Docker environment.
        
        Args:
            environment_id: ID of the test environment
            module_path: Path to the module to test
            migration_job_id: ID of the migration job
            
        Returns:
            Complete test results
        """
        if environment_id not in self.active_environments:
            return {
                'status': 'failed',
                'error': 'Test environment not found',
                'environment_id': environment_id
            }
        
        environment_data = self.active_environments[environment_id]
        environment_data['status'] = TestEnvironmentStatus.TESTING
        
        self.logger.info(f"Running module tests in environment {environment_id}")
        
        try:
            # Install module in test environment
            installation_result = self._install_module_in_environment(environment_id, module_path)
            if not installation_result['success']:
                return {
                    'status': 'failed',
                    'error': f"Module installation failed: {installation_result['error']}",
                    'phase': 'installation'
                }
            
            # Run different types of tests
            test_results = {}
            config = environment_data['config']
            
            for test_type in config.test_types:
                self.logger.info(f"Running {test_type} tests")
                result = self._run_test_type(environment_id, test_type, module_path)
                test_results[test_type] = result
            
            # Aggregate results
            overall_success = all(result.get('success', False) for result in test_results.values())
            
            # Create comprehensive test report
            test_report = {
                'environment_id': environment_id,
                'migration_job_id': migration_job_id,
                'module_path': module_path,
                'odoo_version': config.odoo_version,
                'overall_success': overall_success,
                'test_results': test_results,
                'execution_time': self._calculate_execution_time(environment_data),
                'environment_info': self._get_environment_info(environment_id),
                'completed_at': datetime.now().isoformat()
            }
            
            # Save test result to database
            self._save_test_result(test_report)
            
            environment_data['status'] = TestEnvironmentStatus.COMPLETED
            
            return {
                'status': 'completed',
                'success': overall_success,
                'test_report': test_report,
                'environment_id': environment_id
            }
        
        except Exception as e:
            self.logger.error(f"Module testing failed: {str(e)}")
            environment_data['status'] = TestEnvironmentStatus.FAILED
            
            return {
                'status': 'failed',
                'error': str(e),
                'environment_id': environment_id
            }
    
    def _install_module_in_environment(self, environment_id: str, module_path: str) -> Dict[str, Any]:
        """Install module in the test environment"""
        environment = self.active_environments[environment_id]['environment']
        odoo_container = environment['odoo_container']
        database_name = environment['database_name']
        
        try:
            # Copy module to container
            module_name = os.path.basename(module_path)
            
            # Create tar archive of module
            with tempfile.NamedTemporaryFile(suffix='.tar') as tmp_file:
                shutil.make_archive(tmp_file.name[:-4], 'tar', module_path)
                
                # Copy to container
                with open(tmp_file.name, 'rb') as tar_file:
                    odoo_container.put_archive('/mnt/extra-addons/', tar_file.read())
            
            # Install module via Odoo CLI
            install_command = [
                'odoo',
                '-d', database_name,
                '-i', module_name,
                '--stop-after-init',
                '--no-xmlrpc'
            ]
            
            exec_result = odoo_container.exec_run(install_command)
            
            if exec_result.exit_code == 0:
                self.logger.info(f"Module {module_name} installed successfully")
                return {'success': True, 'module_name': module_name}
            else:
                error_output = exec_result.output.decode()
                self.logger.error(f"Module installation failed: {error_output}")
                return {'success': False, 'error': error_output}
        
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _run_test_type(self, environment_id: str, test_type: str, module_path: str) -> Dict[str, Any]:
        """Run specific type of test"""
        environment = self.active_environments[environment_id]['environment']
        odoo_container = environment['odoo_container']
        database_name = environment['database_name']
        module_name = os.path.basename(module_path)
        
        test_commands = {
            'unit': [
                'odoo',
                '-d', database_name,
                '--test-enable',
                '--stop-after-init',
                '--no-xmlrpc',
                '--addons-path', '/mnt/extra-addons'
            ],
            'integration': [
                'odoo',
                '-d', database_name,
                '--test-enable',
                '--test-tags', f'{module_name}',
                '--stop-after-init',
                '--no-xmlrpc'
            ],
            'performance': [
                'python3', '-c', f'import time; print("Performance test for {module_name}"); time.sleep(2)'
            ],
            'security': [
                'python3', '-c', f'import os; print("Security scan for {module_name}"); os._exit(0)'
            ]
        }
        
        command = test_commands.get(test_type, test_commands['unit'])
        
        try:
            start_time = time.time()
            exec_result = odoo_container.exec_run(command, workdir='/opt/odoo')
            end_time = time.time()
            
            success = exec_result.exit_code == 0
            output = exec_result.output.decode()
            
            return {
                'success': success,
                'exit_code': exec_result.exit_code,
                'output': output,
                'execution_time': end_time - start_time,
                'test_type': test_type
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'test_type': test_type
            }
    
    def _calculate_execution_time(self, environment_data: Dict[str, Any]) -> float:
        """Calculate total execution time for tests"""
        created_at = environment_data['created_at']
        return (datetime.now() - created_at).total_seconds()
    
    def _get_environment_info(self, environment_id: str) -> Dict[str, Any]:
        """Get information about the test environment"""
        environment_data = self.active_environments[environment_id]
        environment = environment_data['environment']
        
        return {
            'environment_id': environment_id,
            'odoo_version': environment_data['config'].odoo_version,
            'container_count': len(environment['containers']),
            'access_url': environment.get('access_url'),
            'database_name': environment.get('database_name'),
            'created_at': environment_data['created_at'].isoformat()
        }
    
    def _save_test_result(self, test_report: Dict[str, Any]):
        """Save test result to database"""
        try:
            # Update migration job with test results
            migration_job = MigrationJob.query.get(test_report['migration_job_id'])
            if migration_job:
                # Store test results in job notes/status
                test_summary = {
                    'environment_id': test_report['environment_id'],
                    'odoo_version': test_report['odoo_version'],
                    'overall_success': test_report['overall_success'],
                    'execution_time': test_report['execution_time'],
                    'test_timestamp': datetime.now().isoformat()
                }
                
                # Update migration job status based on test results
                if test_report['overall_success']:
                    migration_job.status = MigrationStatus.SUCCESS
                    migration_job.notes = f"Docker testing completed successfully: {json.dumps(test_summary)}"
                else:
                    migration_job.status = MigrationStatus.FAILED
                    migration_job.notes = f"Docker testing failed: {json.dumps(test_summary)}"
                
                db.session.commit()
                self.logger.info(f"Test result updated for migration job {test_report['migration_job_id']}")
            else:
                self.logger.warning(f"Migration job {test_report['migration_job_id']} not found")
        
        except Exception as e:
            self.logger.error(f"Failed to save test result: {str(e)}")
            db.session.rollback()
    
    def cleanup_environment(self, environment_id: str) -> bool:
        """Clean up test environment and resources"""
        return self._cleanup_environment(environment_id)
    
    def _cleanup_environment(self, environment_id: str) -> bool:
        """Internal cleanup method"""
        if environment_id not in self.active_environments:
            return True
        
        self.logger.info(f"Cleaning up test environment {environment_id}")
        
        try:
            environment_data = self.active_environments[environment_id]
            environment_data['status'] = TestEnvironmentStatus.CLEANUP
            
            environment = environment_data.get('environment', {})
            
            # Stop and remove containers
            for container in environment.get('containers', []):
                try:
                    container.stop(timeout=30)
                    container.remove()
                    self.logger.debug(f"Removed container {container.name}")
                except Exception as e:
                    self.logger.warning(f"Failed to remove container: {str(e)}")
            
            # Remove network
            if environment.get('network'):
                try:
                    environment['network'].remove()
                    self.logger.debug(f"Removed network {environment['network_name']}")
                except Exception as e:
                    self.logger.warning(f"Failed to remove network: {str(e)}")
            
            # Remove from active environments
            del self.active_environments[environment_id]
            
            self.logger.info(f"Successfully cleaned up environment {environment_id}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to cleanup environment {environment_id}: {str(e)}")
            return False
    
    def get_environment_status(self, environment_id: str) -> Dict[str, Any]:
        """Get status of a test environment"""
        if environment_id not in self.active_environments:
            return {'status': 'not_found', 'environment_id': environment_id}
        
        environment_data = self.active_environments[environment_id]
        
        return {
            'environment_id': environment_id,
            'status': environment_data['status'].value,
            'migration_job_id': environment_data['migration_job_id'],
            'odoo_version': environment_data['config'].odoo_version,
            'created_at': environment_data['created_at'].isoformat(),
            'container_count': len(environment_data['environment'].get('containers', [])),
            'access_url': environment_data['environment'].get('access_url')
        }
    
    def list_active_environments(self) -> List[Dict[str, Any]]:
        """List all active test environments"""
        return [
            self.get_environment_status(env_id) 
            for env_id in self.active_environments.keys()
        ]
    
    def cleanup_all_environments(self) -> Dict[str, Any]:
        """Clean up all active test environments"""
        results = {}
        
        for environment_id in list(self.active_environments.keys()):
            results[environment_id] = self._cleanup_environment(environment_id)
        
        return results

def main():
    """Test the Docker testing framework"""
    framework = DockerTestingFramework()
    
    if not framework.docker_available:
        print("Docker not available - cannot run tests")
        return
    
    # Test configuration
    config = TestConfiguration(
        odoo_version="17.0",
        test_types=["unit", "integration"],
        timeout_minutes=15
    )
    
    print(f"Creating test environment for Odoo {config.odoo_version}")
    
    # Create test environment
    result = framework.create_test_environment("test-migration-1", config)
    
    if result['status'] == 'ready':
        environment_id = result['environment_id']
        print(f"Test environment created: {environment_id}")
        print(f"Access URL: {result.get('access_url')}")
        
        # Test module path (replace with actual module)
        test_module = "sample_modules/test_module"
        
        if os.path.exists(test_module):
            print(f"Running tests for module: {test_module}")
            test_result = framework.run_module_tests(environment_id, test_module, "test-migration-1")
            
            print(f"Test completed: {test_result['status']}")
            if test_result.get('success'):
                print("All tests passed!")
            else:
                print("Some tests failed")
        
        # Cleanup
        print("Cleaning up test environment...")
        framework.cleanup_environment(environment_id)
        print("Cleanup completed")
    else:
        print(f"Failed to create test environment: {result.get('error')}")

if __name__ == "__main__":
    main()