{% extends "base.html" %}
{% set active_page = "automation-logs" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Automation Logs</h2>
                    <p class="text-muted mb-0">View and monitor automation system logs</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="{{ url_for('automation.automation_dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Total Log Files</h5>
                    <h3 class="text-primary">{{ log_files|length }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Recent Entries</h5>
                    <h3 class="text-info">156</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Error Count</h5>
                    <h3 class="text-warning">3</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Success Rate</h5>
                    <h3 class="text-success">98.1%</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Files List -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Log Files</h5>
                </div>
                <div class="card-body">
                    {% if log_files %}
                        <div class="list-group">
                            {% for log_file in log_files %}
                            <a href="#" class="list-group-item list-group-item-action" onclick="loadLogContent('{{ log_file.name }}')">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ log_file.name }}</h6>
                                    <small>{{ log_file.size }}</small>
                                </div>
                                <p class="mb-1">{{ log_file.description }}</p>
                                <small>Modified: {{ log_file.modified }}</small>
                            </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>No log files found</p>
                            <small>Log files will appear here when automation processes run</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Log Content</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="downloadLog()">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="clearLog()">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="log-content" style="height: 500px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px;">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-mouse-pointer fa-2x mb-3"></i>
                            <p>Select a log file from the left panel to view its content</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Log Stream -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Live Log Stream</h5>
                    <div>
                        <button class="btn btn-sm btn-success" id="stream-toggle" onclick="toggleLogStream()">
                            <i class="fas fa-play"></i> Start Stream
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="live-log-stream" style="height: 200px; overflow-y: auto; background-color: #000; color: #00ff00; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                        <div class="text-center" style="color: #666;">
                            <p>Live log stream is stopped. Click "Start Stream" to begin monitoring.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentLogFile = null;
let streamActive = false;
let streamInterval = null;

function refreshLogs() {
    location.reload();
}

function loadLogContent(logFileName) {
    currentLogFile = logFileName;
    const logContent = document.getElementById('log-content');
    
    // Show loading
    logContent.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading ${logFileName}...</p>
        </div>
    `;
    
    // Simulate loading log content (in production, this would fetch from server)
    setTimeout(() => {
        const mockLogContent = generateMockLogContent(logFileName);
        logContent.innerHTML = `<pre style="margin: 0; white-space: pre-wrap;">${mockLogContent}</pre>`;
        logContent.scrollTop = logContent.scrollHeight;
    }, 1000);
}

function generateMockLogContent(fileName) {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] INFO: Starting automation process for ${fileName}
[${timestamp}] INFO: Loading configuration...
[${timestamp}] INFO: Connecting to database...
[${timestamp}] INFO: Processing migration jobs...
[${timestamp}] INFO: Found 3 pending jobs
[${timestamp}] INFO: Starting job 1: test_sales_module
[${timestamp}] INFO: Analysis phase completed successfully
[${timestamp}] INFO: Code transformation phase started
[${timestamp}] INFO: Applying AST transformations...
[${timestamp}] INFO: XML view updates completed
[${timestamp}] INFO: Job 1 completed successfully
[${timestamp}] INFO: Starting job 2: test_inventory_module
[${timestamp}] WARNING: Deprecated API usage detected
[${timestamp}] INFO: Applying compatibility fixes...
[${timestamp}] INFO: Job 2 completed with warnings
[${timestamp}] INFO: All jobs processed successfully
[${timestamp}] INFO: Automation cycle completed`;
}

function downloadLog() {
    if (!currentLogFile) {
        alert('Please select a log file first');
        return;
    }
    
    // In production, this would trigger actual file download
    alert(`Downloading ${currentLogFile}...`);
}

function clearLog() {
    if (!currentLogFile) {
        alert('Please select a log file first');
        return;
    }
    
    if (confirm(`Are you sure you want to clear ${currentLogFile}?`)) {
        document.getElementById('log-content').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                <p>Log file ${currentLogFile} has been cleared</p>
            </div>
        `;
    }
}

function toggleLogStream() {
    const button = document.getElementById('stream-toggle');
    const stream = document.getElementById('live-log-stream');
    
    if (!streamActive) {
        // Start streaming
        streamActive = true;
        button.innerHTML = '<i class="fas fa-stop"></i> Stop Stream';
        button.className = 'btn btn-sm btn-danger';
        
        stream.innerHTML = '';
        
        streamInterval = setInterval(() => {
            const timestamp = new Date().toLocaleTimeString();
            const messages = [
                `[${timestamp}] INFO: Processing migration job...`,
                `[${timestamp}] INFO: Analysis completed successfully`,
                `[${timestamp}] INFO: Code transformation in progress...`,
                `[${timestamp}] INFO: Testing phase started`,
                `[${timestamp}] INFO: All tests passed`,
                `[${timestamp}] INFO: Job completed successfully`
            ];
            
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            stream.innerHTML += randomMessage + '\n';
            stream.scrollTop = stream.scrollHeight;
        }, 2000);
        
    } else {
        // Stop streaming
        streamActive = false;
        button.innerHTML = '<i class="fas fa-play"></i> Start Stream';
        button.className = 'btn btn-sm btn-success';
        
        if (streamInterval) {
            clearInterval(streamInterval);
            streamInterval = null;
        }
        
        stream.innerHTML += `\n[${new Date().toLocaleTimeString()}] INFO: Log stream stopped\n`;
    }
}

// Auto-refresh live stream when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Optional: Auto-start live stream
    // toggleLogStream();
});
</script>
{% endblock %}
