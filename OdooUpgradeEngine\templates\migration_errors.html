{% extends "base.html" %}

{% block title %}Migration Errors - {{ job.module.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-exclamation-triangle text-danger me-2"></i>Migration Errors</h2>
                    <p class="text-muted mb-0">{{ job.module.name }} → {{ job.target_version }}</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="retryMigration({{ job.id }})">
                        <i class="fas fa-redo me-1"></i>Retry Migration
                    </button>
                    <a href="{{ url_for('main.migration_orchestrator') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bug me-2"></i>Error Details
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if job.log %}
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-exclamation-circle me-2"></i>Migration Log</h6>
                                    <pre class="mb-0" style="white-space: pre-wrap; font-size: 12px;">{{ job.log }}</pre>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No detailed error log available. The migration failed during processing.
                                </div>
                            {% endif %}

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Common Solutions</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <strong>Dependency Issues:</strong> Ensure all required dependencies are available in the target version
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <strong>API Changes:</strong> Check if deprecated APIs have been removed in the target version
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <strong>Module Structure:</strong> Verify the module follows Odoo's standard structure
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <strong>Database Schema:</strong> Check for incompatible database schema changes
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Migration Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Module:</strong><br>
                                <span class="text-muted">{{ job.module.name }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Current Version:</strong><br>
                                <span class="badge bg-secondary">{{ job.module.version }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Target Version:</strong><br>
                                <span class="badge bg-primary">{{ job.target_version }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                <span class="badge bg-danger">{{ job.status.replace('_', ' ').title() }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Started:</strong><br>
                                <span class="text-muted">{{ job.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Next Steps
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="retryMigration({{ job.id }})">
                                    <i class="fas fa-redo me-1"></i>Retry Migration
                                </button>
                                <a href="{{ url_for('main.module_details', module_id=job.module_id) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-1"></i>View Module Details
                                </a>
                                <a href="{{ url_for('main.orchestrate_migration_form', module_id=job.module_id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-cog me-1"></i>Reconfigure Migration
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>Need Help?
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted">
                                If you continue to experience issues, consider:
                            </p>
                            <ul class="small text-muted">
                                <li>Checking the module documentation</li>
                                <li>Reviewing Odoo upgrade guides</li>
                                <li>Consulting the community forums</li>
                                <li>Manual code review and fixes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function retryMigration(jobId) {
    if (confirm('Are you sure you want to retry this migration? This will reset the migration status and start over.')) {
        fetch(`/migration/${jobId}/retry`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Migration has been queued for retry!');
                window.location.href = '/migration_orchestrator';
            } else {
                alert('Error retrying migration: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error retrying migration: ' + error.message);
        });
    }
}
</script>
{% endblock %}
