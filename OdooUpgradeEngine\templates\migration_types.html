{% extends "base.html" %}

{% block title %}Migration Types - Choose Your Workflow{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-route me-2 text-primary"></i>
                        Migration Types
                    </h1>
                    <p class="text-muted mb-0">Choose the migration workflow that best fits your needs</p>
                </div>
                <div>
                    <a href="{{ url_for('main.migration_orchestrator') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Types Grid -->
    <div class="row">
        <!-- Single Module Migration -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-primary shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cube me-2"></i>
                        Single Module Migration
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Migrate individual modules with full TrueMigrationOrchestrator analysis and manual control over each step.</p>
                    
                    <h6 class="fw-bold mb-2">Features:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>AST-based code transformation</li>
                        <li><i class="fas fa-check text-success me-2"></i>AI-powered analysis</li>
                        <li><i class="fas fa-check text-success me-2"></i>Visual diff review</li>
                        <li><i class="fas fa-check text-success me-2"></i>Manual approval process</li>
                        <li><i class="fas fa-check text-success me-2"></i>Automated testing</li>
                        <li><i class="fas fa-check text-success me-2"></i>Docker environment testing</li>
                    </ul>

                    <h6 class="fw-bold mb-2">Best For:</h6>
                    <ul class="list-unstyled small text-muted">
                        <li>• Critical business modules</li>
                        <li>• Complex custom modules</li>
                        <li>• First-time migrations</li>
                        <li>• Quality assurance workflows</li>
                    </ul>
                </div>
                <div class="card-footer bg-light">
                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-primary w-100">
                        <i class="fas fa-arrow-right me-1"></i>Start Single Migration
                    </a>
                </div>
            </div>
        </div>

        <!-- Database Bulk Migration -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-success shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        Database Bulk Migration
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Migrate multiple modules simultaneously with database-level operations and bulk processing capabilities.</p>
                    
                    <h6 class="fw-bold mb-2">Features:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>Batch processing</li>
                        <li><i class="fas fa-check text-success me-2"></i>Database schema migration</li>
                        <li><i class="fas fa-check text-success me-2"></i>Dependency resolution</li>
                        <li><i class="fas fa-check text-success me-2"></i>Rollback capabilities</li>
                        <li><i class="fas fa-check text-success me-2"></i>Progress monitoring</li>
                        <li><i class="fas fa-check text-success me-2"></i>Error recovery</li>
                    </ul>

                    <h6 class="fw-bold mb-2">Best For:</h6>
                    <ul class="list-unstyled small text-muted">
                        <li>• Large module collections</li>
                        <li>• Production environments</li>
                        <li>• Time-sensitive migrations</li>
                        <li>• Standard Odoo modules</li>
                    </ul>
                </div>
                <div class="card-footer bg-light">
                    <a href="{{ url_for('main.bulk_migration') }}" class="btn btn-success w-100">
                        <i class="fas fa-arrow-right me-1"></i>Start Bulk Migration
                    </a>
                </div>
            </div>
        </div>

        <!-- Automated Migration -->
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-warning shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        Automated Migration
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Fully automated migration pipeline with AI-driven decisions and minimal human intervention.</p>
                    
                    <h6 class="fw-bold mb-2">Features:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>AI-powered automation</li>
                        <li><i class="fas fa-check text-success me-2"></i>Smart conflict resolution</li>
                        <li><i class="fas fa-check text-success me-2"></i>Continuous integration</li>
                        <li><i class="fas fa-check text-success me-2"></i>Auto-testing & validation</li>
                        <li><i class="fas fa-check text-success me-2"></i>Scheduled migrations</li>
                        <li><i class="fas fa-check text-success me-2"></i>Notification system</li>
                    </ul>

                    <h6 class="fw-bold mb-2">Best For:</h6>
                    <ul class="list-unstyled small text-muted">
                        <li>• Routine maintenance</li>
                        <li>• Standard module updates</li>
                        <li>• CI/CD pipelines</li>
                        <li>• Development environments</li>
                    </ul>
                </div>
                <div class="card-footer bg-light">
                    <a href="{{ url_for('automation.automation_dashboard') }}" class="btn btn-warning w-100">
                        <i class="fas fa-arrow-right me-1"></i>Start Automated Migration
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflow Comparison -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Migration Type Comparison
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Feature</th>
                                    <th class="text-center">Single Module</th>
                                    <th class="text-center">Database Bulk</th>
                                    <th class="text-center">Automated</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Manual Control</strong></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-minus text-muted"></i></td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                </tr>
                                <tr>
                                    <td><strong>Batch Processing</strong></td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td><strong>Speed</strong></td>
                                    <td class="text-center"><span class="badge bg-warning">Slow</span></td>
                                    <td class="text-center"><span class="badge bg-success">Fast</span></td>
                                    <td class="text-center"><span class="badge bg-success">Very Fast</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Quality Assurance</strong></td>
                                    <td class="text-center"><span class="badge bg-success">High</span></td>
                                    <td class="text-center"><span class="badge bg-warning">Medium</span></td>
                                    <td class="text-center"><span class="badge bg-info">Automated</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Human Intervention</strong></td>
                                    <td class="text-center"><span class="badge bg-primary">Required</span></td>
                                    <td class="text-center"><span class="badge bg-warning">Optional</span></td>
                                    <td class="text-center"><span class="badge bg-success">Minimal</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Getting Started Guide -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Getting Started
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Before You Begin:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-upload text-primary me-2"></i>Upload your modules via <a href="{{ url_for('main.upload_modules') }}">Upload Modules</a></li>
                                <li><i class="fas fa-cog text-primary me-2"></i>Configure your target Odoo version</li>
                                <li><i class="fas fa-database text-primary me-2"></i>Ensure database backup is available</li>
                                <li><i class="fas fa-docker text-primary me-2"></i>Verify Docker environments are running</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Need Help Choosing?</h6>
                            <p class="small text-muted mb-2">Not sure which migration type to use?</p>
                            <a href="{{ url_for('main.migration_orchestrator') }}" class="btn btn-outline-info btn-sm me-2">
                                <i class="fas fa-tachometer-alt me-1"></i>View Dashboard
                            </a>
                            <a href="#" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-question-circle me-1"></i>Migration Guide
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
