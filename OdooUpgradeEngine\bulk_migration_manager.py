import requests
import time
import logging
from dependency_resolver import DependencyResolver
# We assume a utility exists to connect to a remote DB and list modules
from db_discovery import discover_installed_modules 

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class BulkMigrationManager:
    def __init__(self, db_connection_info, target_version):
        self.db_connection_info = db_connection_info
        self.target_version = target_version
        self.api_base_url = "http://localhost:5000"

    def run_bulk_migration(self):
        logging.info("Starting bulk migration process...")

        # 1. Discover all modules from the production database
        # This is a placeholder for the actual discovery logic.
        # It would return a list of module names and their dependencies.
        all_modules_with_deps = discover_installed_modules(self.db_connection_info)
        module_names = list(all_modules_with_deps.keys())
        logging.info(f"Discovered {len(module_names)} modules.")

        # 2. Resolve dependency order
        resolver = DependencyResolver(all_modules_with_deps)
        sorted_batches = resolver.get_dependency_batches()
        logging.info(f"Resolved modules into {len(sorted_batches)} batches.")

        # 3. Process each batch sequentially
        for i, batch in enumerate(sorted_batches):
            logging.info(f"--- Processing Batch {i+1}/{len(sorted_batches)}: {', '.join(batch)} ---")
            
            job_ids = []
            for module_name in batch:
                # First, we need to get the module ID from our system's database
                module_id = self._get_module_id_by_name(module_name)
                if module_id:
                    job_id = self._trigger_migration(module_id, self.target_version)
                    if job_id:
                        job_ids.append(job_id)
                else:
                    logging.warning(f"Module '{module_name}' not found in OdooUpgradeEngine. Skipping.")

            # 4. Monitor the status of the current batch before proceeding
            self._wait_for_batch_completion(job_ids)
            logging.info(f"--- Batch {i+1} complete. ---")

        logging.info("Bulk migration process finished.")

    def _get_module_id_by_name(self, module_name):
        # This assumes an API endpoint exists to fetch a module by name
        # This would need to be added to routes.py
        try:
            response = requests.get(f"{self.api_base_url}/api/modules/by_name/{module_name}")
            if response.status_code == 200:
                return response.json().get('id')
        except requests.exceptions.RequestException as e:
            logging.error(f"Could not retrieve module ID for '{module_name}': {e}")
        return None

    def _trigger_migration(self, module_id, target_version):
        """Triggers a migration and returns the new job ID."""
        # This assumes the /migration_jobs endpoint is modified to return the job ID in its response
        try:
            response = requests.post(f"{self.api_base_url}/migration_jobs", data={'module_id': module_id, 'target_version': target_version})
            if response.status_code == 201: # Assuming API returns 201 Created with job data
                job_id = response.json().get('job_id')
                logging.info(f"Successfully queued job {job_id} for module ID {module_id}.")
                return job_id
            else:
                 logging.error(f"Failed to queue job for module ID {module_id}. Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logging.error(f"API call to queue job for module ID {module_id} failed: {e}")
        return None

    def _wait_for_batch_completion(self, job_ids):
        """Polls the status of all jobs in a batch until they are complete."""
        logging.info(f"Waiting for jobs to complete: {job_ids}")
        while True:
            completed_count = 0
            all_finished = True
            for job_id in job_ids:
                try:
                    res = requests.get(f"{self.api_base_url}/api/status/{job_id}")
                    status = res.json().get('status', 'UNKNOWN')
                    if status in ['COMPLETED', 'FAILED']:
                        completed_count += 1
                    else:
                        all_finished = False
                except requests.exceptions.RequestException:
                    all_finished = False # Assume not finished if API call fails
            
            logging.info(f"Batch progress: {completed_count}/{len(job_ids)} jobs finished.")
            if all_finished:
                logging.info("All jobs in batch have finished.")
                break
            time.sleep(30) # Wait 30 seconds before polling again