#!/usr/bin/env python3
"""
Docker Environment Manager - Multi-Version Odoo Environment Management

This module manages Docker-based Odoo environments for testing migrations
across multiple versions (v13-v18). It replaces the legacy single-installation
approach with a flexible, containerized testing system.
"""

import docker
import logging
import json
import time
import random
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from models import DockerOdooEnvironment, db
from app import app

logger = logging.getLogger(__name__)

class DockerEnvironmentManager:
    """
    Manages Docker-based Odoo environments for multi-version testing.
    
    This manager handles:
    - Creating isolated Odoo environments for each version (v13-v18)
    - Managing container lifecycle (start, stop, cleanup)
    - Port allocation and network configuration
    - Performance monitoring and resource usage
    - Environment persistence and recovery
    """
    
    def __init__(self):
        self.docker_client = None
        self.supported_versions = ['13.0', '14.0', '15.0', '16.0', '17.0', '18.0']
        self.base_port = 8069  # Standard Odoo port
        self.port_range = (8069, 8099)  # Available port range
        self.network_name = 'odoo-migration-network'
        
        # Initialize Docker client
        self._initialize_docker()
        
        # Setup migration network
        self._ensure_migration_network()
    
    def _initialize_docker(self):
        """Initialize Docker client with error handling"""
        try:
            self.docker_client = docker.from_env()
            # Test connection
            self.docker_client.ping()
            logger.info("Docker client initialized successfully")
        except Exception as e:
            logger.warning(f"Docker not available: {e}")
            self.docker_client = None
    
    def _ensure_migration_network(self):
        """Ensure the migration network exists"""
        if not self.docker_client:
            return
        
        try:
            # Check if network exists
            networks = self.docker_client.networks.list(names=[self.network_name])
            if not networks:
                # Create the network
                self.docker_client.networks.create(
                    self.network_name,
                    driver="bridge",
                    options={"com.docker.network.bridge.enable_icc": "true"}
                )
                logger.info(f"Created migration network: {self.network_name}")
            else:
                logger.debug(f"Migration network already exists: {self.network_name}")
        except Exception as e:
            logger.error(f"Failed to create migration network: {e}")
    
    def create_environment(self, odoo_version: str, force_recreate: bool = False) -> Optional[DockerOdooEnvironment]:
        """
        Create a new Docker-based Odoo environment for the specified version.
        
        Args:
            odoo_version: Odoo version (e.g., '16.0', '17.0')
            force_recreate: Whether to recreate if environment already exists
            
        Returns:
            DockerOdooEnvironment instance or None if creation failed
        """
        if not self.docker_client:
            logger.error("Docker not available - cannot create environment")
            return None
        
        if odoo_version not in self.supported_versions:
            logger.error(f"Unsupported Odoo version: {odoo_version}")
            return None
        
        # Check if environment already exists
        with app.app_context():
            existing = DockerOdooEnvironment.query.filter_by(
                odoo_version=odoo_version,
                status='running'
            ).first()
            
            if existing and not force_recreate:
                logger.info(f"Environment for Odoo {odoo_version} already exists")
                return existing
        
        try:
            # Generate unique container name
            container_name = f"odoo-{odoo_version}-{int(time.time())}"
            
            # Allocate port
            port = self._allocate_port()
            if not port:
                logger.error("No available ports for Odoo environment")
                return None
            
            # Create database record
            with app.app_context():
                env_record = DockerOdooEnvironment(
                    odoo_version=odoo_version,
                    container_name=container_name,
                    image_tag=f"odoo:{odoo_version}",
                    status='creating',
                    port=port,
                    database_name=f"odoo_{odoo_version.replace('.', '_')}",
                    admin_password=self._generate_admin_password()
                )
                
                db.session.add(env_record)
                db.session.commit()
                env_id = env_record.id
            
            # Create and start container
            container = self._create_container(
                odoo_version=odoo_version,
                container_name=container_name,
                port=port,
                database_name=env_record.database_name,
                admin_password=env_record.admin_password
            )
            
            if container:
                # Update record with container information
                with app.app_context():
                    env_record = DockerOdooEnvironment.query.get(env_id)
                    env_record.container_id = container.id
                    env_record.status = 'running'
                    env_record.environment_config = {
                        'port': port,
                        'database_name': env_record.database_name,
                        'admin_password': env_record.admin_password,
                        'network': self.network_name
                    }
                    db.session.commit()
                
                logger.info(f"Successfully created Odoo {odoo_version} environment on port {port}")
                return env_record
            else:
                # Cleanup failed record
                with app.app_context():
                    env_record = DockerOdooEnvironment.query.get(env_id)
                    db.session.delete(env_record)
                    db.session.commit()
                return None
                
        except Exception as e:
            logger.error(f"Failed to create Odoo {odoo_version} environment: {e}")
            return None
    
    def _create_container(self, odoo_version: str, container_name: str, port: int, 
                         database_name: str, admin_password: str) -> Optional[Any]:
        """Create and start the Docker container"""
        try:
            # Environment variables for Odoo
            environment = {
                'POSTGRES_DB': database_name,
                'POSTGRES_USER': 'odoo',
                'POSTGRES_PASSWORD': 'odoo',
                'POSTGRES_HOST': 'db'
            }
            
            # Create container
            container = self.docker_client.containers.run(
                image=f"odoo:{odoo_version}",
                name=container_name,
                ports={'8069/tcp': port},
                environment=environment,
                network=self.network_name,
                detach=True,
                restart_policy={"Name": "unless-stopped"}
            )
            
            # Wait for container to be ready
            time.sleep(5)
            
            # Verify container is running
            container.reload()
            if container.status == 'running':
                logger.info(f"Container {container_name} started successfully")
                return container
            else:
                logger.error(f"Container {container_name} failed to start: {container.status}")
                container.remove(force=True)
                return None
                
        except Exception as e:
            logger.error(f"Failed to create container: {e}")
            return None
    
    def _allocate_port(self) -> Optional[int]:
        """Allocate an available port for the environment"""
        start_port, end_port = self.port_range
        
        # Get currently used ports
        with app.app_context():
            used_ports = set(
                env.port for env in DockerOdooEnvironment.query.filter_by(status='running').all()
                if env.port
            )
        
        # Find available port
        for port in range(start_port, end_port + 1):
            if port not in used_ports:
                return port
        
        return None
    
    def _generate_admin_password(self) -> str:
        """Generate a secure admin password"""
        import string
        import secrets
        
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(16))
    
    def get_environment(self, odoo_version: str) -> Optional[DockerOdooEnvironment]:
        """Get existing environment for specified version"""
        with app.app_context():
            return DockerOdooEnvironment.get_by_version(odoo_version)
    
    def list_environments(self) -> List[DockerOdooEnvironment]:
        """List all managed environments"""
        with app.app_context():
            return DockerOdooEnvironment.query.all()
    
    def stop_environment(self, env_id: int) -> bool:
        """Stop a specific environment"""
        if not self.docker_client:
            return False
        
        try:
            with app.app_context():
                env = DockerOdooEnvironment.query.get(env_id)
                if not env or not env.container_id:
                    return False
                
                # Stop container
                container = self.docker_client.containers.get(env.container_id)
                container.stop()
                
                # Update status
                env.status = 'stopped'
                db.session.commit()
                
                logger.info(f"Stopped environment {env.container_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to stop environment: {e}")
            return False
    
    def remove_environment(self, env_id: int) -> bool:
        """Remove an environment completely"""
        if not self.docker_client:
            return False
        
        try:
            with app.app_context():
                env = DockerOdooEnvironment.query.get(env_id)
                if not env:
                    return False
                
                # Remove container if it exists
                if env.container_id:
                    try:
                        container = self.docker_client.containers.get(env.container_id)
                        container.stop()
                        container.remove()
                    except:
                        pass  # Container might already be removed
                
                # Remove database record
                db.session.delete(env)
                db.session.commit()
                
                logger.info(f"Removed environment {env.container_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to remove environment: {e}")
            return False
    
    def cleanup_orphaned_environments(self):
        """Clean up environments that lost their containers"""
        if not self.docker_client:
            return
        
        try:
            with app.app_context():
                environments = DockerOdooEnvironment.query.filter_by(status='running').all()
                
                for env in environments:
                    if env.container_id:
                        try:
                            container = self.docker_client.containers.get(env.container_id)
                            if container.status != 'running':
                                env.status = 'stopped'
                        except:
                            # Container doesn't exist
                            env.status = 'error'
                            env.error_log = f"Container {env.container_id} not found"
                
                db.session.commit()
                logger.info("Completed orphaned environment cleanup")
                
        except Exception as e:
            logger.error(f"Failed to cleanup orphaned environments: {e}")
    
    def get_environment_stats(self) -> Dict[str, Any]:
        """Get statistics about managed environments"""
        with app.app_context():
            environments = DockerOdooEnvironment.query.all()
            
            stats = {
                'total_environments': len(environments),
                'running_environments': len([e for e in environments if e.status == 'running']),
                'stopped_environments': len([e for e in environments if e.status == 'stopped']),
                'error_environments': len([e for e in environments if e.status == 'error']),
                'versions_available': list(set(e.odoo_version for e in environments if e.status == 'running')),
                'port_usage': [e.port for e in environments if e.status == 'running' and e.port],
                'docker_available': self.docker_client is not None
            }
            
            return stats

# Global environment manager instance
_docker_env_manager = None

def get_docker_environment_manager() -> DockerEnvironmentManager:
    """Get the global Docker environment manager instance"""
    global _docker_env_manager
    if _docker_env_manager is None:
        _docker_env_manager = DockerEnvironmentManager()
    return _docker_env_manager

def ensure_odoo_environment(odoo_version: str) -> Optional[DockerOdooEnvironment]:
    """Ensure an Odoo environment exists for the specified version"""
    manager = get_docker_environment_manager()
    
    # Check if environment already exists
    env = manager.get_environment(odoo_version)
    if env and env.status == 'running':
        return env
    
    # Create new environment
    return manager.create_environment(odoo_version)

def main():
    """Test the Docker environment manager"""
    logging.basicConfig(level=logging.INFO)
    
    manager = DockerEnvironmentManager()
    
    print("Docker Environment Manager Test")
    print("=" * 40)
    
    # Test creating environments
    for version in ['16.0', '17.0']:
        print(f"\nCreating Odoo {version} environment...")
        env = manager.create_environment(version)
        if env:
            print(f"✅ Successfully created environment on port {env.port}")
        else:
            print(f"❌ Failed to create environment")
    
    # Show statistics
    stats = manager.get_environment_stats()
    print(f"\nEnvironment Statistics:")
    print(f"Total: {stats['total_environments']}")
    print(f"Running: {stats['running_environments']}")
    print(f"Available versions: {stats['versions_available']}")

if __name__ == "__main__":
    main()