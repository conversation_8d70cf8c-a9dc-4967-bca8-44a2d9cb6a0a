#!/usr/bin/env python3
"""
Test the manifest parsing fix in professional_upgrader.py
"""

import tempfile
import os
from pathlib import Path
from professional_upgrader import ProfessionalModuleUpgrader

def test_manifest_parsing():
    """Test that the manifest parsing fix works correctly"""
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a sample manifest file like the corrupted one
        manifest_content = """# -*- coding: utf-8 -*-
{
    "name": "Test Module",
    "version": "********.0",
    "depends": ["base"],
    "installable": True,
    "auto_install": False,
    "author": "Test Author",
    "category": "Test Category",
    "description": "Test module for manifest parsing"
}
"""
        
        # Create the module structure
        module_path = Path(temp_dir) / "test_module"
        module_path.mkdir()
        
        # Write the manifest file
        manifest_file = module_path / "__manifest__.py"
        with open(manifest_file, 'w', encoding='utf-8') as f:
            f.write(manifest_content)
        
        print(f"Created test module at: {module_path}")
        print(f"Original manifest content:")
        print(manifest_content)
        
        # Test the professional upgrader
        upgrader = ProfessionalModuleUpgrader()
        
        # Test manifest file upgrade
        results = upgrader._upgrade_manifest_files(str(module_path), "17.0")
        
        print(f"\nUpgrade results: {results}")
        
        # Read the upgraded manifest
        with open(manifest_file, 'r', encoding='utf-8') as f:
            upgraded_content = f.read()
        
        print(f"\nUpgraded manifest content:")
        print(upgraded_content)
        
        # Verify the upgrade worked
        if "17.0" in upgraded_content and "Test Module" in upgraded_content:
            print("\n✅ SUCCESS: Manifest parsing and upgrade worked correctly!")
            print("✅ Version was updated to 17.0")
            print("✅ Original content was preserved")
        else:
            print("\n❌ FAILED: Manifest parsing or upgrade failed")
            print("❌ Content may have been corrupted")

if __name__ == "__main__":
    test_manifest_parsing()