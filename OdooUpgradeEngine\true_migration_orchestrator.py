# true_migration_orchestrator.py
from extensions import db
from models import Migration<PERSON><PERSON>, Diff<PERSON>eport
from security_scanner import SecurityScanner
from ast_based_upgrader import ProfessionalASTUpgrader
from xml_safe_upgrader import XMLSafeUpgrader
from visual_diff_viewer import VisualDiffViewer
from database_migration_executor import DatabaseMigrationExecutor
from module_testing_engine import ModuleT<PERSON>ing<PERSON>ngine
from smart_version_updater import SmartVersionUpdater
import os
import shutil
import logging

# Import AI Migration Assistant for intelligent analysis
try:
    from ai_migration_assistant import AIMigrationAssistant, MigrationContext
    AI_ASSISTANT_AVAILABLE = True
except ImportError:
    AI_ASSISTANT_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TrueMigrationOrchestrator:
    def __init__(self, job_id: int):
        self.job = MigrationJob.query.get(job_id)
        if not self.job:
            raise ValueError(f"MigrationJob with ID {job_id} not found.")
        self.work_dir = f"temp/migration_{self.job.id}"
        # Correctly determine module subdirectory name after unpacking
        self.original_module_root = None
        self.upgraded_module_root = None
        self.log = []
        if self.job.log:
            self.log = self.job.log.split('\n')

        # ===== AI INTEGRATION START =====
        # Initialize AI Migration Assistant for intelligent analysis and recommendations
        self.ai_assistant = None
        self.ai_analysis_result = None
        if AI_ASSISTANT_AVAILABLE:
            try:
                self.ai_assistant = AIMigrationAssistant()
                self._add_log("AI Migration Assistant initialized for intelligent analysis")
            except Exception as e:
                self._add_log(f"AI Assistant initialization failed: {str(e)}")
                self.ai_assistant = None
        # ===== AI INTEGRATION END =====

        # Initialize AI Migration Assistant for intelligent analysis
        self.ai_assistant = None
        self.ai_analysis_result = None
        if AI_ASSISTANT_AVAILABLE:
            try:
                self.ai_assistant = AIMigrationAssistant()
                self._add_log("AI Migration Assistant initialized for intelligent analysis")
            except Exception as e:
                self._add_log(f"AI Assistant initialization failed: {str(e)}")
                self.ai_assistant = None

    def _setup_workspace(self):
        if os.path.exists(self.work_dir):
            shutil.rmtree(self.work_dir)
        os.makedirs(self.work_dir)
        original_unzip_path = os.path.join(self.work_dir, 'original')
        upgraded_unzip_path = os.path.join(self.work_dir, 'upgraded')
        
        shutil.unpack_archive(self.job.module.path, original_unzip_path)
        shutil.unpack_archive(self.job.module.path, upgraded_unzip_path)
        
        # Dynamically find the subdirectory name
        self.original_module_root = os.path.join(original_unzip_path, os.listdir(original_unzip_path)[0])
        self.upgraded_module_root = os.path.join(upgraded_unzip_path, os.listdir(upgraded_unzip_path)[0])
        self.job.upgraded_module_path = self.upgraded_module_root
        self._add_log(f"Workspace created at {self.work_dir}")

    def _add_log(self, message):
        logging.info(f"Job {self.job.id}: {message}")
        self.log.append(message)

    def _commit_log_and_status(self):
        self.job.log = "\n".join(self.log)
        db.session.commit()

    def run_initial_phase(self):
        self._add_log(f"Orchestration started for module '{self.job.module.name}' to v{self.job.target_version}.")
        try:
            self._setup_workspace()
            self.job.status = 'ANALYSIS'
            self._commit_log_and_status()
            
            scanner = SecurityScanner(self.upgraded_module_root)
            security_issues = scanner.run_scan()
            self.job.security_report = "\n".join(security_issues) if security_issues else "No security issues found."
            self._add_log(f"Security scan complete. Found {len(security_issues)} issues.")

            self.job.status = 'CODE_TRANSFORMATION'
            self._commit_log_and_status()

            python_upgrader = ProfessionalASTUpgrader()
            python_upgrade_results = python_upgrader.upgrade_module(self.upgraded_module_root)
            self._add_log("AST-based Python transformation complete.")
            for change in python_upgrade_results.get('changes_summary', []):
                self._add_log(f"  - {change}")

            xml_upgrader = XMLSafeUpgrader(self.upgraded_module_root, self.job.target_version)
            xml_upgrader.upgrade_module()
            self._add_log("LXML-based XML transformation complete.")

            # Smart Version Update Phase
            self.job.status = 'VERSION_UPDATE'
            self._commit_log_and_status()

            version_updater = SmartVersionUpdater()
            manifest_path = os.path.join(self.upgraded_module_root, '__manifest__.py')
            version_result = version_updater.detect_and_update_version(manifest_path, self.job.target_version)

            if version_result['success']:
                self._add_log(f"Version updated: {version_result['original_version']} → {version_result['updated_version']}")
                self._add_log(f"Quote style: {version_result['quote_style']}, Pattern: {version_result['pattern_used']}")
            else:
                self._add_log(f"Version update failed: {version_result['error']}")
                # Continue anyway - version update failure shouldn't stop migration

            self.job.status = 'VISUAL_DIFF'
            self._commit_log_and_status()

            diff_viewer = VisualDiffViewer(self.original_module_root, self.upgraded_module_root)
            diff_html, report_path = diff_viewer.generate_diff_report(output_dir='uploads/diff_reports', prefix=f"job_{self.job.id}")
            diff_report = DiffReport(migration_job_id=self.job.id, report_path=report_path, content=diff_html)
            db.session.add(diff_report)
            self._add_log(f"Visual diff report generated at {report_path}.")

            # ===== AI ANALYSIS INTEGRATION START =====
            # Perform AI analysis of the migration for intelligent recommendations
            self._perform_ai_analysis()

            # Determine next status based on AI analysis (smart auto-approval)
            next_status = self._determine_approval_status()
            self.job.status = next_status

            if next_status == 'AI_AUTO_APPROVED':
                self._add_log("AI analysis indicates low risk - migration auto-approved.")
            else:
                self._add_log("Migration requires human review - awaiting approval.")
            # ===== AI ANALYSIS INTEGRATION END =====

            self._commit_log_and_status()
        except Exception as e:
            logging.error(f"Job {self.job.id}: FAILED during initial phase. Error: {e}", exc_info=True)
            self.job.status = 'FAILED'
            self._add_log(f"Orchestration failed catastrophically: {str(e)}")
            self._commit_log_and_status()

    def continue_after_approval(self):
        # ===== AI AUTO-APPROVAL INTEGRATION START =====
        if self.job.status == 'AI_AUTO_APPROVED':
            self._add_log("AI auto-approval detected. Continuing migration automatically.")
        else:
            self._add_log("User approval received. Resuming migration.")
        # ===== AI AUTO-APPROVAL INTEGRATION END =====

        try:
            if self.job.status not in ['DIFF_APPROVED', 'AI_AUTO_APPROVED']:
                raise Exception("Job is not in the required 'DIFF_APPROVED' or 'AI_AUTO_APPROVED' state to continue.")

            self.job.status = 'DB_MIGRATION'
            self._add_log("Beginning database migration phase.")
            self._commit_log_and_status()
            
            db_executor = DatabaseMigrationExecutor(self.job)
            db_success, db_log = db_executor.run_migration()
            self.log.extend(db_log.split('\n'))

            if not db_success:
                raise Exception("Database migration failed. See log for details.")

            self.job.status = 'TESTING'
            self._add_log("Database migration successful. Beginning automated testing phase.")
            self._commit_log_and_status()

            testing_engine = ModuleTestingEngine(self.job, db_executor.db_name)
            test_success, test_log = testing_engine.run_tests()
            self.log.extend(test_log.split('\n'))

            if not test_success:
                raise Exception("Automated testing failed. See log for details.")

            self.job.status = 'COMPLETED'
            self._add_log("All phases completed successfully. Migration is complete.")
            self._commit_log_and_status()
        except Exception as e:
            logging.error(f"Job {self.job.id}: FAILED during continuation phase. Error: {e}", exc_info=True)
            self.job.status = 'FAILED'
            self._add_log(f"Orchestration failed after approval: {str(e)}")
            self._commit_log_and_status()

    # ===== AI ANALYSIS METHODS START =====
    def _perform_ai_analysis(self):
        """
        Perform AI analysis of the migration to provide intelligent recommendations.
        """
        if not self.ai_assistant or not AI_ASSISTANT_AVAILABLE:
            self._add_log("AI analysis skipped - AI assistant not available")
            return

        try:
            # Create migration context for AI analysis
            migration_context = MigrationContext(
                module_name=self.job.module.name,
                source_version=self.job.module.version or "unknown",
                target_version=self.job.target_version,
                module_path=self.original_module_root,
                upgraded_path=self.upgraded_module_root,
                transformation_log='\n'.join(self.log),
                security_scan_results=self.job.security_report or "No security scan performed"
            )

            # Perform AI analysis
            self._add_log("Starting AI analysis of migration...")
            self.ai_analysis_result = self.ai_assistant.analyze_migration_context(migration_context)

            # Log AI analysis results
            self._add_log(f"AI Analysis Complete:")
            self._add_log(f"  - Confidence Score: {self.ai_analysis_result.confidence_score:.2f}")
            self._add_log(f"  - Risk Level: {self.ai_analysis_result.risk_level}")
            self._add_log(f"  - Migration Strategy: {self.ai_analysis_result.migration_strategy}")
            self._add_log(f"  - Recommendations: {len(self.ai_analysis_result.recommendations)} items")
            self._add_log(f"  - Potential Issues: {len(self.ai_analysis_result.potential_issues)} items")

        except Exception as e:
            self._add_log(f"AI analysis failed: {str(e)}")
            self.ai_analysis_result = None

    def _determine_approval_status(self):
        """
        Determine whether migration should be auto-approved based on AI analysis.

        Returns:
            str: 'AI_AUTO_APPROVED' for low-risk migrations, 'AWAITING_APPROVAL' for others
        """
        if not self.ai_analysis_result:
            return 'AWAITING_APPROVAL'

        # Auto-approve criteria:
        # - High confidence (>= 0.8)
        # - Low risk level
        # - No critical issues identified
        confidence_threshold = 0.8

        if (self.ai_analysis_result.confidence_score >= confidence_threshold and
            self.ai_analysis_result.risk_level == 'low' and
            not any(issue.get('impact') == 'critical' for issue in self.ai_analysis_result.potential_issues)):

            return 'AI_AUTO_APPROVED'

        return 'AWAITING_APPROVAL'

    def get_ai_analysis(self):
        """
        Get the AI analysis result for this migration.

        Returns:
            AIAnalysisResult or None: The AI analysis result if available
        """
        return self.ai_analysis_result
    # ===== AI ANALYSIS METHODS END =====