
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Module Upgrade Report: Universal Appointments: HR Bridge</title>
            <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
            <style>
                body { background-color: #1a1a1a; color: #e0e0e0; }
                .diff-container { margin: 20px 0; }
                .change-summary { background-color: #2d2d2d; padding: 15px; border-radius: 8px; margin: 10px 0; }
                .security-badge { padding: 2px 8px; border-radius: 4px; font-size: 12px; }
                .security-high { background-color: #dc3545; }
                .security-medium { background-color: #ffc107; color: #000; }
                .security-improvement { background-color: #28a745; }
                .security-low { background-color: #6c757d; }
                .complexity-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin: 0 2px; }
                .complexity-1 { background-color: #28a745; }
                .complexity-2 { background-color: #20c997; }
                .complexity-3 { background-color: #ffc107; }
                .complexity-4 { background-color: #fd7e14; }
                .complexity-5 { background-color: #dc3545; }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <h1 class="mt-4">Module Upgrade Report</h1>
                        <h2 class="text-primary">Universal Appointments: HR Bridge</h2>

                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Files Changed</h5>
                                        <h3 class="text-info">8</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Lines Added</h5>
                                        <h3 class="text-success">+49</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Lines Removed</h5>
                                        <h3 class="text-danger">-87</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Net Change</h5>
                                        <h3 class="text-warning">-38</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-5">
                            <h3>Change Summary</h3>
                            
                <div class="change-summary">
                    <h5>Framework Upgrades (1)</h5>
                    <ul>
                
                            <li>Upgraded 8 files for Odoo 17.0 compatibility</li>
                        </ul></div>
                        </div>

                        <div class="mt-5">
                            <h3>File-by-File Changes</h3>
                            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        models\business_resource.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span><span class="complexity-indicator complexity-3"></span>
                            <small class="text-muted">+29 -35</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/models\business_resource.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/models\business_resource.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1,5 +1,3 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-#coding: utf-8</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-</span>
<span style="color: #e0e0e0;"> from odoo import _, api, fields, models</span>
<span style="color: #e0e0e0;"> </span>
<span style="color: #e0e0e0;"> </span>
<span style="color: #17a2b8; font-weight: bold;">@@ -7,9 +5,9 @@</span>
<span style="color: #e0e0e0;">     &quot;&quot;&quot;</span>
<span style="color: #e0e0e0;">     Overwrite to link business resources with employee</span>
<span style="color: #e0e0e0;">     &quot;&quot;&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    _inherit = &quot;business.resource&quot;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    _inherit = &#x27;business.resource&#x27;</span>
<span style="color: #e0e0e0;"> </span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    @api.onchange(&quot;employee_id&quot;)</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    @api.onchange(&#x27;employee_id&#x27;)</span>
<span style="color: #e0e0e0;">     def _onchange_employee_id(self):</span>
<span style="color: #e0e0e0;">         &quot;&quot;&quot;</span>
<span style="color: #e0e0e0;">         Onchange method for employee_id</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -20,30 +18,27 @@</span>
<span style="color: #e0e0e0;">                 if resource.employee_id.user_id:</span>
<span style="color: #e0e0e0;">                     resource.user_id = resource.employee_id.user_id</span>
<span style="color: #e0e0e0;">                 if resource.employee_id.resource_calendar_id:</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    resource.resource_calendar_id = resource.employee_id.resource_calendar_id</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    employee_id = fields.Many2one(</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;hr.employee&quot;, </span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        string=&quot;Employee&quot;, </span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        ondelete=&quot;cascade&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        copy=False,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    )</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    _sql_constraints = [(&quot;employee_id_uniq&quot;, &quot;unique(employee_id)&quot;, _(&quot;Resource per each employee should be unique!&quot;))]</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    resource.resource_calendar_id = (resource.employee_id.</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                        resource_calendar_id)</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    employee_id = fields.Many2one(&#x27;hr.employee&#x27;, string=&#x27;Employee&#x27;,</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+        ondelete=&#x27;cascade&#x27;, copy=False)</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    _sql_constraints = [(&#x27;employee_id_uniq&#x27;, &#x27;unique(employee_id)&#x27;, _(</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+        &#x27;Resource per each employee should be unique!&#x27;))]</span>
<span style="color: #e0e0e0;"> </span>
<span style="color: #e0e0e0;">     @api.model</span>
<span style="color: #e0e0e0;">     def create(self, vals):</span>
<span style="color: #e0e0e0;">         &quot;&quot;&quot;</span>
<span style="color: #e0e0e0;">         Overwrite to retrieve resource from employee</span>
<span style="color: #e0e0e0;">         &quot;&quot;&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        if vals.get(&quot;employee_id&quot;):</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-            if vals.get(&quot;main_resource_id&quot;):</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                vals.pop(&quot;employee_id&quot;)</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+        if vals.get(&#x27;employee_id&#x27;):</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+            if vals.get(&#x27;main_resource_id&#x27;):</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                vals.pop(&#x27;employee_id&#x27;)</span>
<span style="color: #e0e0e0;">             else:</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                resource_id = self.env[&quot;hr.employee&quot;].browse(vals.get(&quot;employee_id&quot;)).resource_id</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                vals.update({&quot;resource_id&quot;: resource_id.id})</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                if not vals.get(&quot;tz&quot;):</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    vals.update({&quot;tz&quot;: resource_id.tz})</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                resource_id = self.env[&#x27;hr.employee&#x27;].browse(vals.get(</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    &#x27;employee_id&#x27;)).resource_id</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                vals.update({&#x27;resource_id&#x27;: resource_id.id})</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                if not vals.get(&#x27;tz&#x27;):</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    vals.update({&#x27;tz&#x27;: resource_id.tz})</span>
<span style="color: #e0e0e0;">         return super(business_resource, self).create(vals)</span>
<span style="color: #e0e0e0;"> </span>
<span style="color: #e0e0e0;">     def write(self, vals):</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -56,24 +51,23 @@</span>
<span style="color: #e0e0e0;">         res = False</span>
<span style="color: #e0e0e0;">         for resource in self:</span>
<span style="color: #e0e0e0;">             resource_vals = vals.copy()</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-            main_resource_id = resource_vals.get(&quot;main_resource_id&quot;) \</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                               or (resource_vals.get(&quot;main_resource_id&quot;) is None and resource.main_resource_id)</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-            if resource_vals.get(&quot;employee_id&quot;):</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+            main_resource_id = resource_vals.get(&#x27;main_resource_id&#x27;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                ) or resource_vals.get(&#x27;main_resource_id&#x27;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                ) is None and resource.main_resource_id</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+            if resource_vals.get(&#x27;employee_id&#x27;):</span>
<span style="color: #e0e0e0;">                 if main_resource_id:</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    # 0</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    resource_vals.pop(&quot;employee_id&quot;)</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    resource_vals.pop(&#x27;employee_id&#x27;)</span>
<span style="color: #e0e0e0;">                 else:</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    # 1</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    resource_id = self.env[&quot;hr.employee&quot;].browse(resource_vals.get(&quot;employee_id&quot;)).resource_id.id</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                    resource_vals.update({&quot;resource_id&quot;: resource_id})</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    resource_id = self.env[&#x27;hr.employee&#x27;].browse(resource_vals</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                        .get(&#x27;employee_id&#x27;)).resource_id.id</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    resource_vals.update({&#x27;resource_id&#x27;: resource_id})</span>
<span style="color: #e0e0e0;">                     if not resource.employee_id:</span>
<span style="color: #e0e0e0;">                         resource.resource_id.active = False</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-            elif resource_vals.get(&quot;employee_id&quot;) is not None:</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                # 2</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                resource_id = resource.resource_id.copy({&quot;name&quot;: resource.name})</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                resource_vals.update({&quot;resource_id&quot;: resource_id.id})</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+            elif resource_vals.get(&#x27;employee_id&#x27;) is not None:</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                resource_id = resource.resource_id.copy({&#x27;name&#x27;: resource.name}</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                    )</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                resource_vals.update({&#x27;resource_id&#x27;: resource_id.id})</span>
<span style="color: #e0e0e0;">             elif main_resource_id and resource.employee_id:</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                # 0</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                resource_vals.update({&quot;employee_id&quot;: False})</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                resource_vals.update({&#x27;employee_id&#x27;: False})</span>
<span style="color: #e0e0e0;">             res = super(business_resource, resource).write(resource_vals)</span>
<span style="color: #e0e0e0;">         return res</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        models\__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span>
                            <small class="text-muted">+0 -2</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/models\__init__.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/models\__init__.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1,3 +1 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-# -*- coding: utf-8 -*-</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-</span>
<span style="color: #e0e0e0;"> from . import business_resource</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        reports\__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span>
                            <small class="text-muted">+0 -1</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/reports\__init__.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/reports\__init__.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1 +0,0 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-# -*- coding: utf-8 -*-</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        __manifest__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span><span class="complexity-indicator complexity-3"></span><span class="complexity-indicator complexity-4"></span>
                            <small class="text-muted">+16 -38</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/__manifest__.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/__manifest__.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1,40 +1,18 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-# -*- coding: utf-8 -*-</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-{</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;name&quot;: &quot;Universal Appointments: HR Bridge&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;version&quot;: &quot;********.5&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;category&quot;: &quot;Extra Tools&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;author&quot;: &quot;faOtools&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;website&quot;: &quot;https://faotools.com/apps/15.0/universal-appointments-hr-bridge-15-0-business-appointment-hr-610&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;license&quot;: &quot;Other proprietary&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;application&quot;: True,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;installable&quot;: True,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;auto_install&quot;: False,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;depends&quot;: [</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;hr&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;business_appointment&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    ],</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;data&quot;: [</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;views/business_resource.xml&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;security/ir.model.access.csv&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;data/data.xml&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    ],</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;js&quot;: [</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        </span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    ],</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;assets&quot;: {},</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;demo&quot;: [</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        </span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    ],</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;external_dependencies&quot;: {},</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;summary&quot;: &quot;The extension to the Universal Appointments app to apply employees as appointment resources&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;description&quot;: &quot;&quot;&quot;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+{&#x27;name&#x27;: &#x27;Universal Appointments: HR Bridge&#x27;, &#x27;version&#x27;: &#x27;********.5&#x27;,</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;category&#x27;: &#x27;Extra Tools&#x27;, &#x27;author&#x27;: &#x27;faOtools&#x27;, &#x27;website&#x27;:</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;https://faotools.com/apps/15.0/universal-appointments-hr-bridge-15-0-business-appointment-hr-610&#x27;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    , &#x27;license&#x27;: &#x27;Other proprietary&#x27;, &#x27;application&#x27;: True, &#x27;installable&#x27;: </span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    True, &#x27;auto_install&#x27;: False, &#x27;depends&#x27;: [&#x27;hr&#x27;, &#x27;business_appointment&#x27;],</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;data&#x27;: [&#x27;views/business_resource.xml&#x27;, &#x27;security/ir.model.access.csv&#x27;,</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;data/data.xml&#x27;], &#x27;js&#x27;: [], &#x27;assets&#x27;: {}, &#x27;demo&#x27;: [],</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;external_dependencies&#x27;: {}, &#x27;summary&#x27;:</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;The extension to the Universal Appointments app to apply employees as appointment resources&#x27;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    , &#x27;description&#x27;:</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &quot;&quot;&quot;</span>
<span style="color: #e0e0e0;"> For the full details look at static/description/index.html</span>
<span style="color: #e0e0e0;"> * Features *</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-#odootools_proprietary&quot;&quot;&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;images&quot;: [</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &quot;static/description/main.png&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    ],</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;price&quot;: &quot;0.0&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;currency&quot;: &quot;EUR&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-    &quot;live_test_url&quot;: &quot;https://faotools.com/my/tickets/newticket?&amp;url_app_id=136&amp;ticket_version=15.0&amp;url_type_id=3&quot;,</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-}</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+#odootools_proprietary&quot;&quot;&quot;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    , &#x27;images&#x27;: [&#x27;static/description/main.png&#x27;], &#x27;price&#x27;: &#x27;0.0&#x27;, &#x27;currency&#x27;:</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;EUR&#x27;, &#x27;live_test_url&#x27;:</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    &#x27;https://faotools.com/my/tickets/newticket?&amp;url_app_id=136&amp;ticket_version=15.0&amp;url_type_id=3&#x27;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+    }</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        wizard\__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span>
                            <small class="text-muted">+0 -1</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/wizard\__init__.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/wizard\__init__.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1 +0,0 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-# -*- coding: utf-8 -*-</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        __init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span>
                            <small class="text-muted">+0 -2</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/__init__.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/__init__.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1,5 +1,3 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-# -*- coding: utf-8 -*-</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-</span>
<span style="color: #e0e0e0;"> from . import models</span>
<span style="color: #e0e0e0;"> from . import controllers</span>
<span style="color: #e0e0e0;"> from . import reports</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        views\business_resource.xml
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span>
                            <small class="text-muted">+4 -7</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> xml_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/views\business_resource.xml</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/views\business_resource.xml</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1,11 +1,11 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+&lt;?xml version=&#x27;1.0&#x27; encoding=&#x27;UTF-8&#x27;?&gt;</span>
<span style="color: #e0e0e0;"> &lt;odoo&gt;</span>
<span style="color: #e0e0e0;"> </span>
<span style="color: #e0e0e0;">     &lt;record id=&quot;business_resource_view_search&quot; model=&quot;ir.ui.view&quot;&gt;</span>
<span style="color: #e0e0e0;">         &lt;field name=&quot;name&quot;&gt;business.resource.search.hr.bridge&lt;/field&gt;</span>
<span style="color: #e0e0e0;">         &lt;field name=&quot;model&quot;&gt;business.resource&lt;/field&gt;</span>
<span style="color: #e0e0e0;">         &lt;field name=&quot;inherit_id&quot; ref=&quot;business_appointment.business_resource_view_search&quot;/&gt;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &lt;field name=&quot;arch&quot; type=&quot;xml&quot;&gt;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+        &lt;field name=&quot;arch&quot;&gt;</span>
<span style="color: #e0e0e0;">             &lt;field name=&quot;info_info_user_id&quot; position=&quot;after&quot;&gt;</span>
<span style="color: #e0e0e0;">                 &lt;field name=&quot;employee_id&quot;/&gt;</span>
<span style="color: #e0e0e0;">             &lt;/field&gt;</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -24,15 +24,12 @@</span>
<span style="color: #e0e0e0;">         &lt;field name=&quot;name&quot;&gt;business.resource.form.hr.bridge&lt;/field&gt;</span>
<span style="color: #e0e0e0;">         &lt;field name=&quot;model&quot;&gt;business.resource&lt;/field&gt;</span>
<span style="color: #e0e0e0;">         &lt;field name=&quot;inherit_id&quot; ref=&quot;business_appointment.business_resource_view_form&quot;/&gt;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-        &lt;field name=&quot;arch&quot; type=&quot;xml&quot;&gt;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+        &lt;field name=&quot;arch&quot;&gt;</span>
<span style="color: #e0e0e0;">             &lt;field name=&quot;resource_type&quot; position=&quot;attributes&quot;&gt;</span>
<span style="color: #e0e0e0;">                 &lt;attribute name=&quot;invisible&quot;&gt;0&lt;/attribute&gt;</span>
<span style="color: #e0e0e0;">             &lt;/field&gt;</span>
<span style="color: #e0e0e0;">             &lt;field name=&quot;resource_calendar_id&quot; position=&quot;before&quot;&gt;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                &lt;field name=&quot;employee_id&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                       attrs=&quot;{&#x27;invisible&#x27;: [&#x27;|&#x27;, (&#x27;resource_type&#x27;, &#x27;!=&#x27;, &#x27;user&#x27;), (&#x27;main_resource_id&#x27;, &#x27;!=&#x27;, False)]}&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                       options=&quot;{&#x27;no_create_edit&#x27;: 1, &#x27;no_quick_create&#x27;: 1}&quot;</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-                /&gt;</span>
<span style="color: #28a745; background-color: rgba(40, 167, 69, 0.1);">+                &lt;field name=&quot;employee_id&quot; attrs=&quot;{&#x27;invisible&#x27;: [&#x27;|&#x27;, (&#x27;resource_type&#x27;, &#x27;!=&#x27;, &#x27;user&#x27;), (&#x27;main_resource_id&#x27;, &#x27;!=&#x27;, False)]}&quot; options=&quot;{&#x27;no_create_edit&#x27;: 1, &#x27;no_quick_create&#x27;: 1}&quot;/&gt;</span>
<span style="color: #e0e0e0;">             &lt;/field&gt;</span>
<span style="color: #e0e0e0;">             &lt;field name=&quot;resource_calendar_id&quot; position=&quot;attributes&quot;&gt;</span>
<span style="color: #e0e0e0;">                 &lt;attribute name=&quot;attrs&quot;&gt;{&#x27;invisible&#x27;: [&#x27;|&#x27;, (&#x27;employee_id&#x27;, &#x27;!=&#x27;, False), (&#x27;main_resource_id&#x27;, &#x27;!=&#x27;, False)], &#x27;required&#x27;: [(&#x27;employee_id&#x27;, &#x27;=&#x27;, False), (&#x27;main_resource_id&#x27;, &#x27;=&#x27;, False)]}&lt;/attribute&gt;</span>
</pre></div>
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        controllers\__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span>
                            <small class="text-muted">+0 -1</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> python_upgrade, structure_change
                    </div>
                    <div class="diff-container"><pre class="diff-content" style="background-color: #2d2d2d; padding: 15px; border-radius: 5px; overflow-x: auto;"><span style="color: #6c757d; font-weight: bold;">--- original/controllers\__init__.py</span>
<span style="color: #6c757d; font-weight: bold;">+++ upgraded/controllers\__init__.py</span>
<span style="color: #17a2b8; font-weight: bold;">@@ -1 +0,0 @@</span>
<span style="color: #dc3545; background-color: rgba(220, 53, 69, 0.1);">-# -*- coding: utf-8 -*-</span>
</pre></div>
                </div>
            </div>
            
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        