#!/usr/bin/env python3
"""
Comprehensive Testing Script for Odoo Upgrade Engine
Tests all pages, buttons, and core functionality
"""

import requests
import json
import time
import sys
from datetime import datetime

class OdooUpgradeEngineTest:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, status, message="", details=""):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
        if details:
            print(f"   Details: {details}")
    
    def test_page_accessibility(self, path, page_name):
        """Test if a page is accessible and returns 200"""
        try:
            response = self.session.get(f"{self.base_url}{path}")
            if response.status_code == 200:
                self.log_test(f"Page Access: {page_name}", "PASS", f"Page accessible at {path}")
                return True
            else:
                self.log_test(f"Page Access: {page_name}", "FAIL", f"HTTP {response.status_code}", path)
                return False
        except Exception as e:
            self.log_test(f"Page Access: {page_name}", "FAIL", f"Connection error: {str(e)}", path)
            return False
    
    def test_api_endpoint(self, path, method="GET", data=None, expected_status=200):
        """Test API endpoint"""
        try:
            if method == "GET":
                response = self.session.get(f"{self.base_url}{path}")
            elif method == "POST":
                response = self.session.post(f"{self.base_url}{path}", json=data)
            
            if response.status_code == expected_status:
                self.log_test(f"API: {method} {path}", "PASS", f"HTTP {response.status_code}")
                return True, response
            else:
                self.log_test(f"API: {method} {path}", "FAIL", f"HTTP {response.status_code}, expected {expected_status}")
                return False, response
        except Exception as e:
            self.log_test(f"API: {method} {path}", "FAIL", f"Error: {str(e)}")
            return False, None
    
    def test_github_integration(self):
        """Test GitHub integration functionality"""
        print("\n🔍 Testing GitHub Integration...")
        
        # Test repository scanning
        test_repo = "https://github.com/OCA/server-tools"
        scan_data = {"repository_url": test_repo}
        
        success, response = self.test_api_endpoint(
            "/api/github-scan-repository", 
            method="POST", 
            data=scan_data
        )
        
        if success and response:
            try:
                result = response.json()
                if result.get('success'):
                    modules_count = len(result.get('modules', []))
                    self.log_test("GitHub Scan", "PASS", f"Found {modules_count} modules")
                else:
                    self.log_test("GitHub Scan", "FAIL", result.get('error', 'Unknown error'))
            except:
                self.log_test("GitHub Scan", "FAIL", "Invalid JSON response")
        
        # Test module pulling (with smaller test)
        pull_data = {
            "repository_url": test_repo,
            "target_version": "18.0",
            "migration_mode": "pipeline"
        }
        
        success, response = self.test_api_endpoint(
            "/api/github/pull-modules",
            method="POST",
            data=pull_data
        )
        
        if success and response:
            try:
                result = response.json()
                if result.get('success'):
                    self.log_test("GitHub Pull", "PASS", "Module pulling successful")
                else:
                    self.log_test("GitHub Pull", "WARN", result.get('error', 'Pull failed'))
            except:
                self.log_test("GitHub Pull", "FAIL", "Invalid JSON response")
    
    def test_all_pages(self):
        """Test all main pages"""
        print("\n📄 Testing Page Accessibility...")
        
        pages = [
            ('/', 'Dashboard'),
            ('/migration_orchestrator', 'Migration Orchestrator'),
            ('/migration_types', 'Migration Types'),
            ('/migration_jobs', 'Migration Jobs'),
            ('/upload_modules', 'Upload Modules'),
            ('/github_integration', 'GitHub Integration'),
            ('/analyze_modules', 'Analyze Modules'),
            ('/bulk_migration', 'Bulk Migration'),
            ('/automation/', 'Automation Dashboard'),
            ('/contributor_upload', 'Contribute Modules'),
            ('/testing/', 'Testing Dashboard'),
            ('/manual_interventions', 'Manual Interventions'),
            ('/docker_environments', 'Docker Environments'),
            ('/health_dashboard', 'Health Monitor'),
            ('/ai_providers', 'AI Settings'),
        ]
        
        accessible_pages = 0
        for path, name in pages:
            if self.test_page_accessibility(path, name):
                accessible_pages += 1
        
        self.log_test("Overall Page Access", "PASS" if accessible_pages == len(pages) else "WARN", 
                     f"{accessible_pages}/{len(pages)} pages accessible")
    
    def test_ai_providers(self):
        """Test AI provider functionality"""
        print("\n🤖 Testing AI Providers...")
        
        # Test AI provider status endpoint
        success, response = self.test_api_endpoint("/api/ai-provider-status")
        
        if success and response:
            try:
                result = response.json()
                provider_name = result.get('provider_name', 'Unknown')
                is_available = result.get('is_available', False)
                
                status = "PASS" if is_available else "WARN"
                self.log_test("AI Provider Status", status, f"Provider: {provider_name}, Available: {is_available}")
            except:
                self.log_test("AI Provider Status", "FAIL", "Invalid JSON response")
    
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 Starting Comprehensive Odoo Upgrade Engine Tests")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test basic connectivity
        if not self.test_page_accessibility('/', 'Home'):
            print("❌ Cannot connect to application. Make sure it's running on http://localhost:5000")
            return False
        
        # Run all test suites
        self.test_all_pages()
        self.test_github_integration()
        self.test_ai_providers()
        
        # Generate summary
        end_time = time.time()
        duration = end_time - start_time
        
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warnings = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {passed}")
        print(f"⚠️  Warnings: {warnings}")
        print(f"❌ Failed: {failed}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📈 Success Rate: {(passed/(passed+failed+warnings)*100):.1f}%")
        
        return failed == 0

if __name__ == "__main__":
    tester = OdooUpgradeEngineTest()
    success = tester.run_all_tests()
    
    # Save detailed results
    with open('test_results.json', 'w') as f:
        json.dump(tester.test_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to test_results.json")
    
    sys.exit(0 if success else 1)
