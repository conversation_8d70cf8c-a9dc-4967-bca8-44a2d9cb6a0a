#!/usr/bin/env python3
"""
Automation Runner Script
========================

This script serves as the entry point for the automated Odoo module upgrade system.
It can be run manually or triggered by GitHub Actions for continuous integration.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from automation_system import OdooModuleAutomationSystem

def setup_environment():
    """Setup environment variables and paths."""
    # GitHub Actions environment variables
    github_token = os.environ.get('GITHUB_TOKEN')
    batch_size = int(os.environ.get('BATCH_SIZE', '5'))
    target_version = os.environ.get('TARGET_VERSION', '')
    dry_run = os.environ.get('DRY_RUN', 'false').lower() == 'true'
    
    return {
        'github_token': github_token,
        'batch_size': batch_size,
        'target_version': target_version,
        'dry_run': dry_run
    }

def update_config_from_env(config_path: str, env_vars: dict):
    """Update configuration based on environment variables."""
    if not os.path.exists(config_path):
        print(f"Configuration file {config_path} not found, using defaults")
        return
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Update from environment
    if env_vars['github_token']:
        config['github']['token'] = env_vars['github_token']
    
    if env_vars['batch_size']:
        config['processing']['batch_size'] = env_vars['batch_size']
    
    if env_vars['dry_run']:
        config['github']['auto_commit'] = False
        config['processing']['auto_fix_enabled'] = False
        config['processing']['advanced_upgrade_enabled'] = False
    
    # Save updated config
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)

def main():
    """Main automation runner."""
    parser = argparse.ArgumentParser(description='Odoo Module Automation Runner')
    parser.add_argument('--config', default='automation_config.json', 
                       help='Configuration file path')
    parser.add_argument('--mode', choices=['single', 'continuous'], default='single',
                       help='Run mode: single cycle or continuous')
    parser.add_argument('--interval', type=int, default=3600,
                       help='Interval for continuous mode (seconds)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Run without making changes')
    parser.add_argument('--target-version', 
                       help='Specific target version to upgrade to')
    parser.add_argument('--status-only', action='store_true',
                       help='Generate status report only')
    
    args = parser.parse_args()
    
    try:
        # Setup environment
        env_vars = setup_environment()
        
        # Override with command line arguments
        if args.dry_run:
            env_vars['dry_run'] = True
        if args.target_version:
            env_vars['target_version'] = args.target_version
        
        # Update configuration
        update_config_from_env(args.config, env_vars)
        
        # Initialize automation system
        automation = OdooModuleAutomationSystem(args.config)
        
        print("=" * 60)
        print("Odoo Module Automation System")
        print("=" * 60)
        
        if args.status_only:
            print("Generating status report...")
            report = automation.generate_status_report()
            
            print("\nSTATUS REPORT")
            print("-" * 40)
            print(f"Timestamp: {report['timestamp']}")
            print(f"Modules by version: {report['modules_by_version']}")
            print(f"Pending upgrades: {report['pending_upgrades']}")
            print(f"Version chain: {' -> '.join(report['version_chain'])}")
            
            # Save report to file
            with open('automation_status.json', 'w') as f:
                json.dump(report, f, indent=2)
            
            print("\nStatus report saved to: automation_status.json")
            return
        
        if env_vars['dry_run']:
            print("🔍 DRY RUN MODE - No changes will be made")
        
        print(f"Configuration: {args.config}")
        print(f"Mode: {args.mode}")
        print(f"Batch size: {env_vars['batch_size']}")
        
        if env_vars['target_version']:
            print(f"Target version: {env_vars['target_version']}")
        
        print("\nStarting automation...")
        
        if args.mode == 'single':
            automation.run_automation_cycle()
        else:
            automation.run_continuous_automation(args.interval)
        
        print("\n✅ Automation completed successfully")
        
        # Generate final report
        report = automation.generate_status_report()
        with open('automation_final_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print("📊 Final report saved to: automation_final_report.json")
        
    except KeyboardInterrupt:
        print("\n⏹️  Automation stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Automation failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()