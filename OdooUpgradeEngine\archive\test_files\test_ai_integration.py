#!/usr/bin/env python3
"""
Test script for AI provider integration.
Tests the complete multi-provider AI system integration.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_provider_manager():
    """Test AI provider manager initialization and basic functions"""
    try:
        from ai_provider_manager import get_ai_provider_manager, get_available_ai_providers
        
        print("✓ AI provider manager imported successfully")
        
        # Test getting available providers
        providers = get_available_ai_providers()
        print(f"✓ Found {len(providers)} AI providers")
        
        for provider in providers:
            print(f"  - {provider['name']}: {'✓' if provider['available'] else '✗'} ({provider['status']})")
        
        # Test getting manager instance
        manager = get_ai_provider_manager()
        print(f"✓ AI provider manager initialized: {manager.active_provider}")
        
        # Test provider stats
        stats = manager.get_provider_stats()
        print(f"✓ Provider stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ AI provider manager test failed: {e}")
        return False

def test_ai_migration_assistant():
    """Test AI migration assistant integration"""
    try:
        from ai_migration_assistant import AIMigrationAssistant, MigrationContext
        
        print("✓ AI migration assistant imported successfully")
        
        # Test assistant initialization
        assistant = AIMigrationAssistant()
        print(f"✓ AI migration assistant initialized: {assistant.provider_manager is not None}")
        
        # Test context creation
        context = MigrationContext(
            source_version="16.0",
            target_version="17.0",
            module_name="test_module",
            module_files=["__manifest__.py", "models.py"],
            detected_issues=[{"type": "deprecated_api", "severity": "medium"}],
            transformation_results={"python_files": 1, "xml_files": 0}
        )
        
        print(f"✓ Migration context created: {context.module_name}")
        
        # Test context serialization
        context_dict = context.to_dict()
        print(f"✓ Context serialization: {len(context_dict)} keys")
        
        return True
        
    except Exception as e:
        print(f"✗ AI migration assistant test failed: {e}")
        return False

def test_flask_routes():
    """Test Flask routes are properly registered"""
    try:
        from routes import main_blueprint
        
        print("✓ Flask routes imported successfully")
        
        # Check if AI provider routes are registered
        routes = [rule.rule for rule in main_blueprint.url_map.iter_rules()]
        ai_routes = [r for r in routes if 'ai_provider' in r]
        
        print(f"✓ Found {len(ai_routes)} AI provider routes")
        for route in ai_routes:
            print(f"  - {route}")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask routes test failed: {e}")
        return False

def test_template_exists():
    """Test that AI provider template exists"""
    try:
        template_path = "templates/ai_providers.html"
        
        if os.path.exists(template_path):
            print(f"✓ AI provider template exists: {template_path}")
            
            # Check template content
            with open(template_path, 'r') as f:
                content = f.read()
                
            if "AI Provider Configuration" in content:
                print("✓ Template contains correct content")
                return True
            else:
                print("✗ Template missing expected content")
                return False
        else:
            print(f"✗ Template not found: {template_path}")
            return False
            
    except Exception as e:
        print(f"✗ Template test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing AI Provider Integration")
    print("=" * 50)
    
    tests = [
        ("AI Provider Manager", test_ai_provider_manager),
        ("AI Migration Assistant", test_ai_migration_assistant),
        ("Flask Routes", test_flask_routes),
        ("Template Existence", test_template_exists)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! AI provider integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())