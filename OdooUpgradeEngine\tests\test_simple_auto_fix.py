#!/usr/bin/env python3
"""
Simple Auto-Fix Test - Verify counting works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from module_fixer import ModuleFixer
import tempfile
import shutil

def test_counting_system():
    """Test the counting system works correctly"""
    
    # Create test module
    test_module_dir = tempfile.mkdtemp()
    
    # Create test Python file
    test_python_file = os.path.join(test_module_dir, "test_file.py")
    with open(test_python_file, 'w') as f:
        f.write("# Test file\nrecords = self.env['res.partner'].sudo().search([])\n")
    
    print("=== SIMPLE AUTO-FIX TEST ===")
    
    # Test with fresh instance
    fixer = ModuleFixer()
    print(f"Initial state - fixes_applied: {len(fixer.fixes_applied)}")
    print(f"Initial state - success_fixes: {len(fixer.success_fixes)}")
    print(f"Initial state - error_fixes: {len(fixer.error_fixes)}")
    
    # Run fix
    result = fixer.fix_module(test_module_dir, {
        'compatibility_issues': ['Deprecated sudo() usage in test_file.py'],
        'compatibility_warnings': []
    }, target_version="18.0")
    
    print(f"\nAfter fix - fixes_applied: {len(result['fixes_applied'])}")
    print(f"After fix - success_fixes: {len(result['success_fixes'])}")
    print(f"After fix - error_fixes: {len(result['error_fixes'])}")
    
    print(f"\nFixes applied list: {result['fixes_applied']}")
    print(f"Success fixes list: {result['success_fixes']}")
    print(f"Error fixes list: {result['error_fixes']}")
    
    # Check file content
    with open(test_python_file, 'r') as f:
        content = f.read()
    
    print(f"\nFixed file content:")
    print(content)
    print(f"Contains 'with_user': {'with_user' in content}")
    print(f"Contains 'sudo()': {'sudo()' in content}")
    
    # Cleanup
    shutil.rmtree(test_module_dir)
    
    success_count = len(result.get('success_fixes', []))
    return success_count > 0

if __name__ == "__main__":
    success = test_counting_system()
    print(f"\n=== FINAL RESULT ===")
    print(f"Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)