{% extends "base.html" %}
{% set active_page = "docker-environments" %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-0">Docker Environments</h1>
            <p class="text-muted mb-0">Manage multi-version Odoo testing environments</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createEnvironmentModal">
                <i class="fas fa-plus"></i> Create Environment
            </button>
            <button class="btn btn-outline-secondary ms-2" onclick="refreshEnvironments()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4" id="statisticsCards">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="totalEnvironments">-</h4>
                            <p class="card-text">Total Environments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-server fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="runningEnvironments">-</h4>
                            <p class="card-text">Running</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="stoppedEnvironments">-</h4>
                            <p class="card-text">Stopped</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="dockerStatus">-</h4>
                            <p class="card-text">Docker Status</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fab fa-docker fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Versions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Environment Access</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="versionButtons">
                        <!-- Version buttons will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Environments Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Environment Details</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="environmentsTable">
                    <thead>
                        <tr>
                            <th>Version</th>
                            <th>Container</th>
                            <th>Status</th>
                            <th>Port</th>
                            <th>Created</th>
                            <th>Last Used</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="environmentsTableBody">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Environment Modal -->
<div class="modal fade" id="createEnvironmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Docker Environment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createEnvironmentForm">
                    <div class="mb-3">
                        <label for="odooVersion" class="form-label">Odoo Version</label>
                        <select class="form-select" id="odooVersion" required>
                            <option value="">Select Odoo Version</option>
                            <option value="13.0">Odoo 13.0</option>
                            <option value="14.0">Odoo 14.0</option>
                            <option value="15.0">Odoo 15.0</option>
                            <option value="16.0">Odoo 16.0</option>
                            <option value="17.0">Odoo 17.0</option>
                            <option value="18.0">Odoo 18.0</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="forceRecreate">
                            <label class="form-check-label" for="forceRecreate">
                                Force recreate if environment exists
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createEnvironment()">Create Environment</button>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let environments = [];
let statistics = {};

// Load environments on page load
document.addEventListener('DOMContentLoaded', function() {
    loadEnvironments();
    
    // Auto-refresh every 30 seconds
    setInterval(loadEnvironments, 30000);
});

function loadEnvironments() {
    fetch('/api/docker-environments')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                environments = data.environments;
                statistics = data.statistics;
                updateStatistics();
                updateVersionButtons();
                updateEnvironmentsTable();
            } else {
                showAlert('error', 'Failed to load environments: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error loading environments:', error);
            showAlert('error', 'Failed to load environments');
        });
}

function updateStatistics() {
    document.getElementById('totalEnvironments').textContent = statistics.total_environments || 0;
    document.getElementById('runningEnvironments').textContent = statistics.running_environments || 0;
    document.getElementById('stoppedEnvironments').textContent = statistics.stopped_environments || 0;
    document.getElementById('dockerStatus').textContent = statistics.docker_available ? 'Available' : 'Unavailable';
    
    // Update card colors based on Docker availability
    const dockerCard = document.getElementById('dockerStatus').closest('.card');
    if (statistics.docker_available) {
        dockerCard.className = 'card bg-success text-white';
    } else {
        dockerCard.className = 'card bg-danger text-white';
    }
}

function updateVersionButtons() {
    const container = document.getElementById('versionButtons');
    container.innerHTML = '';
    
    const supportedVersions = ['13.0', '14.0', '15.0', '16.0', '17.0', '18.0'];
    
    supportedVersions.forEach(version => {
        const runningEnv = environments.find(env => env.odoo_version === version && env.status === 'running');
        const hasEnvironment = runningEnv !== undefined;
        
        const col = document.createElement('div');
        col.className = 'col-md-2 mb-2';
        
        const button = document.createElement('button');
        button.className = hasEnvironment ? 'btn btn-success w-100' : 'btn btn-outline-secondary w-100';
        button.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>v${version}</span>
                ${hasEnvironment ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-plus"></i>'}
            </div>
            ${hasEnvironment ? `<small>Port ${runningEnv.port}</small>` : '<small>Create</small>'}
        `;
        
        if (hasEnvironment) {
            button.onclick = () => window.open(`http://localhost:${runningEnv.port}`, '_blank');
        } else {
            button.onclick = () => ensureEnvironment(version);
        }
        
        col.appendChild(button);
        container.appendChild(col);
    });
}

function updateEnvironmentsTable() {
    const tbody = document.getElementById('environmentsTableBody');
    tbody.innerHTML = '';
    
    environments.forEach(env => {
        const row = document.createElement('tr');
        
        const statusBadge = getStatusBadge(env.status);
        const createdAt = env.created_at ? new Date(env.created_at).toLocaleDateString() : '-';
        const lastUsed = env.last_used ? new Date(env.last_used).toLocaleDateString() : '-';
        
        row.innerHTML = `
            <td><strong>v${env.odoo_version}</strong></td>
            <td><code>${env.container_name}</code></td>
            <td>${statusBadge}</td>
            <td>${env.port ? `<a href="http://localhost:${env.port}" target="_blank">${env.port}</a>` : '-'}</td>
            <td>${createdAt}</td>
            <td>${lastUsed}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    ${env.status === 'running' ? 
                        `<button class="btn btn-outline-primary" onclick="window.open('http://localhost:${env.port}', '_blank')" title="Open">
                            <i class="fas fa-external-link-alt"></i>
                        </button>` : 
                        `<button class="btn btn-outline-success" onclick="startEnvironment(${env.id})" title="Start">
                            <i class="fas fa-play"></i>
                        </button>`
                    }
                    <button class="btn btn-outline-danger" onclick="removeEnvironment(${env.id})" title="Remove">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    });
    
    if (environments.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="7" class="text-center text-muted">No environments created yet</td>';
        tbody.appendChild(row);
    }
}

function getStatusBadge(status) {
    const badges = {
        'running': '<span class="badge bg-success">Running</span>',
        'stopped': '<span class="badge bg-warning">Stopped</span>',
        'creating': '<span class="badge bg-info">Creating</span>',
        'error': '<span class="badge bg-danger">Error</span>',
        'removed': '<span class="badge bg-secondary">Removed</span>'
    };
    return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
}

function createEnvironment() {
    const version = document.getElementById('odooVersion').value;
    const forceRecreate = document.getElementById('forceRecreate').checked;
    
    if (!version) {
        showAlert('error', 'Please select an Odoo version');
        return;
    }
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('createEnvironmentModal')).hide();
    
    // Show loading message
    showAlert('info', `Creating Odoo ${version} environment...`);
    
    fetch('/api/docker-environments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            odoo_version: version,
            force_recreate: forceRecreate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadEnvironments();
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error creating environment:', error);
        showAlert('error', 'Failed to create environment');
    });
    
    // Reset form
    document.getElementById('createEnvironmentForm').reset();
}

function ensureEnvironment(version) {
    showAlert('info', `Ensuring Odoo ${version} environment...`);
    
    fetch(`/api/ensure-environment/${version}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadEnvironments();
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error ensuring environment:', error);
        showAlert('error', 'Failed to ensure environment');
    });
}

function removeEnvironment(envId) {
    if (!confirm('Are you sure you want to remove this environment? This action cannot be undone.')) {
        return;
    }
    
    fetch(`/api/docker-environments/${envId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadEnvironments();
        } else {
            showAlert('error', data.error);
        }
    })
    .catch(error => {
        console.error('Error removing environment:', error);
        showAlert('error', 'Failed to remove environment');
    });
}

function refreshEnvironments() {
    showAlert('info', 'Refreshing environments...');
    loadEnvironments();
}

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-auto-dismiss');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    const alertHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show alert-auto-dismiss" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHTML);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert-auto-dismiss');
        if (alert) {
            bootstrap.Alert.getInstance(alert)?.close();
        }
    }, 5000);
}
</script>
{% endblock %}