{% extends "base.html" %}
{% set title = "System Settings" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-cogs me-3"></i>
            System Settings
        </h1>
        <p class="lead">Configure system settings, AI providers, and performance options</p>
    </div>
</div>

<!-- Settings Navigation -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="list-group" id="settings-nav">
            <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#general-settings">
                <i class="fas fa-cog me-2"></i> General Settings
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#ai-settings">
                <i class="fas fa-robot me-2"></i> AI Configuration
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#performance-settings">
                <i class="fas fa-tachometer-alt me-2"></i> Performance
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#security-settings">
                <i class="fas fa-shield-alt me-2"></i> Security
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#notification-settings">
                <i class="fas fa-bell me-2"></i> Notifications
            </a>
            <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#backup-settings">
                <i class="fas fa-database me-2"></i> Backup & Recovery
            </a>
        </div>
    </div>
    <div class="col-md-9">
        <div class="tab-content">
            <!-- General Settings -->
            <div class="tab-pane fade show active" id="general-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>
                            General System Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="system-name" class="form-label">System Name</label>
                                    <input type="text" class="form-control" id="system-name" value="Odoo Upgrade Engine">
                                </div>
                                <div class="col-md-6">
                                    <label for="default-version" class="form-label">Default Target Version</label>
                                    <select class="form-select" id="default-version">
                                        <option value="18.0" selected>18.0</option>
                                        <option value="17.0">17.0</option>
                                        <option value="16.0">16.0</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="max-concurrent" class="form-label">Max Concurrent Migrations</label>
                                    <input type="number" class="form-control" id="max-concurrent" value="3" min="1" max="10">
                                </div>
                                <div class="col-md-6">
                                    <label for="timeout" class="form-label">Migration Timeout (hours)</label>
                                    <input type="number" class="form-control" id="timeout" value="6" min="1" max="24">
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto-cleanup" checked>
                                    <label class="form-check-label" for="auto-cleanup">
                                        Auto-cleanup temporary files after migration
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="detailed-logging" checked>
                                    <label class="form-check-label" for="detailed-logging">
                                        Enable detailed logging
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save General Settings</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- AI Settings -->
            <div class="tab-pane fade" id="ai-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-robot me-2"></i>
                            AI Provider Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Configure AI providers for automated migration assistance and analysis.
                        </div>
                        
                        <form>
                            <div class="mb-4">
                                <label class="form-label">Primary AI Provider</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="ai-provider" id="openai" value="openai">
                                            <label class="form-check-label" for="openai">
                                                <strong>OpenAI GPT-4</strong><br>
                                                <small class="text-muted">Premium, high accuracy</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="ai-provider" id="deepseek" value="deepseek" checked>
                                            <label class="form-check-label" for="deepseek">
                                                <strong>DeepSeek</strong><br>
                                                <small class="text-muted">Free, good performance</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="ai-provider" id="ollama" value="ollama">
                                            <label class="form-check-label" for="ollama">
                                                <strong>Ollama (Local)</strong><br>
                                                <small class="text-muted">Free, private, local</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="ai-api-key" class="form-label">API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="ai-api-key" placeholder="Enter API key (if required)">
                                    <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKey()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">Required for OpenAI and some other providers</small>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="ai-model" class="form-label">AI Model</label>
                                    <select class="form-select" id="ai-model">
                                        <option value="gpt-4o">GPT-4o (OpenAI)</option>
                                        <option value="deepseek-chat" selected>DeepSeek Chat</option>
                                        <option value="llama3">Llama 3 (Ollama)</option>
                                        <option value="codellama">Code Llama (Ollama)</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="ai-temperature" class="form-label">AI Temperature</label>
                                    <input type="range" class="form-range" id="ai-temperature" min="0" max="1" step="0.1" value="0.3">
                                    <small class="form-text text-muted">Lower = more focused, Higher = more creative</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ai-auto-approve" checked>
                                    <label class="form-check-label" for="ai-auto-approve">
                                        Auto-approve low-risk migrations (AI confidence > 90%)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ai-suggestions" checked>
                                    <label class="form-check-label" for="ai-suggestions">
                                        Enable AI suggestions for failed migrations
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">Save AI Settings</button>
                            <button type="button" class="btn btn-outline-info" onclick="testAIConnection()">Test Connection</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Performance Settings -->
            <div class="tab-pane fade" id="performance-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Performance Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="worker-processes" class="form-label">Worker Processes</label>
                                    <input type="number" class="form-control" id="worker-processes" value="4" min="1" max="16">
                                    <small class="form-text text-muted">Number of parallel migration workers</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="memory-limit" class="form-label">Memory Limit (GB)</label>
                                    <input type="number" class="form-control" id="memory-limit" value="8" min="2" max="32">
                                    <small class="form-text text-muted">Maximum memory per migration</small>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="cache-size" class="form-label">Cache Size (MB)</label>
                                    <input type="number" class="form-control" id="cache-size" value="512" min="128" max="2048">
                                </div>
                                <div class="col-md-6">
                                    <label for="cleanup-interval" class="form-label">Cleanup Interval (hours)</label>
                                    <input type="number" class="form-control" id="cleanup-interval" value="24" min="1" max="168">
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable-caching" checked>
                                    <label class="form-check-label" for="enable-caching">
                                        Enable result caching
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="parallel-testing" checked>
                                    <label class="form-check-label" for="parallel-testing">
                                        Enable parallel testing
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Performance Settings</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="tab-pane fade" id="security-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Security Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="security-scan" checked>
                                    <label class="form-check-label" for="security-scan">
                                        Enable security scanning for all migrations
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="code-signing" checked>
                                    <label class="form-check-label" for="code-signing">
                                        Require code signing for production deployments
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="audit-logging" checked>
                                    <label class="form-check-label" for="audit-logging">
                                        Enable comprehensive audit logging
                                    </label>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="session-timeout" class="form-label">Session Timeout (minutes)</label>
                                    <input type="number" class="form-control" id="session-timeout" value="60" min="15" max="480">
                                </div>
                                <div class="col-md-6">
                                    <label for="max-login-attempts" class="form-label">Max Login Attempts</label>
                                    <input type="number" class="form-control" id="max-login-attempts" value="5" min="3" max="10">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Security Settings</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="tab-pane fade" id="notification-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bell me-2"></i>
                            Notification Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <label for="email-notifications" class="form-label">Email Notifications</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="migration-complete" checked>
                                    <label class="form-check-label" for="migration-complete">
                                        Migration completion
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="migration-failed" checked>
                                    <label class="form-check-label" for="migration-failed">
                                        Migration failures
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="system-alerts">
                                    <label class="form-check-label" for="system-alerts">
                                        System alerts and warnings
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="notification-email" class="form-label">Notification Email</label>
                                <input type="email" class="form-control" id="notification-email" placeholder="<EMAIL>">
                            </div>
                            <button type="submit" class="btn btn-primary">Save Notification Settings</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Backup Settings -->
            <div class="tab-pane fade" id="backup-settings">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            Backup & Recovery Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto-backup" checked>
                                    <label class="form-check-label" for="auto-backup">
                                        Enable automatic backups before migrations
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="backup-retention" checked>
                                    <label class="form-check-label" for="backup-retention">
                                        Enable backup retention policy
                                    </label>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="backup-frequency" class="form-label">Backup Frequency</label>
                                    <select class="form-select" id="backup-frequency">
                                        <option value="before-migration" selected>Before each migration</option>
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="retention-days" class="form-label">Retention Period (days)</label>
                                    <input type="number" class="form-control" id="retention-days" value="30" min="7" max="365">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Backup Settings</button>
                            <button type="button" class="btn btn-outline-info" onclick="createBackup()">Create Backup Now</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleApiKey() {
    const input = document.getElementById('ai-api-key');
    const icon = event.target.querySelector('i') || event.target;
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function testAIConnection() {
    alert('AI connection test will be implemented in Phase 3');
}

function createBackup() {
    if (confirm('Create a system backup now?')) {
        alert('Backup functionality will be implemented');
    }
}

// Form submission handlers
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Settings save functionality will be implemented');
    });
});
</script>
{% endblock %}
