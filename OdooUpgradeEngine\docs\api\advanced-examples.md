# 🔌 API Reference - Odoo Upgrade Engine

## 📋 **Complete API Documentation**

This document provides comprehensive documentation for all API endpoints in the Odoo Upgrade Engine.

---

## 🌐 **Base URL**
```
http://localhost:5000
```

## 🔐 **Authentication**
Most endpoints are currently open for development. Production deployments should implement proper authentication.

---

## 📊 **Core API Endpoints**

### **1. Health & Status**

#### **GET /health**
Check application health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-14T10:30:00Z",
  "version": "1.0.0-production-ready"
}
```

#### **GET /api/status**
Get detailed system status.

**Response:**
```json
{
  "system": {
    "cpu_usage": 25.5,
    "memory_usage": 45.2,
    "disk_usage": 60.1
  },
  "database": {
    "modules": 45,
    "migration_jobs": 12,
    "active_jobs": 2
  },
  "services": {
    "ai_providers": ["openai", "anthropic"],
    "github_integration": true,
    "background_processing": true
  }
}
```

---

## 🔗 **GitHub Integration API**

### **POST /api/github/pull-modules**
Pull modules from a GitHub repository.

**Request Body:**
```json
{
  "repository_url": "https://github.com/OCA/server-tools",
  "target_version": "18.0",
  "migration_mode": "pipeline",
  "limit": 10
}
```

**Parameters:**
- `repository_url` (required): GitHub repository URL
- `target_version` (required): Target Odoo version (e.g., "18.0")
- `migration_mode` (optional): "direct" or "pipeline" (default: "pipeline")
- `limit` (optional): Maximum number of modules to process

**Response:**
```json
{
  "success": true,
  "repository_url": "https://github.com/OCA/server-tools",
  "modules_detected": 32,
  "modules_processed": 32,
  "modules_failed": 0,
  "modules": [
    {
      "name": "sentry",
      "version": "********.0",
      "migration_job_id": 123,
      "status": "queued"
    }
  ],
  "github_stats": {
    "api_calls": 15,
    "rate_limit_remaining": 4985
  }
}
```

### **POST /api/github/sync-upgraded**
Sync upgraded modules back to GitHub.

**Request Body:**
```json
{
  "repository_url": "https://github.com/user/repo",
  "branch_name": "upgraded_modules_18.0",
  "migration_job_ids": [123, 124, 125],
  "commit_message": "Upgraded modules to Odoo 18.0"
}
```

**Response:**
```json
{
  "success": true,
  "synced_modules": 3,
  "branch_created": true,
  "commit_hash": "abc123def456",
  "github_url": "https://github.com/user/repo/tree/upgraded_modules_18.0"
}
```

---

## 🔄 **Migration API**

### **GET /api/migrations**
List all migration jobs.

**Query Parameters:**
- `status` (optional): Filter by status ("queued", "running", "completed", "failed")
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset

**Response:**
```json
{
  "migrations": [
    {
      "id": 123,
      "module_name": "sentry",
      "status": "completed",
      "target_version": "18.0",
      "created_at": "2025-07-14T10:00:00Z",
      "completed_at": "2025-07-14T10:05:00Z",
      "duration_seconds": 300
    }
  ],
  "total": 45,
  "has_more": true
}
```

### **POST /api/migrations**
Create a new migration job.

**Request Body:**
```json
{
  "module_id": 456,
  "target_version": "18.0",
  "migration_mode": "pipeline",
  "options": {
    "enable_ai": true,
    "auto_fix": true,
    "run_tests": false
  }
}
```

**Response:**
```json
{
  "success": true,
  "migration_job_id": 789,
  "status": "queued",
  "estimated_duration": "5-10 minutes"
}
```

### **GET /api/migrations/{id}**
Get detailed migration job information.

**Response:**
```json
{
  "id": 123,
  "module": {
    "id": 456,
    "name": "sentry",
    "version": "********.0"
  },
  "status": "completed",
  "target_version": "18.0",
  "progress": 100,
  "logs": [
    {
      "timestamp": "2025-07-14T10:01:00Z",
      "level": "info",
      "message": "Starting migration analysis"
    }
  ],
  "results": {
    "files_modified": 12,
    "lines_changed": 245,
    "errors": 0,
    "warnings": 2
  },
  "diff_url": "/api/migrations/123/diff"
}
```

### **GET /api/migrations/{id}/diff**
Get migration diff in various formats.

**Query Parameters:**
- `format` (optional): "html", "json", "unified" (default: "html")

**Response (HTML format):**
```html
<!DOCTYPE html>
<html>
<head><title>Migration Diff - sentry</title></head>
<body>
  <!-- Detailed diff visualization -->
</body>
</html>
```

### **POST /api/migrations/{id}/retry**
Retry a failed migration.

**Response:**
```json
{
  "success": true,
  "new_job_id": 790,
  "message": "Migration retry initiated"
}
```

---

## 📦 **Module Management API**

### **GET /api/modules**
List all modules in the system.

**Query Parameters:**
- `search` (optional): Search by module name
- `version` (optional): Filter by Odoo version
- `status` (optional): Filter by status

**Response:**
```json
{
  "modules": [
    {
      "id": 456,
      "name": "sentry",
      "version": "********.0",
      "description": "Error tracking integration",
      "author": "OCA",
      "depends": ["base", "web"],
      "status": "active",
      "file_path": "/uploads/sentry.zip",
      "created_at": "2025-07-14T09:00:00Z"
    }
  ],
  "total": 45
}
```

### **POST /api/modules/upload**
Upload a new module.

**Request:** Multipart form data with file upload

**Response:**
```json
{
  "success": true,
  "module_id": 457,
  "module_name": "custom_module",
  "message": "Module uploaded successfully"
}
```

### **GET /api/modules/{id}**
Get detailed module information.

**Response:**
```json
{
  "id": 456,
  "name": "sentry",
  "version": "********.0",
  "description": "Error tracking integration",
  "manifest": {
    "name": "Sentry",
    "version": "********.0",
    "depends": ["base", "web"],
    "author": "OCA",
    "license": "AGPL-3"
  },
  "analysis": {
    "complexity": "medium",
    "migration_difficulty": "low",
    "estimated_time": "5 minutes"
  },
  "migrations": [
    {
      "id": 123,
      "target_version": "18.0",
      "status": "completed"
    }
  ]
}
```

---

## 🤖 **AI Integration API**

### **GET /api/ai/providers**
List configured AI providers.

**Response:**
```json
{
  "providers": [
    {
      "name": "openai",
      "status": "active",
      "model": "gpt-4",
      "usage": {
        "requests_today": 45,
        "tokens_used": 12500
      }
    },
    {
      "name": "anthropic",
      "status": "active",
      "model": "claude-3",
      "usage": {
        "requests_today": 23,
        "tokens_used": 8900
      }
    }
  ]
}
```

### **POST /api/ai/analyze**
Analyze code with AI.

**Request Body:**
```json
{
  "code": "class MyModel(models.Model):\n    _name = 'my.model'",
  "context": "odoo_migration",
  "target_version": "18.0",
  "provider": "openai"
}
```

**Response:**
```json
{
  "success": true,
  "analysis": {
    "issues": [
      {
        "type": "deprecation",
        "line": 2,
        "message": "Consider using _name with proper naming convention"
      }
    ],
    "suggestions": [
      {
        "type": "improvement",
        "description": "Add proper docstring and field definitions"
      }
    ],
    "confidence": 0.95
  },
  "provider_used": "openai",
  "tokens_used": 150
}
```

---

## 📊 **Analytics & Reporting API**

### **GET /api/analytics/overview**
Get system analytics overview.

**Response:**
```json
{
  "period": "last_30_days",
  "statistics": {
    "total_migrations": 156,
    "successful_migrations": 142,
    "failed_migrations": 14,
    "success_rate": 91.0,
    "average_duration": 420,
    "modules_processed": 89
  },
  "trends": {
    "migrations_per_day": [5, 8, 12, 6, 9],
    "success_rate_trend": [88, 90, 91, 89, 91]
  }
}
```

### **GET /api/analytics/performance**
Get performance metrics.

**Response:**
```json
{
  "system_performance": {
    "average_response_time": 250,
    "cpu_usage_avg": 35.5,
    "memory_usage_avg": 45.2,
    "disk_io_avg": 12.8
  },
  "migration_performance": {
    "average_migration_time": 420,
    "fastest_migration": 45,
    "slowest_migration": 1800,
    "bottlenecks": ["ai_analysis", "file_processing"]
  }
}
```

---

## 🔧 **Utility Endpoints**

### **POST /api/utils/validate-manifest**
Validate an Odoo manifest file.

**Request Body:**
```json
{
  "manifest_content": "{'name': 'Test Module', 'version': '********.0'}"
}
```

**Response:**
```json
{
  "valid": true,
  "issues": [],
  "suggestions": [
    "Consider adding 'license' field",
    "Add 'author' information"
  ]
}
```

### **GET /api/utils/odoo-versions**
Get supported Odoo versions.

**Response:**
```json
{
  "supported_versions": ["13.0", "14.0", "15.0", "16.0", "17.0", "18.0"],
  "latest_version": "18.0",
  "migration_paths": {
    "15.0": ["16.0", "17.0", "18.0"],
    "16.0": ["17.0", "18.0"],
    "17.0": ["18.0"]
  }
}
```

---

## 📝 **Response Codes**

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

---

## 🔄 **Rate Limiting**

API endpoints are rate-limited to prevent abuse:
- **General endpoints**: 100 requests per minute
- **Migration endpoints**: 10 requests per minute
- **AI endpoints**: 50 requests per hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```

---

**For more information, see:**
- **[Getting Started Guide](../user/getting-started.md)**
- **[Complete Feature List](../../FEATURES.md)**
- **[Developer Documentation](../developer/architecture.md)**
