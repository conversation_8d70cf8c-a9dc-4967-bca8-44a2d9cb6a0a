"""
AI Provider Manager - Multi-Provider AI Integration System

This module provides a unified interface for multiple AI providers including
OpenAI, DeepSeek, and other free/paid alternatives. Users can choose their
preferred AI provider based on cost, features, and availability.

Supported Providers:
- OpenAI GPT-4o (Paid)
- DeepSeek Chat/Reasoner (Free/Cheap)
- OpenRouter (Free tier + Multiple models)
- <PERSON><PERSON><PERSON> Claude (Paid)
- Google Gemini (Free tier)
- Hugging Face Transformers (Free/Local)
- Ollama (Free/Local)
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import requests

# Set up logging
logger = logging.getLogger(__name__)

class AIProviderType(Enum):
    """Enumeration of supported AI providers"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    OPENROUTER = "openrouter"
    ANTHROPIC = "anthropic"
    GEMINI = "gemini"
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"
    FALLBACK = "fallback"

@dataclass
class AIProviderConfig:
    """Configuration for AI provider"""
    provider_type: AIProviderType
    name: str
    base_url: str
    api_key_env: str
    model_name: str
    cost_per_1m_tokens: float
    free_tier: bool
    requires_signup: bool
    description: str
    features: List[str]

class AIProviderManager:
    """
    Manages multiple AI providers and provides unified interface.
    
    This allows users to choose their preferred AI provider based on:
    - Cost (free vs paid)
    - Features (reasoning, coding, etc.)
    - Availability (API keys, local models)
    - Performance requirements
    """
    
    def __init__(self):
        self.providers = self._initialize_providers()
        self.active_provider = None
        self.client = None
        self._load_provider_preferences()
    
    def _initialize_providers(self) -> Dict[AIProviderType, AIProviderConfig]:
        """Initialize all supported AI providers"""
        return {
            AIProviderType.OPENAI: AIProviderConfig(
                provider_type=AIProviderType.OPENAI,
                name="OpenAI GPT-4o",
                base_url="https://api.openai.com/v1",
                api_key_env="OPENAI_API_KEY",
                model_name="gpt-4o",
                cost_per_1m_tokens=15.0,
                free_tier=False,
                requires_signup=True,
                description="Most reliable and capable AI, but paid service",
                features=["General AI", "Coding", "Analysis", "Creative Writing"]
            ),
            
            AIProviderType.DEEPSEEK: AIProviderConfig(
                provider_type=AIProviderType.DEEPSEEK,
                name="DeepSeek Chat/Reasoner",
                base_url="https://api.deepseek.com",
                api_key_env="DEEPSEEK_API_KEY",
                model_name="deepseek-chat",
                cost_per_1m_tokens=0.27,
                free_tier=True,
                requires_signup=True,
                description="Extremely cost-effective, 90%+ cheaper than GPT-4",
                features=["Reasoning", "Coding", "Math", "Analysis", "Free Trial"]
            ),
            
            AIProviderType.OPENROUTER: AIProviderConfig(
                provider_type=AIProviderType.OPENROUTER,
                name="OpenRouter (Multi-Model)",
                base_url="https://openrouter.ai/api/v1",
                api_key_env="OPENROUTER_API_KEY",
                model_name="deepseek/deepseek-r1:free",
                cost_per_1m_tokens=0.0,
                free_tier=True,
                requires_signup=True,
                description="Free access to multiple AI models including DeepSeek",
                features=["Multiple Models", "Free Tier", "DeepSeek Access"]
            ),
            
            AIProviderType.ANTHROPIC: AIProviderConfig(
                provider_type=AIProviderType.ANTHROPIC,
                name="Anthropic Claude",
                base_url="https://api.anthropic.com",
                api_key_env="ANTHROPIC_API_KEY",
                model_name="claude-3-5-sonnet-20241022",
                cost_per_1m_tokens=3.0,
                free_tier=False,
                requires_signup=True,
                description="Excellent for document analysis and coding",
                features=["Document Analysis", "Coding", "Safety", "Long Context"]
            ),
            
            AIProviderType.GEMINI: AIProviderConfig(
                provider_type=AIProviderType.GEMINI,
                name="Google Gemini",
                base_url="https://generativelanguage.googleapis.com",
                api_key_env="GEMINI_API_KEY",
                model_name="gemini-pro",
                cost_per_1m_tokens=0.5,
                free_tier=True,
                requires_signup=True,
                description="Free tier available, good for general tasks",
                features=["Free Tier", "Google Integration", "Multimodal"]
            ),
            
            AIProviderType.OLLAMA: AIProviderConfig(
                provider_type=AIProviderType.OLLAMA,
                name="Ollama (Local)",
                base_url="http://localhost:11434",
                api_key_env="",
                model_name="llama3.2",
                cost_per_1m_tokens=0.0,
                free_tier=True,
                requires_signup=False,
                description="Free local AI models, no API key required",
                features=["Completely Free", "Local", "Privacy", "No API Key"]
            ),
            
            AIProviderType.FALLBACK: AIProviderConfig(
                provider_type=AIProviderType.FALLBACK,
                name="Fallback Analysis",
                base_url="",
                api_key_env="",
                model_name="rule-based",
                cost_per_1m_tokens=0.0,
                free_tier=True,
                requires_signup=False,
                description="Rule-based analysis when no AI is available",
                features=["Always Available", "No Dependencies", "Fast"]
            )
        }
    
    def _load_provider_preferences(self):
        """Load user's AI provider preferences"""
        try:
            if os.path.exists('ai_provider_config.json'):
                with open('ai_provider_config.json', 'r') as f:
                    config = json.load(f)
                    preferred_provider = config.get('preferred_provider', 'deepseek')
                    self.set_active_provider(AIProviderType(preferred_provider))
            else:
                # Default to DeepSeek as it's free and powerful
                self.set_active_provider(AIProviderType.DEEPSEEK)
        except Exception as e:
            logger.warning(f"Failed to load provider preferences: {e}")
            self.set_active_provider(AIProviderType.DEEPSEEK)
    
    def save_provider_preferences(self, provider_type: AIProviderType):
        """Save user's AI provider preferences"""
        try:
            config = {
                'preferred_provider': provider_type.value,
                'last_updated': str(datetime.now())
            }
            with open('ai_provider_config.json', 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save provider preferences: {e}")
    
    def get_available_providers(self) -> List[Dict[str, Any]]:
        """Get list of available AI providers with their status"""
        providers = []
        
        for provider_type, config in self.providers.items():
            status = self._check_provider_availability(provider_type)
            providers.append({
                'type': provider_type.value,
                'name': config.name,
                'cost_per_1m_tokens': config.cost_per_1m_tokens,
                'free_tier': config.free_tier,
                'description': config.description,
                'features': config.features,
                'available': status['available'],
                'status': status['message']
            })
        
        return sorted(providers, key=lambda x: x['cost_per_1m_tokens'])
    
    def _check_provider_availability(self, provider_type: AIProviderType) -> Dict[str, Any]:
        """Check if a provider is available and configured"""
        config = self.providers[provider_type]
        
        # Check if API key is available (if required)
        if config.api_key_env and not os.getenv(config.api_key_env):
            return {
                'available': False,
                'message': f'API key {config.api_key_env} not configured'
            }
        
        # Special checks for specific providers
        if provider_type == AIProviderType.OLLAMA:
            try:
                response = requests.get(f"{config.base_url}/api/version", timeout=5)
                if response.status_code == 200:
                    return {'available': True, 'message': 'Ollama server running'}
                else:
                    return {'available': False, 'message': 'Ollama server not running'}
            except:
                return {'available': False, 'message': 'Ollama not installed or not running'}
        
        if provider_type == AIProviderType.FALLBACK:
            return {'available': True, 'message': 'Always available'}
        
        return {'available': True, 'message': 'API key configured'}
    
    def set_active_provider(self, provider_type: AIProviderType) -> bool:
        """Set the active AI provider"""
        if provider_type not in self.providers:
            logger.error(f"Unknown provider type: {provider_type}")
            return False
        
        config = self.providers[provider_type]
        
        # Check availability
        status = self._check_provider_availability(provider_type)
        if not status['available']:
            logger.warning(f"Provider {config.name} not available: {status['message']}")
            return False
        
        # Initialize client for the provider
        try:
            self.client = self._initialize_client(provider_type)
            self.active_provider = provider_type
            self.save_provider_preferences(provider_type)
            logger.info(f"Active AI provider set to: {config.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize {config.name}: {e}")
            return False
    
    def _initialize_client(self, provider_type: AIProviderType):
        """Initialize API client for the provider"""
        config = self.providers[provider_type]
        
        if provider_type == AIProviderType.FALLBACK:
            return None  # No client needed for fallback
        
        if provider_type == AIProviderType.OLLAMA:
            # Ollama uses its own client
            try:
                import ollama
                return ollama.Client(host=config.base_url)
            except ImportError:
                logger.error("Ollama client not installed. Run: pip install ollama")
                return None
        
        if provider_type == AIProviderType.GEMINI:
            # Google Gemini uses different client
            try:
                import google.generativeai as genai
                genai.configure(api_key=os.getenv(config.api_key_env))
                return genai
            except ImportError:
                logger.error("Google AI client not installed. Run: pip install google-generativeai")
                return None
        
        if provider_type == AIProviderType.ANTHROPIC:
            # Anthropic uses different client
            try:
                import anthropic
                return anthropic.Anthropic(api_key=os.getenv(config.api_key_env))
            except ImportError:
                logger.error("Anthropic client not installed. Run: pip install anthropic")
                return None
        
        # For OpenAI-compatible providers (OpenAI, DeepSeek, OpenRouter)
        try:
            from openai import OpenAI
            return OpenAI(
                api_key=os.getenv(config.api_key_env),
                base_url=config.base_url
            )
        except ImportError:
            logger.error("OpenAI client not installed. Run: pip install openai")
            return None
    
    def generate_response(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Generate AI response using the active provider"""
        if not self.active_provider:
            logger.error("No active AI provider set")
            return self._fallback_response(prompt, system_prompt)
        
        config = self.providers[self.active_provider]
        
        try:
            if self.active_provider == AIProviderType.FALLBACK:
                return self._fallback_response(prompt, system_prompt)
            
            elif self.active_provider == AIProviderType.OLLAMA:
                return self._ollama_response(prompt, system_prompt, **kwargs)
            
            elif self.active_provider == AIProviderType.GEMINI:
                return self._gemini_response(prompt, system_prompt, **kwargs)
            
            elif self.active_provider == AIProviderType.ANTHROPIC:
                return self._anthropic_response(prompt, system_prompt, **kwargs)
            
            else:
                # OpenAI-compatible providers
                return self._openai_compatible_response(prompt, system_prompt, **kwargs)
        
        except Exception as e:
            logger.error(f"AI generation failed with {config.name}: {e}")
            return self._fallback_response(prompt, system_prompt)
    
    def _openai_compatible_response(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Generate response using OpenAI-compatible API"""
        config = self.providers[self.active_provider]
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        response = self.client.chat.completions.create(
            model=config.model_name,
            messages=messages,
            response_format=kwargs.get("response_format", None),
            temperature=kwargs.get("temperature", 0.7),
            max_tokens=kwargs.get("max_tokens", 2000)
        )
        
        return {
            'success': True,
            'content': response.choices[0].message.content,
            'provider': config.name,
            'model': config.model_name,
            'cost_estimate': self._calculate_cost(response.usage.total_tokens if hasattr(response, 'usage') else 1000)
        }
    
    def _ollama_response(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Generate response using Ollama local models"""
        config = self.providers[self.active_provider]
        
        full_prompt = f"{system_prompt}\n\n{prompt}" if system_prompt else prompt
        
        response = self.client.generate(
            model=config.model_name,
            prompt=full_prompt,
            options={
                'temperature': kwargs.get('temperature', 0.7),
                'num_predict': kwargs.get('max_tokens', 2000)
            }
        )
        
        return {
            'success': True,
            'content': response['response'],
            'provider': config.name,
            'model': config.model_name,
            'cost_estimate': 0.0  # Local models are free
        }
    
    def _gemini_response(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Generate response using Google Gemini"""
        config = self.providers[self.active_provider]
        
        model = self.client.GenerativeModel(config.model_name)
        
        full_prompt = f"{system_prompt}\n\n{prompt}" if system_prompt else prompt
        response = model.generate_content(full_prompt)
        
        return {
            'success': True,
            'content': response.text,
            'provider': config.name,
            'model': config.model_name,
            'cost_estimate': self._calculate_cost(len(full_prompt.split()) * 1.3)  # Rough estimate
        }
    
    def _anthropic_response(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Generate response using Anthropic Claude"""
        config = self.providers[self.active_provider]
        
        response = self.client.messages.create(
            model=config.model_name,
            max_tokens=kwargs.get('max_tokens', 2000),
            temperature=kwargs.get('temperature', 0.7),
            system=system_prompt or "You are a helpful assistant",
            messages=[{"role": "user", "content": prompt}]
        )
        
        return {
            'success': True,
            'content': response.content[0].text,
            'provider': config.name,
            'model': config.model_name,
            'cost_estimate': self._calculate_cost(response.usage.input_tokens + response.usage.output_tokens)
        }
    
    def _fallback_response(self, prompt: str, system_prompt: str = None) -> Dict[str, Any]:
        """Generate fallback response when no AI is available"""
        return {
            'success': True,
            'content': "AI analysis not available. Using rule-based analysis with basic recommendations.",
            'provider': "Fallback Analysis",
            'model': "rule-based",
            'cost_estimate': 0.0
        }
    
    def _calculate_cost(self, tokens: int) -> float:
        """Calculate estimated cost for the request"""
        if not self.active_provider:
            return 0.0
        
        config = self.providers[self.active_provider]
        return (tokens / 1000000) * config.cost_per_1m_tokens
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """Get statistics about provider usage"""
        if not self.active_provider:
            return {'error': 'No active provider'}
        
        config = self.providers[self.active_provider]
        return {
            'active_provider': config.name,
            'model': config.model_name,
            'cost_per_1m_tokens': config.cost_per_1m_tokens,
            'free_tier': config.free_tier,
            'features': config.features
        }

# Global instance
ai_provider_manager = AIProviderManager()

def get_ai_provider_manager() -> AIProviderManager:
    """Get the global AI provider manager instance"""
    return ai_provider_manager

# Convenience functions for migration assistant
def analyze_with_ai(prompt: str, system_prompt: str = None) -> Dict[str, Any]:
    """Analyze using the active AI provider"""
    return ai_provider_manager.generate_response(prompt, system_prompt)

def get_available_ai_providers() -> List[Dict[str, Any]]:
    """Get list of available AI providers"""
    return ai_provider_manager.get_available_providers()

def set_ai_provider(provider_name: str) -> bool:
    """Set active AI provider by name"""
    try:
        provider_type = AIProviderType(provider_name.lower())
        return ai_provider_manager.set_active_provider(provider_type)
    except ValueError:
        logger.error(f"Unknown provider: {provider_name}")
        return False

    def generate_migration_analysis(self, module_data: Dict[str, Any], target_version: str = "18.0") -> Dict[str, Any]:
        """Generate comprehensive migration analysis for a module"""
        try:
            # Prepare analysis prompt
            analysis_prompt = f"""
            Analyze this Odoo module for migration from version {module_data.get('current_version', 'unknown')} to {target_version}.

            Module Information:
            - Name: {module_data.get('name', 'Unknown')}
            - Description: {module_data.get('description', 'No description')}
            - Dependencies: {module_data.get('depends', [])}
            - Current Version: {module_data.get('current_version', 'unknown')}

            Code Analysis:
            {module_data.get('code_content', 'No code provided')}

            Please provide:
            1. Compatibility assessment
            2. Required changes for migration
            3. Potential breaking changes
            4. Recommended migration steps
            5. Risk assessment (LOW/MEDIUM/HIGH)
            6. Estimated effort (hours)
            """

            # Get AI response
            ai_response = self.get_ai_response(analysis_prompt, "migration_analysis")

            if ai_response and ai_response.get('success'):
                # Parse and structure the response
                analysis_result = {
                    'success': True,
                    'module_name': module_data.get('name'),
                    'target_version': target_version,
                    'analysis': ai_response.get('response', ''),
                    'compatibility_score': self._extract_compatibility_score(ai_response.get('response', '')),
                    'risk_level': self._extract_risk_level(ai_response.get('response', '')),
                    'estimated_effort': self._extract_effort_estimate(ai_response.get('response', '')),
                    'breaking_changes': self._extract_breaking_changes(ai_response.get('response', '')),
                    'migration_steps': self._extract_migration_steps(ai_response.get('response', '')),
                    'timestamp': datetime.now().isoformat()
                }

                # Record interaction for learning
                if hasattr(self, 'learning_system'):
                    self.learning_system.record_ai_interaction(
                        provider=self.current_provider,
                        model=self.current_model,
                        input_type='migration_analysis',
                        input_data=module_data,
                        ai_response=analysis_result
                    )

                return analysis_result
            else:
                return {
                    'success': False,
                    'error': 'Failed to generate AI analysis',
                    'module_name': module_data.get('name'),
                    'target_version': target_version
                }

        except Exception as e:
            logger.error(f"Error generating migration analysis: {e}")
            return {
                'success': False,
                'error': str(e),
                'module_name': module_data.get('name'),
                'target_version': target_version
            }

    def _extract_compatibility_score(self, analysis_text: str) -> float:
        """Extract compatibility score from analysis text"""
        import re
        # Look for percentage or score patterns
        score_patterns = [
            r'compatibility[:\s]*(\d+)%',
            r'score[:\s]*(\d+)%',
            r'(\d+)%\s*compatible'
        ]

        for pattern in score_patterns:
            match = re.search(pattern, analysis_text.lower())
            if match:
                return float(match.group(1)) / 100.0

        # Default score based on risk level
        if 'low' in analysis_text.lower():
            return 0.8
        elif 'medium' in analysis_text.lower():
            return 0.6
        elif 'high' in analysis_text.lower():
            return 0.3
        else:
            return 0.5

    def _extract_risk_level(self, analysis_text: str) -> str:
        """Extract risk level from analysis text"""
        text_lower = analysis_text.lower()
        if 'high risk' in text_lower or 'risk: high' in text_lower:
            return 'HIGH'
        elif 'medium risk' in text_lower or 'risk: medium' in text_lower:
            return 'MEDIUM'
        elif 'low risk' in text_lower or 'risk: low' in text_lower:
            return 'LOW'
        else:
            return 'MEDIUM'  # Default

    def _extract_effort_estimate(self, analysis_text: str) -> int:
        """Extract effort estimate in hours from analysis text"""
        import re
        # Look for hour patterns
        hour_patterns = [
            r'(\d+)\s*hours?',
            r'effort[:\s]*(\d+)',
            r'estimate[:\s]*(\d+)'
        ]

        for pattern in hour_patterns:
            match = re.search(pattern, analysis_text.lower())
            if match:
                return int(match.group(1))

        # Default estimates based on risk
        if 'high' in analysis_text.lower():
            return 40
        elif 'medium' in analysis_text.lower():
            return 20
        else:
            return 8

    def _extract_breaking_changes(self, analysis_text: str) -> List[str]:
        """Extract breaking changes from analysis text"""
        breaking_changes = []
        lines = analysis_text.split('\n')

        in_breaking_section = False
        for line in lines:
            line = line.strip()
            if 'breaking change' in line.lower() or 'breaking:' in line.lower():
                in_breaking_section = True
                continue
            elif in_breaking_section and line.startswith('-'):
                breaking_changes.append(line[1:].strip())
            elif in_breaking_section and line and not line.startswith('-'):
                in_breaking_section = False

        return breaking_changes[:5]  # Limit to 5 most important

    def _extract_migration_steps(self, analysis_text: str) -> List[str]:
        """Extract migration steps from analysis text"""
        migration_steps = []
        lines = analysis_text.split('\n')

        in_steps_section = False
        for line in lines:
            line = line.strip()
            if 'migration step' in line.lower() or 'steps:' in line.lower():
                in_steps_section = True
                continue
            elif in_steps_section and (line.startswith('-') or line.startswith('1.') or line.startswith('2.')):
                migration_steps.append(line.lstrip('-123456789. ').strip())
            elif in_steps_section and line and not any(line.startswith(x) for x in ['-', '1', '2', '3', '4', '5']):
                in_steps_section = False

        return migration_steps[:8]  # Limit to 8 steps

    def test_deepseek_connection(self, api_key: str, model: str = "deepseek-chat") -> Dict[str, Any]:
        """Test DeepSeek API connection"""
        try:
            import time
            start_time = time.time()

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model,
                "messages": [{"role": "user", "content": "Hello, this is a connection test."}],
                "max_tokens": 50
            }

            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'model_info': {
                        'model': model,
                        'provider': 'deepseek'
                    },
                    'response_time': response_time,
                    'test_response': result.get('choices', [{}])[0].get('message', {}).get('content', '')
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def test_openai_connection(self, api_key: str, model: str = "gpt-4o-mini") -> Dict[str, Any]:
        """Test OpenAI API connection"""
        try:
            import time
            start_time = time.time()

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model,
                "messages": [{"role": "user", "content": "Hello, this is a connection test."}],
                "max_tokens": 50
            }

            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'model_info': {
                        'model': model,
                        'provider': 'openai'
                    },
                    'response_time': response_time,
                    'test_response': result.get('choices', [{}])[0].get('message', {}).get('content', '')
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def test_ollama_connection(self, url: str = "http://localhost:11434", model: str = "llama2") -> Dict[str, Any]:
        """Test Ollama local connection"""
        try:
            import time
            start_time = time.time()

            # First check if Ollama is running
            health_response = requests.get(f"{url}/api/tags", timeout=10)

            if health_response.status_code != 200:
                return {
                    'success': False,
                    'error': f"Ollama server not accessible at {url}"
                }

            # Test model availability
            data = {
                "model": model,
                "prompt": "Hello, this is a connection test.",
                "stream": False
            }

            response = requests.post(
                f"{url}/api/generate",
                json=data,
                timeout=30
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'model_info': {
                        'model': model,
                        'provider': 'ollama',
                        'url': url
                    },
                    'response_time': response_time,
                    'test_response': result.get('response', '')
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }

        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': f"Cannot connect to Ollama at {url}. Make sure Ollama is running."
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

if __name__ == "__main__":
    # Test the AI provider manager
    manager = AIProviderManager()
    
    print("Available AI Providers:")
    for provider in manager.get_available_providers():
        print(f"- {provider['name']}: ${provider['cost_per_1m_tokens']:.2f}/1M tokens")
        print(f"  Free tier: {provider['free_tier']}, Available: {provider['available']}")
        print(f"  Features: {', '.join(provider['features'])}")
        print()
    
    # Test with a simple prompt
    response = manager.generate_response("What is 2+2?", "You are a helpful math assistant")
    print(f"Test response: {response}")