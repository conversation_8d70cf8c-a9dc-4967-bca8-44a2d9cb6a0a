"""
AI Learning System for Odoo Upgrade Engine
Tracks AI performance, learns from human corrections, and improves suggestions over time.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class AIInteraction:
    """Represents a single AI interaction"""
    id: str
    timestamp: datetime
    provider: str
    model: str
    input_type: str  # 'migration_analysis', 'failure_analysis', 'suggestion'
    input_data: Dict[str, Any]
    ai_response: Dict[str, Any]
    human_feedback: Optional[Dict[str, Any]] = None
    accuracy_score: Optional[float] = None
    was_helpful: Optional[bool] = None
    correction_applied: bool = False

@dataclass
class AIMetrics:
    """AI performance metrics"""
    provider: str
    model: str
    total_interactions: int
    successful_interactions: int
    average_accuracy: float
    helpfulness_rate: float
    correction_rate: float
    response_time_avg: float
    last_updated: datetime

class AILearningSystem:
    """
    AI Learning System that tracks performance and learns from human feedback
    """
    
    def __init__(self, data_dir: str = "ai_learning_data"):
        self.data_dir = data_dir
        self.interactions_file = os.path.join(data_dir, "interactions.jsonl")
        self.metrics_file = os.path.join(data_dir, "metrics.json")
        self.patterns_file = os.path.join(data_dir, "learned_patterns.json")
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Load existing data
        self.interactions: List[AIInteraction] = self._load_interactions()
        self.metrics: Dict[str, AIMetrics] = self._load_metrics()
        self.learned_patterns: Dict[str, Any] = self._load_patterns()
    
    def record_ai_interaction(self, 
                            provider: str,
                            model: str,
                            input_type: str,
                            input_data: Dict[str, Any],
                            ai_response: Dict[str, Any],
                            response_time: float = 0.0) -> str:
        """Record a new AI interaction"""
        
        interaction_id = f"{provider}_{model}_{datetime.now().isoformat()}_{len(self.interactions)}"
        
        interaction = AIInteraction(
            id=interaction_id,
            timestamp=datetime.now(),
            provider=provider,
            model=model,
            input_type=input_type,
            input_data=input_data,
            ai_response=ai_response
        )
        
        self.interactions.append(interaction)
        self._save_interaction(interaction)
        
        # Update metrics
        self._update_metrics(provider, model, response_time)
        
        return interaction_id
    
    def record_human_feedback(self,
                            interaction_id: str,
                            was_helpful: bool,
                            accuracy_score: float,
                            feedback_notes: str = "",
                            correction_data: Optional[Dict[str, Any]] = None):
        """Record human feedback for an AI interaction"""
        
        # Find the interaction
        interaction = None
        for i in self.interactions:
            if i.id == interaction_id:
                interaction = i
                break
        
        if not interaction:
            logger.error(f"Interaction {interaction_id} not found")
            return
        
        # Update interaction with feedback
        interaction.human_feedback = {
            'was_helpful': was_helpful,
            'accuracy_score': accuracy_score,
            'feedback_notes': feedback_notes,
            'correction_data': correction_data,
            'feedback_timestamp': datetime.now().isoformat()
        }
        interaction.was_helpful = was_helpful
        interaction.accuracy_score = accuracy_score
        interaction.correction_applied = correction_data is not None
        
        # Learn from the feedback
        self._learn_from_feedback(interaction)
        
        # Update metrics
        self._update_metrics(interaction.provider, interaction.model)
        
        # Save updated data
        self._save_all_interactions()
        
        logger.info(f"Recorded feedback for interaction {interaction_id}")
    
    def get_ai_suggestions_with_learning(self,
                                       provider: str,
                                       model: str,
                                       input_type: str,
                                       input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get AI suggestions enhanced with learned patterns"""
        
        # Get base suggestions from learned patterns
        learned_suggestions = self._get_learned_suggestions(input_type, input_data)
        
        # Get provider-specific improvements
        provider_improvements = self._get_provider_improvements(provider, model, input_type)
        
        return {
            'learned_suggestions': learned_suggestions,
            'provider_improvements': provider_improvements,
            'confidence_adjustments': self._get_confidence_adjustments(provider, model, input_type)
        }
    
    def get_provider_performance(self, provider: str = None) -> Dict[str, Any]:
        """Get performance metrics for AI providers"""
        
        if provider:
            return {k: asdict(v) for k, v in self.metrics.items() if k.startswith(provider)}
        else:
            return {k: asdict(v) for k, v in self.metrics.items()}
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from the learning system"""
        
        total_interactions = len(self.interactions)
        if total_interactions == 0:
            return {'message': 'No interactions recorded yet'}
        
        # Calculate overall statistics
        helpful_count = sum(1 for i in self.interactions if i.was_helpful)
        accuracy_scores = [i.accuracy_score for i in self.interactions if i.accuracy_score is not None]
        corrections_count = sum(1 for i in self.interactions if i.correction_applied)
        
        # Provider comparison
        provider_stats = defaultdict(lambda: {'count': 0, 'helpful': 0, 'accuracy_sum': 0, 'accuracy_count': 0})
        
        for interaction in self.interactions:
            key = f"{interaction.provider}_{interaction.model}"
            provider_stats[key]['count'] += 1
            if interaction.was_helpful:
                provider_stats[key]['helpful'] += 1
            if interaction.accuracy_score is not None:
                provider_stats[key]['accuracy_sum'] += interaction.accuracy_score
                provider_stats[key]['accuracy_count'] += 1
        
        # Most common issues and patterns
        common_patterns = self._analyze_common_patterns()
        
        return {
            'total_interactions': total_interactions,
            'helpfulness_rate': helpful_count / total_interactions if total_interactions > 0 else 0,
            'average_accuracy': sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0,
            'correction_rate': corrections_count / total_interactions if total_interactions > 0 else 0,
            'provider_comparison': {
                k: {
                    'count': v['count'],
                    'helpfulness_rate': v['helpful'] / v['count'] if v['count'] > 0 else 0,
                    'average_accuracy': v['accuracy_sum'] / v['accuracy_count'] if v['accuracy_count'] > 0 else 0
                }
                for k, v in provider_stats.items()
            },
            'common_patterns': common_patterns,
            'learned_patterns_count': len(self.learned_patterns)
        }
    
    def _load_interactions(self) -> List[AIInteraction]:
        """Load interactions from file"""
        interactions = []
        
        if os.path.exists(self.interactions_file):
            try:
                with open(self.interactions_file, 'r') as f:
                    for line in f:
                        data = json.loads(line.strip())
                        # Convert timestamp string back to datetime
                        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
                        interactions.append(AIInteraction(**data))
            except Exception as e:
                logger.error(f"Error loading interactions: {e}")
        
        return interactions
    
    def _save_interaction(self, interaction: AIInteraction):
        """Save a single interaction to file"""
        try:
            with open(self.interactions_file, 'a') as f:
                data = asdict(interaction)
                data['timestamp'] = interaction.timestamp.isoformat()
                f.write(json.dumps(data) + '\n')
        except Exception as e:
            logger.error(f"Error saving interaction: {e}")
    
    def _save_all_interactions(self):
        """Save all interactions to file"""
        try:
            with open(self.interactions_file, 'w') as f:
                for interaction in self.interactions:
                    data = asdict(interaction)
                    data['timestamp'] = interaction.timestamp.isoformat()
                    f.write(json.dumps(data) + '\n')
        except Exception as e:
            logger.error(f"Error saving all interactions: {e}")
    
    def _load_metrics(self) -> Dict[str, AIMetrics]:
        """Load metrics from file"""
        metrics = {}
        
        if os.path.exists(self.metrics_file):
            try:
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    for key, value in data.items():
                        value['last_updated'] = datetime.fromisoformat(value['last_updated'])
                        metrics[key] = AIMetrics(**value)
            except Exception as e:
                logger.error(f"Error loading metrics: {e}")
        
        return metrics
    
    def _save_metrics(self):
        """Save metrics to file"""
        try:
            data = {}
            for key, metric in self.metrics.items():
                metric_dict = asdict(metric)
                metric_dict['last_updated'] = metric.last_updated.isoformat()
                data[key] = metric_dict
            
            with open(self.metrics_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving metrics: {e}")
    
    def _load_patterns(self) -> Dict[str, Any]:
        """Load learned patterns from file"""
        patterns = {}
        
        if os.path.exists(self.patterns_file):
            try:
                with open(self.patterns_file, 'r') as f:
                    patterns = json.load(f)
            except Exception as e:
                logger.error(f"Error loading patterns: {e}")
        
        return patterns
    
    def _save_patterns(self):
        """Save learned patterns to file"""
        try:
            with open(self.patterns_file, 'w') as f:
                json.dump(self.learned_patterns, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving patterns: {e}")
    
    def _update_metrics(self, provider: str, model: str, response_time: float = 0.0):
        """Update metrics for a provider/model combination"""
        key = f"{provider}_{model}"
        
        if key not in self.metrics:
            self.metrics[key] = AIMetrics(
                provider=provider,
                model=model,
                total_interactions=0,
                successful_interactions=0,
                average_accuracy=0.0,
                helpfulness_rate=0.0,
                correction_rate=0.0,
                response_time_avg=0.0,
                last_updated=datetime.now()
            )
        
        metric = self.metrics[key]
        
        # Update counts
        metric.total_interactions += 1
        
        # Calculate rates from interactions
        provider_interactions = [i for i in self.interactions if i.provider == provider and i.model == model]
        
        helpful_count = sum(1 for i in provider_interactions if i.was_helpful)
        accuracy_scores = [i.accuracy_score for i in provider_interactions if i.accuracy_score is not None]
        corrections_count = sum(1 for i in provider_interactions if i.correction_applied)
        
        metric.helpfulness_rate = helpful_count / len(provider_interactions) if provider_interactions else 0
        metric.average_accuracy = sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0
        metric.correction_rate = corrections_count / len(provider_interactions) if provider_interactions else 0
        
        # Update response time (simple moving average)
        if response_time > 0:
            if metric.response_time_avg == 0:
                metric.response_time_avg = response_time
            else:
                metric.response_time_avg = (metric.response_time_avg * 0.8) + (response_time * 0.2)
        
        metric.last_updated = datetime.now()
        
        self._save_metrics()
    
    def _learn_from_feedback(self, interaction: AIInteraction):
        """Learn patterns from human feedback"""
        
        if not interaction.human_feedback:
            return
        
        input_type = interaction.input_type
        was_helpful = interaction.was_helpful
        accuracy_score = interaction.accuracy_score
        
        # Initialize pattern storage for this input type
        if input_type not in self.learned_patterns:
            self.learned_patterns[input_type] = {
                'successful_patterns': [],
                'failed_patterns': [],
                'common_corrections': [],
                'accuracy_thresholds': {}
            }
        
        patterns = self.learned_patterns[input_type]
        
        # Store successful patterns
        if was_helpful and accuracy_score and accuracy_score > 0.7:
            pattern = {
                'input_features': self._extract_features(interaction.input_data),
                'successful_response': interaction.ai_response,
                'accuracy_score': accuracy_score,
                'provider': interaction.provider,
                'model': interaction.model
            }
            patterns['successful_patterns'].append(pattern)
        
        # Store failed patterns
        elif not was_helpful or (accuracy_score and accuracy_score < 0.5):
            pattern = {
                'input_features': self._extract_features(interaction.input_data),
                'failed_response': interaction.ai_response,
                'accuracy_score': accuracy_score or 0,
                'provider': interaction.provider,
                'model': interaction.model
            }
            patterns['failed_patterns'].append(pattern)
        
        # Store corrections
        if interaction.correction_applied and interaction.human_feedback.get('correction_data'):
            correction = {
                'original_response': interaction.ai_response,
                'corrected_response': interaction.human_feedback['correction_data'],
                'input_features': self._extract_features(interaction.input_data),
                'correction_type': interaction.human_feedback.get('correction_type', 'general')
            }
            patterns['common_corrections'].append(correction)
        
        # Update accuracy thresholds
        provider_key = f"{interaction.provider}_{interaction.model}"
        if provider_key not in patterns['accuracy_thresholds']:
            patterns['accuracy_thresholds'][provider_key] = []
        
        if accuracy_score is not None:
            patterns['accuracy_thresholds'][provider_key].append(accuracy_score)
        
        self._save_patterns()
    
    def _extract_features(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant features from input data for pattern matching"""
        
        features = {}
        
        # Extract common features
        if 'module_name' in input_data:
            features['module_name'] = input_data['module_name']
        
        if 'error_type' in input_data:
            features['error_type'] = input_data['error_type']
        
        if 'source_version' in input_data:
            features['source_version'] = input_data['source_version']
        
        if 'target_version' in input_data:
            features['target_version'] = input_data['target_version']
        
        # Extract complexity indicators
        if 'code_complexity' in input_data:
            features['complexity_level'] = 'high' if input_data['code_complexity'] > 0.7 else 'medium' if input_data['code_complexity'] > 0.3 else 'low'
        
        return features
    
    def _get_learned_suggestions(self, input_type: str, input_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get suggestions based on learned patterns"""
        
        if input_type not in self.learned_patterns:
            return []
        
        patterns = self.learned_patterns[input_type]
        input_features = self._extract_features(input_data)
        suggestions = []
        
        # Find similar successful patterns
        for pattern in patterns['successful_patterns']:
            similarity = self._calculate_similarity(input_features, pattern['input_features'])
            if similarity > 0.6:  # Threshold for similarity
                suggestions.append({
                    'type': 'learned_success',
                    'suggestion': pattern['successful_response'],
                    'confidence': similarity * pattern['accuracy_score'],
                    'source': f"Similar successful case with {pattern['provider']} {pattern['model']}"
                })
        
        # Check for common corrections
        for correction in patterns['common_corrections']:
            similarity = self._calculate_similarity(input_features, correction['input_features'])
            if similarity > 0.7:
                suggestions.append({
                    'type': 'learned_correction',
                    'suggestion': correction['corrected_response'],
                    'confidence': similarity,
                    'source': f"Common correction pattern"
                })
        
        return sorted(suggestions, key=lambda x: x['confidence'], reverse=True)[:3]
    
    def _get_provider_improvements(self, provider: str, model: str, input_type: str) -> Dict[str, Any]:
        """Get provider-specific improvements"""
        
        key = f"{provider}_{model}"
        
        if key not in self.metrics:
            return {}
        
        metric = self.metrics[key]
        
        return {
            'expected_accuracy': metric.average_accuracy,
            'helpfulness_rate': metric.helpfulness_rate,
            'typical_response_time': metric.response_time_avg,
            'recommendation': self._get_provider_recommendation(metric)
        }
    
    def _get_confidence_adjustments(self, provider: str, model: str, input_type: str) -> Dict[str, float]:
        """Get confidence adjustments based on historical performance"""
        
        if input_type not in self.learned_patterns:
            return {'adjustment': 1.0}
        
        patterns = self.learned_patterns[input_type]
        provider_key = f"{provider}_{model}"
        
        if provider_key in patterns['accuracy_thresholds']:
            scores = patterns['accuracy_thresholds'][provider_key]
            avg_accuracy = sum(scores) / len(scores) if scores else 0.5
            
            # Adjust confidence based on historical accuracy
            adjustment = min(max(avg_accuracy, 0.3), 1.2)  # Clamp between 0.3 and 1.2
            
            return {
                'adjustment': adjustment,
                'historical_accuracy': avg_accuracy,
                'sample_size': len(scores)
            }
        
        return {'adjustment': 1.0}
    
    def _calculate_similarity(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """Calculate similarity between two feature sets"""
        
        if not features1 or not features2:
            return 0.0
        
        common_keys = set(features1.keys()) & set(features2.keys())
        if not common_keys:
            return 0.0
        
        matches = 0
        for key in common_keys:
            if features1[key] == features2[key]:
                matches += 1
        
        return matches / len(common_keys)
    
    def _get_provider_recommendation(self, metric: AIMetrics) -> str:
        """Get recommendation for provider usage"""
        
        if metric.average_accuracy > 0.8 and metric.helpfulness_rate > 0.8:
            return "Excellent performance - recommended for all tasks"
        elif metric.average_accuracy > 0.6 and metric.helpfulness_rate > 0.6:
            return "Good performance - suitable for most tasks"
        elif metric.average_accuracy > 0.4:
            return "Moderate performance - use with caution"
        else:
            return "Poor performance - consider alternative provider"
    
    def _analyze_common_patterns(self) -> Dict[str, Any]:
        """Analyze common patterns across all interactions"""
        
        patterns = {
            'most_common_input_types': defaultdict(int),
            'most_helpful_providers': defaultdict(int),
            'common_failure_reasons': defaultdict(int),
            'accuracy_trends': []
        }
        
        for interaction in self.interactions:
            patterns['most_common_input_types'][interaction.input_type] += 1
            
            if interaction.was_helpful:
                patterns['most_helpful_providers'][f"{interaction.provider}_{interaction.model}"] += 1
            
            if not interaction.was_helpful and interaction.human_feedback:
                feedback_notes = interaction.human_feedback.get('feedback_notes', '')
                if 'incorrect' in feedback_notes.lower():
                    patterns['common_failure_reasons']['incorrect_analysis'] += 1
                elif 'incomplete' in feedback_notes.lower():
                    patterns['common_failure_reasons']['incomplete_response'] += 1
                elif 'slow' in feedback_notes.lower():
                    patterns['common_failure_reasons']['slow_response'] += 1
        
        return patterns
