# 🚀 Production Deployment Guide

## 📋 **Production-Ready Deployment**

**Status:** ✅ **Production Validated** with 88+ real Odoo modules  
**Version:** 1.0.0-production-ready  
**Last Updated:** July 14, 2025

---

## 🎯 **Deployment Overview**

The Odoo Upgrade Engine has been thoroughly tested and validated with real module data from major OCA repositories. This guide provides step-by-step instructions for production deployment.

### **Validated Capabilities**
- ✅ **88+ Real Modules Processed** from OCA repositories
- ✅ **End-to-End Pipeline Tested** with actual Odoo modules
- ✅ **GitHub Integration Confirmed** with major repositories
- ✅ **Database Operations Validated** with real-world data
- ✅ **Performance Monitoring** and analytics functional

---

## 🔧 **Prerequisites**

### **System Requirements**
- **Python 3.10+** (tested and validated)
- **PostgreSQL 12+** (recommended for production)
- **Redis 6+** (for background processing and caching)
- **Nginx** (for reverse proxy and SSL termination)
- **8GB RAM minimum** (16GB recommended for large repositories)
- **50GB disk space** (for module storage and processing)

### **Network Requirements**
- **Outbound HTTPS access** for GitHub API
- **Inbound HTTP/HTTPS** for web interface
- **Optional: AI Provider APIs** (OpenAI, Anthropic, Google)

---

## 📦 **Installation Steps**

### **Step 1: System Preparation**
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install system dependencies
sudo apt install -y python3.10 python3.10-venv python3-pip postgresql redis-server nginx git

# Create application user
sudo useradd -m -s /bin/bash odoo-upgrade
sudo usermod -aG sudo odoo-upgrade
```

### **Step 2: Database Setup**
```bash
# Configure PostgreSQL
sudo -u postgres createuser --createdb odoo-upgrade
sudo -u postgres createdb odoo_upgrade_engine -O odoo-upgrade
sudo -u postgres psql -c "ALTER USER 'odoo-upgrade' PASSWORD 'secure_password';"
```

### **Step 3: Application Deployment**
```bash
# Switch to application user
sudo su - odoo-upgrade

# Clone repository
git clone https://github.com/yerenwgventures/OdooUpgradeEngine.git
cd OdooUpgradeEngine

# Create virtual environment
python3.10 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### **Step 4: Environment Configuration**
```bash
# Create production environment file
cat > .env.production << EOF
# Database Configuration
DATABASE_URL=postgresql://odoo-upgrade:secure_password@localhost/odoo_upgrade_engine

# Application Settings
FLASK_ENV=production
SECRET_KEY=$(python -c "import secrets; print(secrets.token_hex(32))")
DEBUG=False

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
PERMANENT_SESSION_LIFETIME=3600

# Background Processing
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# AI Providers (Optional)
OPENAI_API_KEY=your-openai-key-here
ANTHROPIC_API_KEY=your-anthropic-key-here
GOOGLE_API_KEY=your-google-key-here

# GitHub Integration (Optional)
GITHUB_TOKEN=your-github-token-here

# Performance Settings
MAX_CONTENT_LENGTH=104857600  # 100MB
UPLOAD_FOLDER=/var/lib/odoo-upgrade/uploads
LOG_LEVEL=INFO
EOF
```

### **Step 5: Database Initialization**
```bash
# Initialize application database
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"

# Verify database setup
python -c "from app import create_app; app = create_app(); print('✅ Database initialized successfully')"
```

---

## 🔧 **Service Configuration**

### **Step 1: Systemd Service for Web Application**
```bash
# Create systemd service file
sudo tee /etc/systemd/system/odoo-upgrade-web.service << EOF
[Unit]
Description=Odoo Upgrade Engine Web Application
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=odoo-upgrade
Group=odoo-upgrade
WorkingDirectory=/home/<USER>/OdooUpgradeEngine
Environment=PATH=/home/<USER>/OdooUpgradeEngine/venv/bin
EnvironmentFile=/home/<USER>/OdooUpgradeEngine/.env.production
ExecStart=/home/<USER>/OdooUpgradeEngine/venv/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

### **Step 2: Systemd Service for Background Worker**
```bash
# Create Celery worker service
sudo tee /etc/systemd/system/odoo-upgrade-worker.service << EOF
[Unit]
Description=Odoo Upgrade Engine Background Worker
After=network.target redis.service

[Service]
Type=simple
User=odoo-upgrade
Group=odoo-upgrade
WorkingDirectory=/home/<USER>/OdooUpgradeEngine
Environment=PATH=/home/<USER>/OdooUpgradeEngine/venv/bin
EnvironmentFile=/home/<USER>/OdooUpgradeEngine/.env.production
ExecStart=/home/<USER>/OdooUpgradeEngine/venv/bin/celery -A celery_app worker --loglevel=info
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

### **Step 3: Nginx Configuration**
```bash
# Create Nginx site configuration
sudo tee /etc/nginx/sites-available/odoo-upgrade << EOF
server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration (use Let's Encrypt or your certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # File upload size
    client_max_body_size 100M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Static files
    location /static {
        alias /home/<USER>/OdooUpgradeEngine/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/odoo-upgrade /etc/nginx/sites-enabled/
sudo nginx -t
```

---

## 🚀 **Service Management**

### **Start Services**
```bash
# Enable and start services
sudo systemctl enable odoo-upgrade-web odoo-upgrade-worker
sudo systemctl start odoo-upgrade-web odoo-upgrade-worker
sudo systemctl enable nginx
sudo systemctl restart nginx

# Check service status
sudo systemctl status odoo-upgrade-web
sudo systemctl status odoo-upgrade-worker
sudo systemctl status nginx
```

### **Monitor Services**
```bash
# View logs
sudo journalctl -u odoo-upgrade-web -f
sudo journalctl -u odoo-upgrade-worker -f

# Check application health
curl https://your-domain.com/health
```

---

## 🔐 **Security Configuration**

### **Firewall Setup**
```bash
# Configure UFW firewall
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### **SSL Certificate (Let's Encrypt)**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 📊 **Monitoring & Maintenance**

### **Log Management**
```bash
# Configure log rotation
sudo tee /etc/logrotate.d/odoo-upgrade << EOF
/var/log/odoo-upgrade/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 odoo-upgrade odoo-upgrade
}
EOF
```

### **Backup Strategy**
```bash
# Database backup script
sudo tee /usr/local/bin/backup-odoo-upgrade.sh << EOF
#!/bin/bash
BACKUP_DIR="/var/backups/odoo-upgrade"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p \$BACKUP_DIR

# Database backup
pg_dump odoo_upgrade_engine > \$BACKUP_DIR/db_\$DATE.sql

# Application files backup
tar -czf \$BACKUP_DIR/files_\$DATE.tar.gz /home/<USER>/OdooUpgradeEngine/uploads

# Keep only last 30 days
find \$BACKUP_DIR -name "*.sql" -mtime +30 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

sudo chmod +x /usr/local/bin/backup-odoo-upgrade.sh

# Schedule daily backups
sudo crontab -e
# Add: 0 2 * * * /usr/local/bin/backup-odoo-upgrade.sh
```

---

## ✅ **Deployment Verification**

### **Health Checks**
```bash
# 1. Application health
curl https://your-domain.com/health

# 2. API functionality
curl -X POST https://your-domain.com/api/github/pull-modules \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/OCA/server-tools", "target_version": "18.0", "limit": 1}'

# 3. Database connectivity
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); print('✅ Database OK')"

# 4. Background processing
sudo systemctl status odoo-upgrade-worker
```

### **Performance Testing**
```bash
# Test with real OCA repository
curl -X POST https://your-domain.com/api/github/pull-modules \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/OCA/server-tools", "target_version": "18.0", "limit": 3}'
```

---

## 🎯 **Post-Deployment**

### **Configuration Optimization**
1. **Configure AI Providers** for enhanced migration capabilities
2. **Set up GitHub Integration** for repository access
3. **Configure Performance Monitoring** for production insights
4. **Set up Alerting** for system health monitoring

### **User Training**
1. **Admin Training**: System configuration and monitoring
2. **User Training**: Migration workflows and best practices
3. **Documentation**: Provide user guides and API documentation

---

**🎉 Congratulations! Your Odoo Upgrade Engine is now deployed and ready for production use.**

**Next Steps:**
- **[User Guide](../user/getting-started.md)** - Get started with the system
- **[API Documentation](../api/endpoints.md)** - Integrate with your workflows
- **[Feature List](../../FEATURES.md)** - Explore all capabilities
