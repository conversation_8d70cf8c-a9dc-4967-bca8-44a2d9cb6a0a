/**
 * AI Integration JavaScript
 * Centralized AI functionality for the Odoo Upgrade Engine
 * 
 * This file provides common AI integration functions used across
 * multiple pages in the application.
 */

// ===== CORE AI FUNCTIONS =====

/**
 * Make AI API request with error handling
 */
async function makeAIRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('AI API Error:', error);
        throw error;
    }
}

/**
 * Show loading state on button
 */
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.dataset.originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
        button.disabled = true;
    } else {
        button.innerHTML = button.dataset.originalContent || button.innerHTML;
        button.disabled = false;
    }
}

/**
 * Show AI notification
 */
function showAINotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    notification.innerHTML = `
        <i class="fas fa-robot me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ===== MODAL FUNCTIONS =====

/**
 * Create and show AI modal
 */
function showAIModal(title, content, actions = []) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    ${actions.map(action => `
                        <button type="button" class="btn ${action.class}" onclick="${action.onclick}">
                            ${action.text}
                        </button>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    // Remove modal from DOM when hidden
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
    
    return bsModal;
}

/**
 * Show AI analysis results
 */
function showAIAnalysisModal(analysis, title = 'AI Analysis Results') {
    const content = `
        <div class="alert alert-info">
            <i class="fas fa-robot me-2"></i>
            AI has completed the analysis and provided the following insights:
        </div>
        <div class="card">
            <div class="card-body">
                <pre class="text-wrap" style="white-space: pre-wrap;">${analysis}</pre>
            </div>
        </div>
    `;
    
    const actions = [
        {
            text: '<i class="fas fa-copy me-1"></i>Copy Results',
            class: 'btn-outline-primary',
            onclick: `copyToClipboard('${analysis.replace(/'/g, "\\'")}')`
        },
        {
            text: '<i class="fas fa-download me-1"></i>Download Report',
            class: 'btn-primary',
            onclick: `downloadAIReport('${analysis.replace(/'/g, "\\'")}')`
        }
    ];
    
    return showAIModal(title, content, actions);
}

/**
 * Show AI suggestions
 */
function showAISuggestionsModal(suggestions, title = 'AI Suggestions') {
    const content = `
        <div class="alert alert-warning">
            <i class="fas fa-lightbulb me-2"></i>
            AI recommendations for improving your migration:
        </div>
        <div class="card">
            <div class="card-body">
                <pre class="text-wrap" style="white-space: pre-wrap;">${suggestions}</pre>
            </div>
        </div>
    `;
    
    const actions = [
        {
            text: '<i class="fas fa-check me-1"></i>Apply Suggestions',
            class: 'btn-success',
            onclick: 'applySuggestions()'
        }
    ];
    
    return showAIModal(title, content, actions);
}

// ===== UTILITY FUNCTIONS =====

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAINotification('Results copied to clipboard!', 'success');
    }).catch(() => {
        showAINotification('Failed to copy to clipboard', 'error');
    });
}

/**
 * Download AI report as text file
 */
function downloadAIReport(content) {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-report-${new Date().toISOString().slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showAINotification('AI report downloaded!', 'success');
}

/**
 * Apply AI suggestions (placeholder)
 */
function applySuggestions() {
    showAINotification('Apply suggestions functionality will be implemented in the backend', 'info');
}

// ===== COMMON AI ACTIONS =====

/**
 * Generic AI rerun function
 */
async function aiRerunMigration(jobId, buttonElement = null) {
    if (buttonElement) setButtonLoading(buttonElement, true);
    
    try {
        const result = await makeAIRequest(`/api/ai-rerun-migration/${jobId}`, {
            method: 'POST'
        });
        
        showAINotification(result.message, 'success');
        
        // Refresh page or update UI
        if (typeof refreshModules === 'function') {
            refreshModules();
        } else if (typeof loadMigrationJobs === 'function') {
            loadMigrationJobs();
        } else {
            location.reload();
        }
        
    } catch (error) {
        showAINotification(`Failed to start AI migration: ${error.message}`, 'error');
    } finally {
        if (buttonElement) setButtonLoading(buttonElement, false);
    }
}

/**
 * Generic AI failure analysis
 */
async function aiAnalyzeFailure(jobId, buttonElement = null) {
    if (buttonElement) setButtonLoading(buttonElement, true);
    
    try {
        const result = await makeAIRequest(`/api/ai-analyze-failure/${jobId}`, {
            method: 'POST'
        });
        
        showAIAnalysisModal(result.analysis, 'AI Failure Analysis');
        
    } catch (error) {
        showAINotification(`Failed to analyze failure: ${error.message}`, 'error');
    } finally {
        if (buttonElement) setButtonLoading(buttonElement, false);
    }
}

/**
 * Generic AI suggestions
 */
async function aiGetSuggestions(jobId) {
    try {
        const result = await makeAIRequest(`/api/ai-suggestions/${jobId}`);
        showAISuggestionsModal(result.suggestions, 'AI Suggestions');
    } catch (error) {
        showAINotification(`Failed to get AI suggestions: ${error.message}`, 'error');
    }
}

// ===== INITIALIZATION =====

/**
 * Initialize AI integration when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Integration JavaScript loaded');
    
    // Add global error handler for AI operations
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message && event.reason.message.includes('AI')) {
            showAINotification('An AI operation failed. Please try again.', 'error');
        }
    });
});

// ===== EXPORT FOR GLOBAL USE =====

// Make functions available globally
window.AIIntegration = {
    makeAIRequest,
    setButtonLoading,
    showAINotification,
    showAIModal,
    showAIAnalysisModal,
    showAISuggestionsModal,
    copyToClipboard,
    downloadAIReport,
    applySuggestions,
    aiRerunMigration,
    aiAnalyzeFailure,
    aiGetSuggestions
};

console.log('AI Integration module loaded successfully');
