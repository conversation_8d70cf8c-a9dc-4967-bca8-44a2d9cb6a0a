import os
import zipfile
import tarfile
import ast
import json
import logging
import re
from pathlib import Path
import tempfile
import shutil

class ModuleAnalyzer:
    def __init__(self):
        self.odoo18_api_changes = {
            # Common API changes in Odoo 18
            'deprecated_imports': [
                'from odoo.tools import _',
                'from odoo.tools.translate import _',
            ],
            'new_imports': [
                'from odoo import _',
            ],
            'deprecated_methods': [
                'sudo()',
                'with_context(',
                'search_count(',
            ],
            'model_changes': [
                'ir.actions.act_window',
                'ir.model.data',
                'ir.ui.view',
            ]
        }
    
    def analyze_module(self, file_path, target_version='18.0'):
        """Analyze a module file (zip or tar)"""
        try:
            # Extract module to temporary directory
            temp_dir = tempfile.mkdtemp()
            extracted_path = self._extract_module(file_path, temp_dir)
            
            if not extracted_path:
                return {'error': 'Failed to extract module'}
            
            # Find module root directory
            module_root = self._find_module_root(extracted_path)
            if not module_root:
                return {'error': 'No valid module found in archive'}
            
            # Analyze module structure with target version
            analysis = self._analyze_module_structure(module_root, target_version)
            
            # Cleanup
            shutil.rmtree(temp_dir)
            
            return analysis
            
        except Exception as e:
            logging.error(f"Module analysis failed: {str(e)}")
            return {'error': str(e)}
    
    def _extract_module(self, file_path, extract_to):
        """Extract module archive"""
        try:
            if file_path.endswith('.zip'):
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
            elif file_path.endswith(('.tar', '.tar.gz', '.tgz')):
                with tarfile.open(file_path, 'r:*') as tar_ref:
                    tar_ref.extractall(extract_to)
            else:
                return None
            
            return extract_to
            
        except Exception as e:
            logging.error(f"Extraction failed: {str(e)}")
            return None
    
    def _find_module_root(self, extracted_path):
        """Find the root directory of the module"""
        # Look for __manifest__.py or __openerp__.py
        for root, dirs, files in os.walk(extracted_path):
            if '__manifest__.py' in files or '__openerp__.py' in files:
                return root
        return None
    
    def _analyze_module_structure(self, module_root, target_version='18.0'):
        """Analyze module structure and compatibility"""
        analysis = {
            'name': '',
            'version': '',
            'author': '',
            'website': '',
            'summary': '',
            'description': '',
            'category': '',
            'odoo_version': '',
            'depends': [],
            'external_dependencies': {},
            'has_manifest': False,
            'has_models': False,
            'has_views': False,
            'has_controllers': False,
            'has_static': False,
            'has_security': False,
            'has_data': False,
            'compatibility_score': 0,
            'compatibility_issues': [],
            'compatibility_warnings': [],
            'file_structure': {},
            'target_version': target_version
        }
        
        # Analyze manifest file
        manifest_path = os.path.join(module_root, '__manifest__.py')
        if not os.path.exists(manifest_path):
            manifest_path = os.path.join(module_root, '__openerp__.py')
        
        if os.path.exists(manifest_path):
            analysis['has_manifest'] = True
            manifest_data = self._parse_manifest(manifest_path)
            analysis.update(manifest_data)
        
        # Analyze file structure
        analysis['file_structure'] = self._analyze_file_structure(module_root)
        
        # Check for common directories/files
        analysis['has_models'] = os.path.exists(os.path.join(module_root, 'models'))
        analysis['has_views'] = os.path.exists(os.path.join(module_root, 'views'))
        analysis['has_controllers'] = os.path.exists(os.path.join(module_root, 'controllers'))
        analysis['has_static'] = os.path.exists(os.path.join(module_root, 'static'))
        analysis['has_security'] = os.path.exists(os.path.join(module_root, 'security'))
        analysis['has_data'] = os.path.exists(os.path.join(module_root, 'data'))
        
        # Perform compatibility analysis for target version
        compatibility_result = self._analyze_compatibility(module_root, analysis, target_version)
        analysis.update(compatibility_result)
        
        return analysis
    
    def _parse_manifest(self, manifest_path):
        """Parse module manifest file"""
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Safely evaluate the manifest dictionary
            manifest_dict = ast.literal_eval(content)
            
            return {
                'name': manifest_dict.get('name', ''),
                'version': manifest_dict.get('version', ''),
                'author': manifest_dict.get('author', ''),
                'website': manifest_dict.get('website', ''),
                'summary': manifest_dict.get('summary', ''),
                'description': manifest_dict.get('description', ''),
                'category': manifest_dict.get('category', ''),
                'depends': manifest_dict.get('depends', []),
                'external_dependencies': manifest_dict.get('external_dependencies', {}),
                'installable': manifest_dict.get('installable', True),
                'auto_install': manifest_dict.get('auto_install', False),
                'application': manifest_dict.get('application', False),
            }
            
        except Exception as e:
            logging.error(f"Failed to parse manifest: {str(e)}")
            return {}
    
    def _analyze_file_structure(self, module_root):
        """Analyze module file structure"""
        structure = {}
        
        for root, dirs, files in os.walk(module_root):
            rel_path = os.path.relpath(root, module_root)
            if rel_path == '.':
                rel_path = 'root'
            
            structure[rel_path] = {
                'directories': dirs,
                'files': files,
                'python_files': [f for f in files if f.endswith('.py')],
                'xml_files': [f for f in files if f.endswith('.xml')],
                'js_files': [f for f in files if f.endswith(('.js', '.mjs'))],
                'css_files': [f for f in files if f.endswith(('.css', '.scss', '.sass', '.less'))],
                'template_files': [f for f in files if f.endswith(('.html', '.qweb'))],
                'config_files': [f for f in files if f.endswith(('.json', '.yaml', '.yml', '.ini', '.conf'))],
                'image_files': [f for f in files if f.endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'))],
                'doc_files': [f for f in files if f.endswith(('.md', '.rst', '.txt', '.pdf'))],
                'test_files': [f for f in files if 'test' in f.lower() and f.endswith('.py')],
                'migration_files': [f for f in files if 'migration' in f.lower() or 'upgrade' in f.lower()],
                'wizard_files': [f for f in files if 'wizard' in f.lower() and f.endswith('.py')],
                'report_files': [f for f in files if 'report' in f.lower()],
            }
        
        return structure
    
    def _analyze_compatibility(self, module_root, analysis, target_version='18.0'):
        """Analyze Odoo compatibility for target version"""
        issues = []
        warnings = []
        score = 100
        
        # Adjust base score based on target version
        if target_version == '17.0':
            score = 85  # Start higher for 17.0 as it's less strict
        elif target_version == '18.0':
            score = 100  # Full compatibility expected for 18.0
        
        # Check manifest for Odoo version compatibility
        if 'depends' in analysis:
            # Check for deprecated dependencies
            deprecated_deps = ['web_tree_many2one_clickable', 'web_export_view']
            for dep in analysis['depends']:
                if dep in deprecated_deps:
                    issues.append(f"Deprecated dependency: {dep}")
                    score -= 10
        
        # Analyze Python files for compatibility issues
        python_issues = self._analyze_python_files(module_root)
        issues.extend(python_issues['issues'])
        warnings.extend(python_issues['warnings'])
        score -= len(python_issues['issues']) * 5
        score -= len(python_issues['warnings']) * 2
        
        # Analyze XML files for compatibility issues
        xml_issues = self._analyze_xml_files(module_root)
        issues.extend(xml_issues['issues'])
        warnings.extend(xml_issues['warnings'])
        score -= len(xml_issues['issues']) * 3
        score -= len(xml_issues['warnings']) * 1
        
        # Analyze JavaScript files for compatibility issues
        js_issues = self._analyze_js_files(module_root)
        issues.extend(js_issues['issues'])
        warnings.extend(js_issues['warnings'])
        score -= len(js_issues['issues']) * 2
        score -= len(js_issues['warnings']) * 1
        
        # Determine Odoo version from manifest or code analysis
        odoo_version = self._determine_odoo_version(module_root, analysis)
        
        # Version-specific compatibility checks
        if odoo_version and not odoo_version.startswith('18'):
            if odoo_version.startswith(('8', '9', '10')):
                issues.append(f"Very old Odoo version ({odoo_version}) - major migration required")
                score -= 30
            elif odoo_version.startswith(('11', '12', '13')):
                issues.append(f"Old Odoo version ({odoo_version}) - significant migration required")
                score -= 20
            elif odoo_version.startswith(('14', '15', '16')):
                warnings.append(f"Older Odoo version ({odoo_version}) - minor migration may be required")
                score -= 10
            elif odoo_version.startswith('17'):
                warnings.append("Odoo 17 module - should be mostly compatible with minor adjustments")
                score -= 5
        
        return {
            'odoo_version': odoo_version,
            'compatibility_score': max(0, score),
            'compatibility_issues': issues,
            'compatibility_warnings': warnings
        }
    
    def _analyze_python_files(self, module_root):
        """Analyze Python files for compatibility issues"""
        issues = []
        warnings = []
        
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Check for deprecated imports
                        for deprecated_import in self.odoo18_api_changes['deprecated_imports']:
                            if deprecated_import in content:
                                issues.append(f"Deprecated import in {file}: {deprecated_import}")
                        
                        # Check for old-style API usage
                        if '@api.one' in content:
                            issues.append(f"Old API decorator @api.one found in {file}")
                        
                        if '@api.multi' in content:
                            issues.append(f"Old API decorator @api.multi found in {file}")
                        
                        # Check for deprecated methods
                        if '.sudo()' in content and 'self.sudo()' not in content:
                            warnings.append(f"Potential deprecated sudo() usage in {file}")
                        
                        # Check for osv imports (very old)
                        if 'from osv import' in content or 'import osv' in content:
                            issues.append(f"Very old osv import found in {file}")
                        
                        # Check for new-style model inheritance
                        if 'openerp' in content:
                            issues.append(f"Old 'openerp' import found in {file}, should use 'odoo'")
                        
                    except Exception as e:
                        warnings.append(f"Could not analyze {file}: {str(e)}")
        
        return {'issues': issues, 'warnings': warnings}
    
    def _analyze_xml_files(self, module_root):
        """Analyze XML files for compatibility issues"""
        issues = []
        warnings = []
        
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.xml'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Check for deprecated view inheritance
                        if 'position="attributes"' in content and 'attrs=' in content:
                            warnings.append(f"Potential deprecated attrs usage in {file}")
                        
                        # Check for old menu definitions
                        if 'parent="base.menu_' in content:
                            warnings.append(f"Check menu parent references in {file}")
                        
                        # Check for deprecated field types
                        deprecated_fields = ['many2one_list', 'reference']
                        for field_type in deprecated_fields:
                            if f'widget="{field_type}"' in content:
                                warnings.append(f"Potentially deprecated widget '{field_type}' in {file}")
                        
                    except Exception as e:
                        warnings.append(f"Could not analyze {file}: {str(e)}")
        
        return {'issues': issues, 'warnings': warnings}
    
    def _determine_odoo_version(self, module_root, analysis):
        """Determine the SOURCE Odoo version of the module using comprehensive analysis"""
        # Use sophisticated version detection to prevent wrong folder placement
        version_scores = {}
        
        # Define version-specific patterns with weights
        version_patterns = {
            '13.0': {
                'strong_indicators': [('@api.one', 20), ('@api.multi', 20), ('@api.returns', 15)],
                'medium_indicators': [('sudo()', 10), ('openerp.', 10)],
                'weak_indicators': [('from openerp import', 5)]
            },
            '14.0': {
                'strong_indicators': [('@api.one', 15), ('@api.multi', 15), ('with_user', 10)],
                'medium_indicators': [('sudo()', 8), ('openerp.', 8)],
                'weak_indicators': [('from openerp import', 3)]
            },
            '15.0': {
                'strong_indicators': [('"qweb":', 20), ('web.assets_frontend', 15), ('web.assets_backend', 15)],
                'medium_indicators': [('odoo.define', 10), ('require(', 10)],
                'weak_indicators': [('web.assets_qweb', 5)]
            },
            '16.0': {
                'strong_indicators': [('Widget.extend', 20), ('@odoo-module', 25), ('import {', 20)],
                'medium_indicators': [('Component', 10), ('legacy/', 10)],
                'weak_indicators': [('owl.Component', 5)]
            },
            '17.0': {
                'strong_indicators': [('static template', 25), ('prepend', 15), ('before', 10)],
                'medium_indicators': [('bootstrap-5', 10), ('@odoo-module', 8)],
                'weak_indicators': [('dropdown', 5)]
            },
            '18.0': {
                'strong_indicators': [('spreadsheet', 15), ('custom_properties', 20)],
                'medium_indicators': [('performance', 8), ('3.7x', 10)],
                'weak_indicators': [('backend_performance', 5)]
            }
        }
        
        # Initialize scores
        for version in version_patterns:
            version_scores[version] = 0
        
        # Check manifest version first
        manifest_version = analysis.get('odoo_version', None)
        if manifest_version and manifest_version in version_patterns:
            version_scores[manifest_version] += 50  # Strong boost for manifest
        
        # Analyze all Python files
        python_content = ""
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            python_content += f.read() + "\n"
                    except:
                        continue
        
        # Analyze all JavaScript files
        js_content = ""
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.js'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            js_content += f.read() + "\n"
                    except:
                        continue
        
        # Analyze manifest file content
        manifest_content = ""
        manifest_files = ['__manifest__.py', '__openerp__.py']
        for manifest_file in manifest_files:
            manifest_path = os.path.join(module_root, manifest_file)
            if os.path.exists(manifest_path):
                try:
                    with open(manifest_path, 'r', encoding='utf-8') as f:
                        manifest_content = f.read()
                        if manifest_file == '__openerp__.py':
                            version_scores['13.0'] += 30  # Strong indicator of v13 or older
                        break
                except:
                    continue
        
        # Score based on patterns
        all_content = python_content + js_content + manifest_content
        
        for version, patterns in version_patterns.items():
            for pattern, weight in patterns['strong_indicators']:
                count = all_content.count(pattern)
                version_scores[version] += count * weight
            
            for pattern, weight in patterns['medium_indicators']:
                count = all_content.count(pattern)
                version_scores[version] += count * weight
            
            for pattern, weight in patterns['weak_indicators']:
                count = all_content.count(pattern)
                version_scores[version] += count * weight
        
        # Special logic for exclusions
        # If we find modern patterns, exclude old versions
        if '@odoo-module' in all_content or 'import {' in all_content:
            version_scores['13.0'] = max(0, version_scores['13.0'] - 50)
            version_scores['14.0'] = max(0, version_scores['14.0'] - 30)
            version_scores['15.0'] = max(0, version_scores['15.0'] - 20)
        
        # If we find old API patterns, exclude new versions
        if '@api.one' in all_content or '@api.multi' in all_content:
            version_scores['16.0'] = max(0, version_scores['16.0'] - 40)
            version_scores['17.0'] = max(0, version_scores['17.0'] - 50)
            version_scores['18.0'] = max(0, version_scores['18.0'] - 50)
        
        # Find the version with highest score
        if version_scores:
            best_version = max(version_scores, key=version_scores.get)
            best_score = version_scores[best_version]
            
            # Only return version if score is significant
            if best_score > 10:
                return best_version
        
        # Fallback: check file structure
        if manifest_content:
            if '"assets"' in manifest_content:
                return '15.0'  # Assets introduced in v15
            elif '__openerp__.py' in [f for f in os.listdir(module_root) if f.endswith('.py')]:
                return '13.0'  # Likely older version
        
        # Default to 13.0 for unknown (start of pipeline)
        return '13.0'
    
    def _analyze_js_files(self, module_root):
        """Analyze JavaScript files for compatibility issues"""
        issues = []
        warnings = []
        
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith(('.js', '.mjs')):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Check for deprecated jQuery patterns
                        if '$(document).ready' in content:
                            warnings.append(f"Old jQuery document ready pattern in {file}")
                        
                        # Check for deprecated Odoo web client patterns
                        if 'openerp.web' in content:
                            issues.append(f"Old OpenERP web client reference in {file}")
                        
                        if 'instance.web' in content:
                            issues.append(f"Deprecated instance.web usage in {file}")
                        
                        # Check for modern ES6+ features that might need transpilation
                        if 'class ' in content and 'extends' in content:
                            warnings.append(f"ES6 class syntax found in {file} - ensure browser compatibility")
                        
                        if '=>' in content:
                            warnings.append(f"Arrow functions found in {file} - ensure browser compatibility")
                        
                        if 'const ' in content or 'let ' in content:
                            warnings.append(f"ES6 variable declarations in {file} - ensure browser compatibility")
                        
                        # Check for Odoo 18 web framework patterns
                        if '@odoo/owl' in content:
                            # Modern OWL framework usage - good for Odoo 18
                            pass
                        elif 'owl.Component' in content:
                            warnings.append(f"OWL Component usage in {file} - verify Odoo 18 compatibility")
                        
                        # Check for deprecated web patterns
                        if 'web.Widget' in content:
                            issues.append(f"Deprecated web.Widget usage in {file}")
                        
                        if 'web.Class' in content:
                            issues.append(f"Deprecated web.Class usage in {file}")
                        
                        # Check for AJAX patterns
                        if 'rpc.query' in content:
                            warnings.append(f"Old RPC query pattern in {file}")
                        
                        if 'ajax.jsonRpc' in content:
                            warnings.append(f"Check AJAX jsonRpc compatibility in {file}")
                        
                        # Check for test files
                        if 'test' in file.lower() or 'spec' in file.lower():
                            if 'QUnit' in content:
                                warnings.append(f"QUnit test framework in {file} - verify Odoo 18 test compatibility")
                            if 'describe(' in content and 'it(' in content:
                                warnings.append(f"Mocha/Jasmine test patterns in {file} - verify test framework compatibility")
                        
                    except Exception as e:
                        warnings.append(f"Could not analyze JavaScript file {file}: {str(e)}")
        
        return {'issues': issues, 'warnings': warnings}
