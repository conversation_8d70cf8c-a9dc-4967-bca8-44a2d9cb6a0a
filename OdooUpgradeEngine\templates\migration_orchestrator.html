{% extends "base.html" %}
{% set title = "Migration Orchestrator" %}

{% block content %}
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Migration Orchestrator
                    </h2>
                    <p class="text-muted">Manage all uploaded modules and their migration status</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('main.migration_types') }}" class="btn btn-outline-primary" title="Choose Migration Type">
                        <i class="fas fa-route"></i> Migration Types
                    </a>
                    <button class="btn btn-outline-secondary" onclick="refreshStatus()" title="Refresh Status">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="{{ url_for('main.upload_modules') }}" class="btn btn-primary" title="Upload New Modules">
                        <i class="fas fa-upload"></i> Upload Modules
                    </a>
                </div>

                <!-- AI Integration Buttons -->
                <div class="btn-group ms-2" role="group" aria-label="AI Actions">
                    <button class="btn btn-info" onclick="batchAIAnalysis()" title="Run AI analysis on all pending migrations">
                        <i class="fas fa-robot me-1"></i>Batch AI Analysis
                    </button>
                    <button class="btn btn-warning" onclick="retryFailedWithAI()" title="Retry all failed migrations with AI assistance">
                        <i class="fas fa-brain me-1"></i>Retry Failed with AI
                    </button>
                    <button class="btn btn-outline-info" onclick="showAIInsights()" title="View AI insights and recommendations">
                        <i class="fas fa-lightbulb me-1"></i>AI Insights
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Migration Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-cubes fa-2x text-primary mb-2"></i>
                    <h4 class="mb-1">{{ total_modules }}</h4>
                    <p class="text-muted mb-0">Total Modules</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="mb-1">{{ pending_jobs }}</h4>
                    <p class="text-muted mb-0">Pending Migrations</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-1">{{ completed_jobs }}</h4>
                    <p class="text-muted mb-0">Completed</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                    <h4 class="mb-1">{{ failed_jobs }}</h4>
                    <p class="text-muted mb-0">Failed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Active Migrations Alert -->
    <div class="row mb-3" id="activeAlertsRow" style="display: none;">
        <div class="col-12">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong id="alertCount">0</strong> migrations require your attention
                    </div>
                    <div>
                        <button class="btn btn-warning btn-sm me-2" onclick="toggleActiveDetails()">
                            <i class="fas fa-eye me-1"></i>View Details
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
                <div id="activeDetailsContainer" class="mt-3" style="display: none;">
                    <!-- Active migration details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Module Management Dashboard -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cubes me-2"></i>
                            Module Dashboard
                        </h5>
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <small class="text-muted">Total: <strong id="totalModules">{{ total_modules }}</strong></small>
                            </div>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-secondary" onclick="refreshModules()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="toggleFilters()">
                                    <i class="fas fa-filter"></i> Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters Section -->
                <div class="card-body border-bottom bg-light" id="filtersSection" style="display: none;">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label small">Search Modules</label>
                            <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="Module name...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">Status</label>
                            <select class="form-select form-select-sm" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="uploaded">Uploaded</option>
                                <option value="queued">Queued</option>
                                <option value="migrating">Migrating</option>
                                <option value="awaiting_approval">Awaiting Approval</option>
                                <option value="completed">Completed</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">Target Version</label>
                            <select class="form-select form-select-sm" id="versionFilter">
                                <option value="">All Versions</option>
                                <option value="17.0">17.0</option>
                                <option value="16.0">16.0</option>
                                <option value="15.0">15.0</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label small">Date Range</label>
                            <input type="date" class="form-control form-control-sm" id="dateFilter">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-primary btn-sm" onclick="applyFilters()">
                                    <i class="fas fa-search me-1"></i>Apply
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Table -->
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="modulesTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th width="25%">Module Name</th>
                                    <th width="15%">Current Version</th>
                                    <th width="15%">Target Version</th>
                                    <th width="15%">Status</th>
                                    <th width="15%">Last Updated</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="modulesTableBody">
                                <!-- Table content will be loaded via JavaScript -->
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin me-2"></i>Loading modules...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="showingTotal">0</span> modules
                            </small>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- Pagination will be generated by JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Panel -->
    <div class="row" id="bulkActionsPanel" style="display: none;">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong id="selectedCount">0</strong> modules selected
                        </div>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary btn-sm" onclick="bulkMigrate()">
                                <i class="fas fa-play me-1"></i>Start Migration
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="bulkApprove()">
                                <i class="fas fa-check me-1"></i>Bulk Approve
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="bulkDelete()">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="fas fa-times me-1"></i>Clear Selection
                            </button>

                            <!-- AI Bulk Actions -->
                            <div class="btn-group btn-group-sm ms-2" role="group" aria-label="AI Bulk Actions">
                                <button class="btn btn-info btn-sm" onclick="bulkAIAnalysis()" title="Run AI analysis on selected modules">
                                    <i class="fas fa-robot me-1"></i>AI Analyze Selected
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="bulkAIOptimize()" title="Optimize selected migrations with AI">
                                    <i class="fas fa-brain me-1"></i>AI Optimize
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshStatus() {
    location.reload();
}

let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let filters = {};

// Load modules data
function loadModules(page = 1) {
    console.log('Loading modules for page:', page);
    currentPage = page;

    const params = new URLSearchParams({
        page: page,
        size: pageSize,
        ...filters
    });

    console.log('Fetching:', `/api/modules?${params}`);

    fetch(`/api/modules?${params}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('API response:', data);
            if (data.success) {
                renderModulesTable(data.modules);
                updatePagination(data.pagination);
                updateStats(data.stats);
            } else {
                console.error('API returned error:', data.error);
                showError('Failed to load modules: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading modules:', error);
            showError('Error loading modules: ' + error.message);
        });
}

function renderModulesTable(modules) {
    const tbody = document.getElementById('modulesTableBody');
    
    if (modules.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No modules found</p>
                    <small>Upload modules to get started</small>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = modules.map(module => `
        <tr>
            <td>
                <input type="checkbox" class="form-check-input module-checkbox" value="${module.id}">
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-cube text-primary me-2"></i>
                    <div>
                        <strong>${module.name}</strong>
                        <br><small class="text-muted">${module.description || 'No description'}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-secondary">${module.current_version || 'Unknown'}</span>
            </td>
            <td>
                <span class="badge bg-info">${module.target_version || 'Not set'}</span>
            </td>
            <td>
                ${getStatusBadge(module.status)}
            </td>
            <td>
                <small>${formatDate(module.last_updated)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    ${getActionButtons(module)}
                </div>
            </td>
        </tr>
    `).join('');
    
    // Update checkbox listeners
    updateCheckboxListeners();
}

function getStatusBadge(status) {
    const statusMap = {
        'uploaded': { class: 'bg-secondary', text: 'Uploaded' },
        'queued': { class: 'bg-warning text-dark', text: 'Queued' },
        'analysis': { class: 'bg-info', text: 'Analyzing' },
        'transforming': { class: 'bg-info', text: 'Transforming' },
        'updating': { class: 'bg-info', text: 'Updating' },
        'reviewing': { class: 'bg-primary', text: 'Reviewing' },
        'awaiting_approval': { class: 'bg-warning text-dark', text: 'Awaiting Approval' },
        'ai_auto_approved': { class: 'bg-info', text: 'AI Auto-Approved' },
        'approved': { class: 'bg-success', text: 'Approved' },
        'migrating': { class: 'bg-primary', text: 'Migrating' },
        'testing': { class: 'bg-info', text: 'Testing' },
        'completed': { class: 'bg-success', text: 'Completed' },
        'failed': { class: 'bg-danger', text: 'Failed' }
    };

    const statusInfo = statusMap[status] || { class: 'bg-secondary', text: status.charAt(0).toUpperCase() + status.slice(1) };
    return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

function getActionButtons(module) {
    let buttons = [];

    if (module.status === 'uploaded') {
        buttons.push(`<button class="btn btn-outline-primary btn-sm" onclick="startMigration(${module.id})" title="Start Migration">
            <i class="fas fa-play"></i>
        </button>`);
    }

    if (module.status === 'awaiting_approval') {
        buttons.push(`<button class="btn btn-outline-warning btn-sm" onclick="reviewMigration(${module.job_id})" title="Review Changes">
            <i class="fas fa-eye"></i>
        </button>`);
        buttons.push(`<button class="btn btn-outline-success btn-sm" onclick="approveMigration(${module.job_id})" title="Approve">
            <i class="fas fa-check"></i>
        </button>`);
    }

    // ===== AI AUTO-APPROVAL INTEGRATION START =====
    if (module.status === 'ai_auto_approved') {
        buttons.push(`<button class="btn btn-outline-info btn-sm" onclick="reviewMigration(${module.job_id})" title="View AI Analysis">
            <i class="fas fa-robot"></i>
        </button>`);
        buttons.push(`<button class="btn btn-outline-primary btn-sm" onclick="continueMigration(${module.job_id})" title="Continue Migration">
            <i class="fas fa-arrow-right"></i>
        </button>`);
    }
    // ===== AI AUTO-APPROVAL INTEGRATION END =====

    if (module.status === 'approved') {
        buttons.push(`<button class="btn btn-outline-primary btn-sm" onclick="continueMigration(${module.job_id})" title="Continue Migration">
            <i class="fas fa-arrow-right"></i>
        </button>`);
    }

    if (['queued', 'analysis', 'transforming', 'updating', 'reviewing', 'migrating', 'testing'].includes(module.status)) {
        buttons.push(`<button class="btn btn-outline-info btn-sm" onclick="viewProgress(${module.job_id})" title="View Progress">
            <i class="fas fa-chart-line"></i>
        </button>`);
    }

    if (module.status === 'completed') {
        buttons.push(`<button class="btn btn-outline-success btn-sm" onclick="downloadModule(${module.job_id})" title="Download Upgraded Module">
            <i class="fas fa-download"></i>
        </button>`);
    }

    if (module.status === 'failed') {
        buttons.push(`<button class="btn btn-outline-danger btn-sm" onclick="viewErrors(${module.job_id})" title="View Errors">
            <i class="fas fa-exclamation-triangle"></i>
        </button>`);
        buttons.push(`<button class="btn btn-outline-primary btn-sm" onclick="retryMigration(${module.job_id})" title="Retry Migration">
            <i class="fas fa-redo"></i>
        </button>`);
    }

    // ===== AI INTEGRATION BUTTONS =====

    // AI buttons for completed migrations
    if (module.status === 'completed') {
        buttons.push(`<button class="btn btn-info btn-sm" onclick="rerunModuleWithAI(${module.job_id})" title="Rerun with AI improvements">
            <i class="fas fa-robot"></i>
        </button>`);
        buttons.push(`<button class="btn btn-outline-info btn-sm" onclick="getAIInsights(${module.job_id})" title="Get AI insights">
            <i class="fas fa-lightbulb"></i>
        </button>`);
    }

    // AI buttons for failed migrations
    if (module.status === 'failed') {
        buttons.push(`<button class="btn btn-warning btn-sm" onclick="aiAnalyzeModuleFailure(${module.job_id})" title="AI analyze failure">
            <i class="fas fa-brain"></i>
        </button>`);
    }

    // AI suggestions for any module
    if (['uploaded', 'awaiting_approval', 'approved'].includes(module.status)) {
        buttons.push(`<button class="btn btn-outline-info btn-sm" onclick="getAIPreAnalysis(${module.id})" title="Get AI pre-analysis">
            <i class="fas fa-search-plus"></i>
        </button>`);
    }

    // Always show details and delete buttons
    buttons.push(`<button class="btn btn-outline-info btn-sm" onclick="viewDetails(${module.id})" title="View Details">
        <i class="fas fa-info-circle"></i>
    </button>`);

    buttons.push(`<button class="btn btn-outline-danger btn-sm ms-1" onclick="deleteModule(${module.id}, '${module.name}')" title="Delete Module">
        <i class="fas fa-trash"></i>
    </button>`);

    return buttons.join(' ');
}

function formatDate(dateString) {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

function toggleFilters() {
    const section = document.getElementById('filtersSection');
    section.style.display = section.style.display === 'none' ? 'block' : 'none';
}

function applyFilters() {
    filters = {
        search: document.getElementById('searchInput').value,
        status: document.getElementById('statusFilter').value,
        version: document.getElementById('versionFilter').value,
        date: document.getElementById('dateFilter').value
    };
    
    loadModules(1);
}

function refreshModules() {
    loadModules(currentPage);
}

// Load modules when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, starting to load modules...');
    loadModules();
    loadActiveAlerts();
});

function loadActiveAlerts() {
    fetch('/api/active-migrations')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.migrations && data.migrations.length > 0) {
                const alertsRow = document.getElementById('activeAlertsRow');
                const alertCount = document.getElementById('alertCount');
                
                const awaitingApproval = data.migrations.filter(m => m.status === 'AWAITING_APPROVAL');
                
                if (awaitingApproval.length > 0) {
                    alertCount.textContent = awaitingApproval.length;
                    alertsRow.style.display = 'block';
                }
            }
        })
        .catch(error => console.error('Error loading active alerts:', error));
}

function toggleActiveDetails() {
    const container = document.getElementById('activeDetailsContainer');
    if (container.style.display === 'none') {
        // Load and show details
        fetch('/api/active-migrations')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const awaitingApproval = data.migrations.filter(m => m.status === 'AWAITING_APPROVAL');
                    container.innerHTML = awaitingApproval.map(job => `
                        <div class="border rounded p-2 mb-2 bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${job.module_name}</strong> → v${job.target_version}
                                    <small class="text-muted ms-2">Job #${job.id}</small>
                                </div>
                                <div>
                                    <button class="btn btn-warning btn-sm me-1" onclick="reviewMigration(${job.id})">
                                        <i class="fas fa-eye me-1"></i>Review
                                    </button>
                                    <button class="btn btn-success btn-sm" onclick="approveMigration(${job.id})">
                                        <i class="fas fa-check me-1"></i>Approve
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('');
                    container.style.display = 'block';
                }
            });
    } else {
        container.style.display = 'none';
    }
}

// Action functions
function startMigration(moduleId) {
    window.location.href = `/orchestrate_migration_form/${moduleId}`;
}

function reviewMigration(jobId) {
    window.open(`/migration/${jobId}/review`, '_blank');
}

function approveMigration(jobId) {
    if (confirm('Are you sure you want to approve this migration?')) {
        fetch(`/migration/${jobId}/approve`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Migration approved successfully!');
                loadModules(currentPage);
                loadActiveAlerts();
            } else {
                alert('Error approving migration: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving migration: ' + error.message);
        });
    }
}

function continueMigration(jobId) {
    if (confirm('Continue with the approved migration?')) {
        fetch(`/migration/${jobId}/approve`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Migration continued successfully!');
                loadModules(currentPage);
                loadActiveAlerts(); // Refresh alerts
            } else {
                alert('Error continuing migration: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error continuing migration: ' + error.message);
        });
    }
}

function viewProgress(jobId) {
    window.open(`/migration/${jobId}/progress`, '_blank');
}

function downloadModule(jobId) {
    window.location.href = `/migration/${jobId}/download`;
}

function viewErrors(jobId) {
    window.open(`/migration/${jobId}/errors`, '_blank');
}

function retryMigration(jobId) {
    if (confirm('Retry the failed migration?')) {
        fetch(`/migration/${jobId}/retry`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => {
            if (response.ok) {
                alert('Migration retry started!');
                loadModules(currentPage);
            } else {
                alert('Error retrying migration');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error retrying migration');
        });
    }
}

function viewDetails(moduleId) {
    window.location.href = `/module/${moduleId}`;
}

function updatePagination(pagination) {
    console.log('Updating pagination:', pagination);
    document.getElementById('showingStart').textContent = ((pagination.current_page - 1) * pagination.page_size) + 1;
    document.getElementById('showingEnd').textContent = Math.min(pagination.current_page * pagination.page_size, pagination.total_count);
    document.getElementById('showingTotal').textContent = pagination.total_count;

    // Update pagination controls
    const paginationEl = document.getElementById('pagination');
    let paginationHTML = '';

    // Previous button
    if (pagination.has_prev) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadModules(${pagination.current_page - 1})">Previous</a></li>`;
    } else {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">Previous</span></li>`;
    }

    // Page numbers
    for (let i = 1; i <= pagination.total_pages; i++) {
        if (i === pagination.current_page) {
            paginationHTML += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadModules(${i})">${i}</a></li>`;
        }
    }

    // Next button
    if (pagination.has_next) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadModules(${pagination.current_page + 1})">Next</a></li>`;
    } else {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">Next</span></li>`;
    }

    paginationEl.innerHTML = paginationHTML;
}

function updateStats(stats) {
    console.log('Updating stats:', stats);
    document.getElementById('totalModules').textContent = stats.total_modules;
}

function updateCheckboxListeners() {
    // Add event listeners for checkboxes
    const checkboxes = document.querySelectorAll('.module-checkbox');
    const selectAll = document.getElementById('selectAll');

    selectAll.addEventListener('change', function() {
        checkboxes.forEach(cb => cb.checked = this.checked);
        updateBulkActions();
    });

    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateBulkActions);
    });
}

function updateBulkActions() {
    const selected = document.querySelectorAll('.module-checkbox:checked');
    const panel = document.getElementById('bulkActionsPanel');
    const count = document.getElementById('selectedCount');

    if (selected.length > 0) {
        panel.style.display = 'block';
        count.textContent = selected.length;
    } else {
        panel.style.display = 'none';
    }
}

function clearSelection() {
    document.querySelectorAll('.module-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAll').checked = false;
    updateBulkActions();
}

function bulkMigrate() {
    const selected = Array.from(document.querySelectorAll('.module-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) return;

    if (confirm(`Start migration for ${selected.length} selected modules?`)) {
        // Implement bulk migration logic
        alert('Bulk migration feature coming soon!');
    }
}

function bulkApprove() {
    const selected = Array.from(document.querySelectorAll('.module-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) return;

    if (confirm(`Approve ${selected.length} selected modules?`)) {
        // Implement bulk approval logic
        alert('Bulk approval feature coming soon!');
    }
}

function bulkDelete() {
    const selected = Array.from(document.querySelectorAll('.module-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) return;

    if (confirm(`Delete ${selected.length} selected modules? This action cannot be undone.`)) {
        // Call delete API for each selected module
        Promise.all(selected.map(moduleId =>
            fetch(`/api/delete-module/${moduleId}`, { method: 'DELETE' })
        )).then(() => {
            alert(`Successfully deleted ${selected.length} modules`);
            refreshModules();
        }).catch(error => {
            alert('Error deleting modules: ' + error.message);
        });
    }
}

function deleteModule(moduleId, moduleName) {
    if (confirm(`Delete module "${moduleName}"? This action cannot be undone.`)) {
        fetch(`/api/delete-module/${moduleId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Module "${moduleName}" deleted successfully`);
                refreshModules();
            } else {
                alert('Error deleting module: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error deleting module: ' + error.message);
        });
    }
}

function showError(message) {
    const tbody = document.getElementById('modulesTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="7" class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <p class="text-danger mb-0">${message}</p>
            </td>
        </tr>
    `;
}

// ===== AI INTEGRATION FUNCTIONS =====

// Header AI functions
function batchAIAnalysis() {
    if (confirm('Run AI analysis on all pending migrations? This may take several minutes.')) {
        fetch('/api/batch-ai-analysis', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`AI analysis started for ${data.count} migrations`);
                refreshModules();
            } else {
                alert('Failed to start batch AI analysis: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

function retryFailedWithAI() {
    if (confirm('Retry all failed migrations with AI assistance?')) {
        fetch('/api/retry-failed-with-ai', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`AI retry started for ${data.count} failed migrations`);
                refreshModules();
            } else {
                alert('Failed to retry with AI: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

function showAIInsights() {
    fetch('/api/ai-insights')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIInsightsModal(data.insights);
        } else {
            alert('No AI insights available: ' + data.error);
        }
    })
    .catch(error => alert('Error: ' + error.message));
}

// Bulk AI functions
function bulkAIAnalysis() {
    const selected = getSelectedModules();
    if (selected.length === 0) {
        alert('Please select modules to analyze');
        return;
    }

    if (confirm(`Run AI analysis on ${selected.length} selected modules?`)) {
        fetch('/api/bulk-ai-analysis', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ module_ids: selected })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`AI analysis started for ${selected.length} modules`);
                refreshModules();
            } else {
                alert('Failed to start AI analysis: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

function bulkAIOptimize() {
    const selected = getSelectedModules();
    if (selected.length === 0) {
        alert('Please select modules to optimize');
        return;
    }

    if (confirm(`Optimize ${selected.length} selected modules with AI?`)) {
        fetch('/api/bulk-ai-optimize', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ module_ids: selected })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`AI optimization started for ${selected.length} modules`);
                refreshModules();
            } else {
                alert('Failed to start AI optimization: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

// Individual module AI functions
function rerunModuleWithAI(jobId) {
    if (confirm('Rerun this migration with AI improvements?')) {
        fetch(`/api/ai-rerun-migration/${jobId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('AI-enhanced migration started successfully!');
                refreshModules();
            } else {
                alert('Failed to start AI migration: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

function getAIInsights(jobId) {
    fetch(`/api/ai-insights/${jobId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIInsightsModal(data.insights);
        } else {
            alert('No AI insights available: ' + data.error);
        }
    })
    .catch(error => alert('Error: ' + error.message));
}

function aiAnalyzeModuleFailure(jobId) {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`/api/ai-analyze-failure/${jobId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        button.innerHTML = originalContent;
        button.disabled = false;

        if (data.success) {
            showAIAnalysisModal(data.analysis);
        } else {
            alert('Failed to analyze failure: ' + data.error);
        }
    })
    .catch(error => {
        button.innerHTML = originalContent;
        button.disabled = false;
        alert('Error: ' + error.message);
    });
}

function getAIPreAnalysis(moduleId) {
    fetch(`/api/ai-pre-analysis/${moduleId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIPreAnalysisModal(data.analysis);
        } else {
            alert('AI pre-analysis not available: ' + data.error);
        }
    })
    .catch(error => alert('Error: ' + error.message));
}

// Helper functions
function getSelectedModules() {
    const checkboxes = document.querySelectorAll('input[name="moduleSelect"]:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

function showAIInsightsModal(insights) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        AI Insights & Recommendations
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-robot me-2"></i>
                        AI analysis of your migration patterns and recommendations:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${insights}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="implementAIRecommendations()">Implement Recommendations</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function showAIAnalysisModal(analysis) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-brain text-info me-2"></i>
                        AI Failure Analysis
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-robot me-2"></i>
                        AI has analyzed the migration failure:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${analysis}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="applyAIFixes()">Apply AI Fixes</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function showAIPreAnalysisModal(analysis) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-search-plus text-success me-2"></i>
                        AI Pre-Migration Analysis
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-robot me-2"></i>
                        AI pre-analysis of migration complexity and recommendations:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${analysis}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" onclick="startOptimizedMigration()">Start Optimized Migration</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

// Placeholder functions for future implementation
function implementAIRecommendations() {
    alert('Implement AI recommendations functionality will be added in backend');
}

function applyAIFixes() {
    alert('Apply AI fixes functionality will be added in backend');
}

function startOptimizedMigration() {
    alert('Start optimized migration functionality will be added in backend');
}
</script>
{% endblock %}
