#!/usr/bin/env python3

import tempfile
import os
from xml_safe_upgrader import XMLSafeUpgrader
from lxml import etree

# Create test XML
xml_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="test_view" model="ir.ui.view">
            <field name="arch" type="xml">
                <form>
                    <field name="name" nolabel="1"/>
                </form>
            </field>
        </record>
    </data>
</odoo>'''

# Create temporary file
temp_dir = tempfile.mkdtemp()
temp_file = os.path.join(temp_dir, 'test.xml')
with open(temp_file, 'w') as f:
    f.write(xml_content)

print("Original XML:")
print(xml_content)

# Parse and check
tree = etree.parse(temp_file)
root = tree.getroot()
print(f"\nRoot tag: {root.tag}")

# Check for nolabel attributes
nolabel_elements = root.xpath(".//*[@nolabel='1']")
print(f"Found {len(nolabel_elements)} elements with nolabel='1'")
for elem in nolabel_elements:
    print(f"  - {elem.tag} with nolabel='{elem.get('nolabel')}'")

# Test upgrader
upgrader = XMLSafeUpgrader()
result = upgrader.upgrade_xml_file(temp_file)
print(f"\nUpgrade result: {result}")
print(f"Changes made: {upgrader.changes_made}")

# Check transformed XML
tree_after = etree.parse(temp_file)
root_after = tree_after.getroot()
nolabel_elements_after = root_after.xpath(".//*[@nolabel]")
print(f"\nAfter upgrade, found {len(nolabel_elements_after)} elements with nolabel")

field_elements = root_after.xpath(".//field[@name='name']")
if field_elements:
    print(f"Field element class: '{field_elements[0].get('class', '')}'")
    print(f"Field element attributes: {field_elements[0].attrib}")

# Clean up
import shutil
shutil.rmtree(temp_dir)