"""
OpenUpgrade-inspired Analysis Engine for Odoo Module Migration

This module incorporates the proven OpenUpgrade methodology for analyzing 
module differences between Odoo versions and generating comprehensive
migration reports.
"""

import os
import ast
import json
import logging
import sqlite3
import tempfile
import xml.etree.ElementTree as ET
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Any, Optional

class OpenUpgradeAnalyzer:
    """
    Comprehensive module analyzer based on OpenUpgrade methodology
    Provides detailed migration reports, field analysis, and upgrade recommendations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.field_changes = []
        self.model_changes = []
        self.xmlid_changes = []
        self.data_changes = []
        self.workflow_changes = []
        
        # OpenUpgrade-style change tracking
        self.analysis_report = {
            'models_new': [],
            'models_removed': [],
            'fields_added': [],
            'fields_removed': [],
            'fields_changed': [],
            'xmlids_new': [],
            'xmlids_removed': [],
            'xmlids_changed': [],
            'dependencies_added': [],
            'dependencies_removed': [],
            'data_records_new': [],
            'data_records_removed': [],
            'workflow_changes': [],
            'migration_required': [],
            'warnings': [],
            'recommendations': []
        }
    
    def analyze_module_migration(self, source_module_path: str, target_version: str = '18.0') -> Dict[str, Any]:
        """
        Perform comprehensive OpenUpgrade-style analysis for module migration
        
        Args:
            source_module_path: Path to the source module
            target_version: Target Odoo version for migration
            
        Returns:
            Comprehensive analysis report with migration requirements
        """
        try:
            # Extract module if it's an archive
            if source_module_path.endswith(('.zip', '.tar', '.tar.gz')):
                module_root = self._extract_module(source_module_path)
            else:
                module_root = source_module_path
            
            # Parse manifest
            manifest_data = self._parse_manifest(module_root)
            source_version = self._determine_source_version(module_root, manifest_data)
            
            # Perform comprehensive analysis
            self._analyze_models(module_root, source_version, target_version)
            self._analyze_fields(module_root, source_version, target_version)
            self._analyze_xmlids(module_root, source_version, target_version)
            self._analyze_data_records(module_root, source_version, target_version)
            self._analyze_dependencies(module_root, manifest_data, target_version)
            self._analyze_workflows(module_root, source_version, target_version)
            
            # Generate migration requirements
            self._generate_migration_requirements(source_version, target_version)
            
            # Create OpenUpgrade-style analysis files
            analysis_files = self._generate_analysis_files(module_root)
            
            return {
                'source_version': source_version,
                'target_version': target_version,
                'manifest_data': manifest_data,
                'analysis_report': self.analysis_report,
                'migration_complexity': self._calculate_migration_complexity(),
                'estimated_effort': self._estimate_migration_effort(),
                'critical_issues': self._identify_critical_issues(),
                'analysis_files': analysis_files,
                'openupgrade_recommendations': self._generate_openupgrade_recommendations()
            }
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {str(e)}")
            return {'error': str(e), 'analysis_failed': True}
    
    def _extract_module(self, archive_path: str) -> str:
        """Extract module archive to temporary directory"""
        import zipfile
        import tarfile
        
        temp_dir = tempfile.mkdtemp()
        extract_path = os.path.join(temp_dir, 'module_extract')
        
        if archive_path.endswith('.zip'):
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
        elif archive_path.endswith(('.tar', '.tar.gz', '.tar.bz2')):
            with tarfile.open(archive_path, 'r:*') as tar_ref:
                tar_ref.extractall(extract_path)
        
        # Find module root directory
        extracted_items = os.listdir(extract_path)
        if len(extracted_items) == 1 and os.path.isdir(os.path.join(extract_path, extracted_items[0])):
            return os.path.join(extract_path, extracted_items[0])
        
        return extract_path
    
    def _parse_manifest(self, module_root: str) -> Dict[str, Any]:
        """Parse module manifest file"""
        manifest_files = ['__manifest__.py', '__openerp__.py']
        
        for manifest_file in manifest_files:
            manifest_path = os.path.join(module_root, manifest_file)
            if os.path.exists(manifest_path):
                try:
                    with open(manifest_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Safely evaluate manifest content
                    manifest_dict = ast.literal_eval(content)
                    return manifest_dict
                    
                except Exception as e:
                    self.logger.warning(f"Failed to parse {manifest_file}: {str(e)}")
        
        return {}
    
    def _determine_source_version(self, module_root: str, manifest_data: Dict) -> str:
        """Determine source Odoo version using OpenUpgrade methodology"""
        # Check manifest version
        if 'version' in manifest_data:
            version_str = manifest_data['version']
            if isinstance(version_str, str) and '.' in version_str:
                parts = version_str.split('.')
                if len(parts) >= 2:
                    major = parts[0]
                    if major.isdigit() and int(major) >= 8:
                        return f"{major}.0"
        
        # Check installable flag and manifest structure
        if manifest_data.get('installable', True):
            # Check for version-specific patterns
            if 'assets' in manifest_data:
                return '15.0'  # Assets introduced in v15
            
            if os.path.exists(os.path.join(module_root, '__openerp__.py')):
                return '13.0'  # Likely older version
        
        # Analyze Python files for API patterns
        python_analysis = self._analyze_python_api_patterns(module_root)
        if python_analysis:
            return python_analysis
        
        # Default to v13 for unknown
        return '13.0'
    
    def _analyze_python_api_patterns(self, module_root: str) -> Optional[str]:
        """Analyze Python files for version-specific API patterns"""
        api_patterns = {
            '13.0': ['@api.one', '@api.multi', '@api.returns'],
            '14.0': ['with_user(', 'sudo(user)'],
            '15.0': ['web.assets_', 'qweb'],
            '16.0': ['@odoo-module', 'import {'],
            '17.0': ['static template', 'Component.extend'],
            '18.0': ['spreadsheet', 'custom_properties']
        }
        
        content_all = ""
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content_all += f.read() + "\n"
                    except:
                        continue
        
        # Score versions based on pattern matches
        version_scores = {}
        for version, patterns in api_patterns.items():
            score = sum(content_all.count(pattern) for pattern in patterns)
            if score > 0:
                version_scores[version] = score
        
        if version_scores:
            return max(version_scores, key=version_scores.get)
        
        return None
    
    def _analyze_models(self, module_root: str, source_version: str, target_version: str):
        """Analyze model changes between versions"""
        models_found = set()
        
        # Parse Python files for model definitions
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Find model classes
                        if 'class ' in content and 'models.Model' in content:
                            models = self._extract_model_classes(content)
                            models_found.update(models)
                            
                    except Exception as e:
                        self.logger.warning(f"Error analyzing {file_path}: {str(e)}")
        
        # Store models for comparison
        self.analysis_report['models_found'] = list(models_found)
        
        # Check for version-specific model issues
        if source_version < target_version:
            self._check_model_compatibility(models_found, source_version, target_version)
    
    def _extract_model_classes(self, content: str) -> List[str]:
        """Extract model class names from Python content"""
        models = []
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check if class inherits from models.Model
                    for base in node.bases:
                        if (isinstance(base, ast.Attribute) and 
                            isinstance(base.value, ast.Name) and
                            base.value.id == 'models' and
                            base.attr == 'Model'):
                            
                            # Look for _name attribute
                            for item in node.body:
                                if (isinstance(item, ast.Assign) and
                                    len(item.targets) == 1 and
                                    isinstance(item.targets[0], ast.Name) and
                                    item.targets[0].id == '_name'):
                                    
                                    if isinstance(item.value, ast.Str):
                                        models.append(item.value.s)
                                    elif isinstance(item.value, ast.Constant):
                                        models.append(item.value.value)
        except:
            pass
        
        return models
    
    def _check_model_compatibility(self, models: set, source_version: str, target_version: str):
        """Check for model compatibility issues"""
        deprecated_models = {
            '14.0': ['account.invoice', 'account.invoice.line'],
            '15.0': ['ir.values'],
            '16.0': ['website.menu'],
            '17.0': [],
            '18.0': []
        }
        
        # Check for deprecated models
        for version, deprecated in deprecated_models.items():
            if source_version < version <= target_version:
                for model in models:
                    if model in deprecated:
                        self.analysis_report['warnings'].append(
                            f"Model '{model}' deprecated in {version}"
                        )
                        self.analysis_report['migration_required'].append({
                            'type': 'model_deprecated',
                            'model': model,
                            'version': version,
                            'action': 'Update model usage or find replacement'
                        })
    
    def _analyze_fields(self, module_root: str, source_version: str, target_version: str):
        """Analyze field changes - OpenUpgrade style"""
        fields_found = defaultdict(list)
        
        # Parse Python files for field definitions
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # Extract field definitions
                        field_info = self._extract_field_definitions(content)
                        for model, fields in field_info.items():
                            fields_found[model].extend(fields)
                            
                    except Exception as e:
                        self.logger.warning(f"Error analyzing fields in {file_path}: {str(e)}")
        
        # Store fields for analysis
        self.analysis_report['fields_found'] = dict(fields_found)
        
        # Check for version-specific field issues
        self._check_field_compatibility(fields_found, source_version, target_version)
    
    def _extract_field_definitions(self, content: str) -> Dict[str, List[Dict]]:
        """Extract field definitions from Python content"""
        fields_by_model = defaultdict(list)
        
        try:
            tree = ast.parse(content)
            current_model = None
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Check if this is a model class
                    for item in node.body:
                        if (isinstance(item, ast.Assign) and
                            len(item.targets) == 1 and
                            isinstance(item.targets[0], ast.Name) and
                            item.targets[0].id == '_name'):
                            
                            if isinstance(item.value, ast.Str):
                                current_model = item.value.s
                            elif isinstance(item.value, ast.Constant):
                                current_model = item.value.value
                            break
                    
                    # Extract field assignments
                    if current_model:
                        for item in node.body:
                            if isinstance(item, ast.Assign):
                                for target in item.targets:
                                    if isinstance(target, ast.Name):
                                        field_info = self._analyze_field_assignment(target.id, item.value)
                                        if field_info:
                                            fields_by_model[current_model].append({
                                                'name': target.id,
                                                **field_info
                                            })
                    
                    current_model = None
                    
        except Exception as e:
            self.logger.debug(f"AST parsing failed: {str(e)}")
        
        return dict(fields_by_model)
    
    def _analyze_field_assignment(self, field_name: str, value_node) -> Optional[Dict]:
        """Analyze field assignment to determine field type and properties"""
        if isinstance(value_node, ast.Call):
            if isinstance(value_node.func, ast.Attribute):
                if (isinstance(value_node.func.value, ast.Name) and 
                    value_node.func.value.id == 'fields'):
                    
                    field_type = value_node.func.attr
                    field_info = {'type': field_type}
                    
                    # Extract field parameters
                    for keyword in value_node.keywords:
                        if keyword.arg:
                            if isinstance(keyword.value, ast.Str):
                                field_info[keyword.arg] = keyword.value.s
                            elif isinstance(keyword.value, ast.Constant):
                                field_info[keyword.arg] = keyword.value.value
                            elif isinstance(keyword.value, ast.Name):
                                field_info[keyword.arg] = keyword.value.id
                    
                    return field_info
        
        return None
    
    def _check_field_compatibility(self, fields_found: Dict, source_version: str, target_version: str):
        """Check field compatibility issues"""
        deprecated_field_params = {
            '14.0': ['size'],  # size parameter deprecated
            '15.0': ['track_visibility'],  # replaced with tracking
            '16.0': ['group_operator'],  # some parameters deprecated
            '17.0': [],
            '18.0': []
        }
        
        for version, deprecated_params in deprecated_field_params.items():
            if source_version < version <= target_version:
                for model, fields in fields_found.items():
                    for field in fields:
                        for param in deprecated_params:
                            if param in field:
                                self.analysis_report['warnings'].append(
                                    f"Field parameter '{param}' deprecated in {version} "
                                    f"for {model}.{field['name']}"
                                )
                                self.analysis_report['migration_required'].append({
                                    'type': 'field_parameter_deprecated',
                                    'model': model,
                                    'field': field['name'],
                                    'parameter': param,
                                    'version': version,
                                    'action': f'Remove or replace {param} parameter'
                                })
    
    def _analyze_xmlids(self, module_root: str, source_version: str, target_version: str):
        """Analyze XML IDs and data records"""
        xmlids_found = []
        
        # Parse XML files for data records and IDs
        for root, dirs, files in os.walk(module_root):
            for file in files:
                if file.endswith('.xml'):
                    file_path = os.path.join(root, file)
                    try:
                        tree = ET.parse(file_path)
                        root_elem = tree.getroot()
                        
                        # Extract record IDs
                        for record in root_elem.findall('.//record'):
                            xmlid = record.get('id')
                            model = record.get('model')
                            if xmlid and model:
                                xmlids_found.append({
                                    'id': xmlid,
                                    'model': model,
                                    'file': os.path.relpath(file_path, module_root)
                                })
                        
                        # Extract template IDs
                        for template in root_elem.findall('.//template'):
                            template_id = template.get('id')
                            if template_id:
                                xmlids_found.append({
                                    'id': template_id,
                                    'model': 'ir.ui.view',
                                    'file': os.path.relpath(file_path, module_root),
                                    'type': 'template'
                                })
                                
                    except Exception as e:
                        self.logger.warning(f"Error parsing XML {file_path}: {str(e)}")
        
        self.analysis_report['xmlids_found'] = xmlids_found
        self._check_xmlid_compatibility(xmlids_found, source_version, target_version)
    
    def _check_xmlid_compatibility(self, xmlids: List[Dict], source_version: str, target_version: str):
        """Check for XML ID compatibility issues"""
        deprecated_xmlids = {
            '14.0': ['account.invoice_form', 'account.invoice_tree'],
            '15.0': ['web.assets_common', 'base.view_partner_form'],
            '16.0': ['website.website_menu'],
            '17.0': [],
            '18.0': []
        }
        
        for version, deprecated in deprecated_xmlids.items():
            if source_version < version <= target_version:
                for xmlid_info in xmlids:
                    if xmlid_info['id'] in deprecated:
                        self.analysis_report['warnings'].append(
                            f"XML ID '{xmlid_info['id']}' deprecated in {version}"
                        )
                        self.analysis_report['migration_required'].append({
                            'type': 'xmlid_deprecated',
                            'xmlid': xmlid_info['id'],
                            'model': xmlid_info['model'],
                            'version': version,
                            'action': 'Update XML ID reference or find replacement'
                        })
    
    def _analyze_data_records(self, module_root: str, source_version: str, target_version: str):
        """Analyze data records for migration issues"""
        # This would analyze CSV files, XML data records, etc.
        # Implementation depends on specific requirements
        pass
    
    def _analyze_dependencies(self, module_root: str, manifest_data: Dict, target_version: str):
        """Analyze module dependencies for compatibility"""
        depends = manifest_data.get('depends', [])
        external_depends = manifest_data.get('external_dependencies', {})
        
        # Check for deprecated dependencies
        deprecated_modules = {
            '14.0': ['account_analytic_default'],
            '15.0': ['web_tree_many2one_clickable'],
            '16.0': ['website_sale_delivery'],
            '17.0': [],
            '18.0': []
        }
        
        for version, deprecated in deprecated_modules.items():
            if target_version >= version:
                for dep in depends:
                    if dep in deprecated:
                        self.analysis_report['warnings'].append(
                            f"Dependency '{dep}' deprecated in {version}"
                        )
                        self.analysis_report['migration_required'].append({
                            'type': 'dependency_deprecated',
                            'dependency': dep,
                            'version': version,
                            'action': 'Remove dependency or find replacement'
                        })
    
    def _analyze_workflows(self, module_root: str, source_version: str, target_version: str):
        """Analyze workflow definitions (deprecated in newer versions)"""
        if source_version < '11.0' and target_version >= '11.0':
            # Check for workflow definitions
            for root, dirs, files in os.walk(module_root):
                for file in files:
                    if file.endswith('.xml'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            if 'workflow' in content.lower():
                                self.analysis_report['warnings'].append(
                                    f"Workflow definitions found in {file} - workflows deprecated in v11+"
                                )
                                self.analysis_report['migration_required'].append({
                                    'type': 'workflow_deprecated',
                                    'file': file,
                                    'action': 'Convert workflow to server actions or custom logic'
                                })
                        except:
                            pass
    
    def _generate_migration_requirements(self, source_version: str, target_version: str):
        """Generate comprehensive migration requirements"""
        version_upgrades = self._get_version_upgrade_path(source_version, target_version)
        
        for i, (from_ver, to_ver) in enumerate(version_upgrades):
            requirements = self._get_version_specific_requirements(from_ver, to_ver)
            self.analysis_report['migration_required'].extend(requirements)
    
    def _get_version_upgrade_path(self, source: str, target: str) -> List[Tuple[str, str]]:
        """Get the step-by-step upgrade path"""
        versions = ['13.0', '14.0', '15.0', '16.0', '17.0', '18.0']
        
        try:
            start_idx = versions.index(source)
            end_idx = versions.index(target)
            
            path = []
            for i in range(start_idx, end_idx):
                path.append((versions[i], versions[i + 1]))
            
            return path
            
        except ValueError:
            return [(source, target)]
    
    def _get_version_specific_requirements(self, from_ver: str, to_ver: str) -> List[Dict]:
        """Get requirements for specific version upgrade"""
        requirements = []
        
        upgrade_requirements = {
            ('13.0', '14.0'): [
                {
                    'type': 'api_decorator_removal',
                    'action': 'Remove @api.one, @api.multi, @api.returns decorators',
                    'description': 'Replace with proper iteration patterns'
                },
                {
                    'type': 'sudo_method_update',
                    'action': 'Replace sudo() calls with with_user()',
                    'description': 'Update security context management'
                }
            ],
            ('14.0', '15.0'): [
                {
                    'type': 'assets_manifest',
                    'action': 'Move assets from XML to __manifest__.py',
                    'description': 'Update asset loading mechanism'
                },
                {
                    'type': 'qweb_templates',
                    'action': 'Move QWeb templates to web.assets_qweb bundle',
                    'description': 'Update template loading'
                }
            ],
            ('15.0', '16.0'): [
                {
                    'type': 'owl_migration',
                    'action': 'Convert legacy JavaScript to OWL components',
                    'description': 'Modernize frontend framework'
                },
                {
                    'type': 'es6_modules',
                    'action': 'Convert to ES6 module syntax',
                    'description': 'Update JavaScript import/export patterns'
                }
            ],
            ('16.0', '17.0'): [
                {
                    'type': 'advanced_owl',
                    'action': 'Update to advanced OWL patterns',
                    'description': 'Optimize component architecture'
                }
            ],
            ('17.0', '18.0'): [
                {
                    'type': 'performance_optimization',
                    'action': 'Apply performance optimizations',
                    'description': 'Leverage 3.7x performance improvements'
                }
            ]
        }
        
        return upgrade_requirements.get((from_ver, to_ver), [])
    
    def _calculate_migration_complexity(self) -> str:
        """Calculate migration complexity based on analysis"""
        issues = len(self.analysis_report['migration_required'])
        warnings = len(self.analysis_report['warnings'])
        
        total_score = issues * 2 + warnings
        
        if total_score <= 5:
            return 'Low'
        elif total_score <= 15:
            return 'Medium'
        elif total_score <= 30:
            return 'High'
        else:
            return 'Very High'
    
    def _estimate_migration_effort(self) -> str:
        """Estimate migration effort in developer days"""
        complexity = self._calculate_migration_complexity()
        
        effort_map = {
            'Low': '1-2 days',
            'Medium': '3-5 days',
            'High': '1-2 weeks',
            'Very High': '2-4 weeks'
        }
        
        return effort_map.get(complexity, 'Unknown')
    
    def _identify_critical_issues(self) -> List[Dict]:
        """Identify critical migration issues"""
        critical = []
        
        for req in self.analysis_report['migration_required']:
            if req['type'] in ['model_deprecated', 'workflow_deprecated', 'dependency_deprecated']:
                critical.append(req)
        
        return critical
    
    def _generate_analysis_files(self, module_root: str) -> Dict[str, str]:
        """Generate OpenUpgrade-style analysis files"""
        files = {}
        
        # Generate openupgrade_analysis.txt
        analysis_content = self._format_analysis_txt()
        files['openupgrade_analysis.txt'] = analysis_content
        
        # Generate openupgrade_analysis_work.txt (copy with annotations)
        work_content = analysis_content + "\n\n# Work annotations:\n# TODO: Review and implement required changes\n"
        files['openupgrade_analysis_work.txt'] = work_content
        
        # Generate migration script templates
        files['pre-migration.py'] = self._generate_pre_migration_template()
        files['post-migration.py'] = self._generate_post_migration_template()
        files['end-migration.py'] = self._generate_end_migration_template()
        
        return files
    
    def _format_analysis_txt(self) -> str:
        """Format analysis in OpenUpgrade style"""
        content = []
        content.append("# OpenUpgrade Analysis Report")
        content.append("# Generated by Odoo Module Automation System")
        content.append("")
        
        # Models section
        content.append("## Models")
        for model in self.analysis_report.get('models_found', []):
            content.append(f"+ {model}")
        content.append("")
        
        # Fields section
        content.append("## Fields")
        for model, fields in self.analysis_report.get('fields_found', {}).items():
            for field in fields:
                field_desc = f"{model} / {field['name']} ({field.get('type', 'unknown')})"
                content.append(f"+ {field_desc}")
        content.append("")
        
        # XML IDs section
        content.append("## XML IDs")
        for xmlid in self.analysis_report.get('xmlids_found', []):
            content.append(f"+ {xmlid['id']} ({xmlid['model']})")
        content.append("")
        
        # Migration requirements
        content.append("## Migration Requirements")
        for req in self.analysis_report['migration_required']:
            content.append(f"- {req['type']}: {req['action']}")
        content.append("")
        
        # Warnings
        content.append("## Warnings")
        for warning in self.analysis_report['warnings']:
            content.append(f"! {warning}")
        
        return "\n".join(content)
    
    def _generate_pre_migration_template(self) -> str:
        """Generate pre-migration script template"""
        return '''from openupgradelib import openupgrade

@openupgrade.migrate()
def migrate(env, version):
    """Pre-migration script"""
    # Add column renames, table renames, etc.
    
    # Example: Rename columns
    # openupgrade.rename_columns(env.cr, {
    #     'table_name': [
    #         ('old_column', 'new_column', None),
    #     ],
    # })
    
    pass
'''
    
    def _generate_post_migration_template(self) -> str:
        """Generate post-migration script template"""
        return '''from openupgradelib import openupgrade

@openupgrade.migrate()
def migrate(env, version):
    """Post-migration script"""
    # Add data migrations, field migrations, etc.
    
    # Example: Load new data
    # openupgrade.load_data(env.cr, 'module_name', 'data/data_file.xml')
    
    pass
'''
    
    def _generate_end_migration_template(self) -> str:
        """Generate end-migration script template"""
        return '''from openupgradelib import openupgrade

@openupgrade.migrate()
def migrate(env, version):
    """End-migration script"""
    # Final cleanup operations
    
    # Example: Clean up obsolete data
    # openupgrade.clean_transient_models(env.cr)
    
    pass
'''
    
    def _generate_openupgrade_recommendations(self) -> List[str]:
        """Generate specific OpenUpgrade-style recommendations"""
        recommendations = []
        
        # Add general recommendations
        recommendations.append("1. Create backup of database before migration")
        recommendations.append("2. Test migration on copy of production database")
        recommendations.append("3. Review all identified warnings and migration requirements")
        
        # Add specific recommendations based on findings
        if any(req['type'] == 'api_decorator_removal' for req in self.analysis_report['migration_required']):
            recommendations.append("4. Carefully review @api.one/@api.multi decorator removals")
            recommendations.append("5. Ensure proper iteration patterns in affected methods")
        
        if any(req['type'] == 'assets_manifest' for req in self.analysis_report['migration_required']):
            recommendations.append("6. Verify all assets load correctly after manifest migration")
            recommendations.append("7. Test frontend functionality thoroughly")
        
        if any(req['type'] == 'owl_migration' for req in self.analysis_report['migration_required']):
            recommendations.append("8. Test all JavaScript functionality after OWL migration")
            recommendations.append("9. Verify component interactions work correctly")
        
        return recommendations