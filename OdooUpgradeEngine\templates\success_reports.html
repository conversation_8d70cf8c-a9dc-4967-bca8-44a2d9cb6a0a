{% extends "base.html" %}
{% set title = "Success Reports" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-chart-bar me-3"></i>
            Success Reports
        </h1>
        <p class="lead">Success metrics and migration performance reports</p>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-trophy text-success fa-2x mb-2"></i>
                <h5 class="card-title">Success Rate</h5>
                <h2 class="text-success">{{ success_rate|round(1) }}%</h2>
                <small class="text-muted">{{ completed_jobs }}/{{ total_jobs }} migrations</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-rocket text-info fa-2x mb-2"></i>
                <h5 class="card-title">Total Completed</h5>
                <h2 class="text-info">{{ completed_jobs }}</h2>
                <small class="text-muted">Successful migrations</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                <h5 class="card-title">Failed</h5>
                <h2 class="text-warning">{{ failed_jobs }}</h2>
                <small class="text-muted">Need attention</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-clock text-primary fa-2x mb-2"></i>
                <h5 class="card-title">Avg Time</h5>
                <h2 class="text-primary">2.5h</h2>
                <small class="text-muted">Per migration</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Migration Status Distribution
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col">
                            <span class="badge bg-success">Completed: {{ completed_jobs }}</span>
                        </div>
                        <div class="col">
                            <span class="badge bg-danger">Failed: {{ failed_jobs }}</span>
                        </div>
                        <div class="col">
                            <span class="badge bg-warning">In Progress: {{ (total_jobs - completed_jobs - failed_jobs) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Success Trend (Last 30 Days)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="trendChart" width="400" height="200"></canvas>
                <div class="mt-3 text-center">
                    <small class="text-muted">Migration success rate over time</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Performance Metrics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6>Fastest Migration</h6>
                            <h4 class="text-success">45 minutes</h4>
                            <small class="text-muted">Simple module upgrade</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6>Average Duration</h6>
                            <h4 class="text-info">2h 30m</h4>
                            <small class="text-muted">Typical migration time</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6>Longest Migration</h6>
                            <h4 class="text-warning">6h 15m</h4>
                            <small class="text-muted">Complex enterprise module</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Successes -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-star me-2"></i>
            Recent Successful Migrations
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Module</th>
                        <th>Version</th>
                        <th>Duration</th>
                        <th>Completed</th>
                        <th>Quality Score</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>sale_management</strong></td>
                        <td><span class="badge bg-info">15.0</span> → <span class="badge bg-success">18.0</span></td>
                        <td>1h 45m</td>
                        <td>2 hours ago</td>
                        <td><span class="badge bg-success">95%</span></td>
                    </tr>
                    <tr>
                        <td><strong>account_invoice</strong></td>
                        <td><span class="badge bg-info">16.0</span> → <span class="badge bg-success">18.0</span></td>
                        <td>2h 15m</td>
                        <td>4 hours ago</td>
                        <td><span class="badge bg-success">92%</span></td>
                    </tr>
                    <tr>
                        <td><strong>stock_management</strong></td>
                        <td><span class="badge bg-info">15.0</span> → <span class="badge bg-success">17.0</span></td>
                        <td>3h 30m</td>
                        <td>1 day ago</td>
                        <td><span class="badge bg-warning">78%</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Placeholder for chart initialization
// Charts will be implemented with Chart.js in Phase 4
console.log('Success reports page loaded');

// Simple chart placeholders
document.addEventListener('DOMContentLoaded', function() {
    // Status Chart placeholder
    const statusCanvas = document.getElementById('statusChart');
    if (statusCanvas) {
        const ctx = statusCanvas.getContext('2d');
        ctx.fillStyle = '#28a745';
        ctx.fillRect(10, 10, 100, 50);
        ctx.fillStyle = '#dc3545';
        ctx.fillRect(120, 10, 50, 50);
        ctx.fillStyle = '#ffc107';
        ctx.fillRect(180, 10, 30, 50);
        
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.fillText('Chart will be implemented in Phase 4', 50, 100);
    }
    
    // Trend Chart placeholder
    const trendCanvas = document.getElementById('trendChart');
    if (trendCanvas) {
        const ctx = trendCanvas.getContext('2d');
        ctx.strokeStyle = '#007bff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(10, 150);
        ctx.lineTo(100, 100);
        ctx.lineTo(200, 80);
        ctx.lineTo(300, 60);
        ctx.stroke();
        
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.fillText('Trend chart will be implemented in Phase 4', 50, 180);
    }
});
</script>
{% endblock %}
