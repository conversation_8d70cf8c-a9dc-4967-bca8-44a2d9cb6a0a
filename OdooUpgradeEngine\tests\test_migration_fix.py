#!/usr/bin/env python3
"""
Test Migration Fix - Validate End-to-End Migration Pipeline

This script tests the complete migration pipeline with real modules
to confirm the timeout fix works and migration jobs are created properly.
"""

import os
import sys
import time
import json
from datetime import datetime

def setup_environment():
    """Setup test environment"""
    from test_config import setup_test_environment
    setup_test_environment()

def test_migration_pipeline_with_real_modules():
    """Test the complete migration pipeline with existing real modules"""
    print("🧪 TESTING MIGRATION PIPELINE WITH REAL MODULES")
    print("=" * 60)
    
    try:
        from app import create_app, db
        from models import OdooModule, MigrationJob
        from pipeline_migration_orchestrator import PipelineMigrationOrchestrator
        
        app = create_app()
        with app.app_context():
            # Get existing modules from our real data testing
            existing_modules = OdooModule.query.limit(5).all()
            
            if not existing_modules:
                print("❌ No existing modules found. Run GitHub puller first.")
                return False
            
            print(f"✅ Found {len(existing_modules)} existing modules")
            
            # Test migration job creation for each module
            migration_jobs_created = []
            
            for module in existing_modules:
                print(f"\n📦 Testing module: {module.name} (ID: {module.id})")
                
                try:
                    # Create direct migration job
                    migration_job = MigrationJob(
                        module_id=module.id,
                        target_version="18.0",
                        status='QUEUED',
                        timestamp=datetime.now(),
                        log=f"Test migration for real module: {module.name}"
                    )
                    
                    db.session.add(migration_job)
                    db.session.flush()
                    
                    print(f"   ✅ Created migration job: ID {migration_job.id}")
                    
                    # Test pipeline orchestrator
                    orchestrator = PipelineMigrationOrchestrator(
                        module_id=module.id,
                        target_version="18.0",
                        enable_pipeline=True
                    )
                    
                    pipeline_result = orchestrator.start_pipeline_migration()
                    
                    if pipeline_result['success']:
                        print(f"   ✅ Pipeline created: {pipeline_result['total_steps']} steps")
                        print(f"   🎯 First job: {pipeline_result['first_job_id']}")
                        
                        migration_jobs_created.append({
                            'module_name': module.name,
                            'module_id': module.id,
                            'direct_job_id': migration_job.id,
                            'pipeline_job_id': pipeline_result['first_job_id'],
                            'pipeline_steps': pipeline_result['total_steps']
                        })
                    else:
                        print(f"   ❌ Pipeline failed: {pipeline_result}")
                        
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    continue
            
            # Commit all changes
            db.session.commit()
            
            print(f"\n📊 MIGRATION PIPELINE TEST RESULTS:")
            print(f"   ✅ Modules tested: {len(existing_modules)}")
            print(f"   ✅ Migration jobs created: {len(migration_jobs_created)}")
            
            # Verify jobs in database
            total_jobs = MigrationJob.query.count()
            recent_jobs = MigrationJob.query.order_by(MigrationJob.timestamp.desc()).limit(10).all()
            
            print(f"   📈 Total jobs in database: {total_jobs}")
            print(f"   🕒 Recent jobs:")
            for job in recent_jobs[:5]:
                print(f"      - Job {job.id}: {job.status} (Module: {job.module_id})")
            
            return len(migration_jobs_created) > 0
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_with_small_batch():
    """Test the API endpoint with a small batch to verify timeout fix"""
    print("\n🌐 TESTING API ENDPOINT WITH SMALL BATCH")
    print("=" * 60)
    
    try:
        import requests
        
        # Test with a very small repository or limit
        print("   🚀 Testing /api/github/pull-modules with limit=1...")
        
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:5003/api/github/pull-modules",
            json={
                "repository_url": "https://github.com/OCA/server-tools",
                "target_version": "18.0",
                "migration_mode": "direct",
                "limit": 1  # Force small batch
            },
            timeout=45  # Reasonable timeout
        )
        
        elapsed = time.time() - start_time
        
        print(f"   ⏱️  Response received in {elapsed:.1f} seconds")
        print(f"   📡 Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success')}")
            print(f"   📊 Processing mode: {data.get('processing_mode', 'sync')}")
            
            if data.get('modules'):
                print(f"   📦 Modules processed: {len(data['modules'])}")
                for module in data['modules']:
                    print(f"      - {module['name']}: Job {module.get('migration_job_id')}")
            
            return True
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   📄 Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print(f"   ❌ TIMEOUT: API still taking too long (>{elapsed:.1f}s)")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def verify_migration_jobs_for_real_modules():
    """Verify that real modules have migration jobs"""
    print("\n🔍 VERIFYING MIGRATION JOBS FOR REAL MODULES")
    print("=" * 60)
    
    try:
        from app import create_app, db
        from models import OdooModule, MigrationJob
        
        app = create_app()
        with app.app_context():
            # Get all modules
            all_modules = OdooModule.query.all()
            
            # Check which modules have migration jobs
            modules_with_jobs = []
            modules_without_jobs = []
            
            for module in all_modules:
                jobs = MigrationJob.query.filter_by(module_id=module.id).all()
                if jobs:
                    modules_with_jobs.append({
                        'module': module.name,
                        'module_id': module.id,
                        'job_count': len(jobs),
                        'latest_status': jobs[-1].status if jobs else None
                    })
                else:
                    modules_without_jobs.append({
                        'module': module.name,
                        'module_id': module.id
                    })
            
            print(f"   📊 Total modules: {len(all_modules)}")
            print(f"   ✅ Modules with jobs: {len(modules_with_jobs)}")
            print(f"   ❌ Modules without jobs: {len(modules_without_jobs)}")
            
            if modules_with_jobs:
                print(f"   🎯 Modules with migration jobs:")
                for item in modules_with_jobs[:5]:
                    print(f"      - {item['module']}: {item['job_count']} jobs ({item['latest_status']})")
            
            if modules_without_jobs:
                print(f"   ⚠️  Modules without migration jobs:")
                for item in modules_without_jobs[:5]:
                    print(f"      - {item['module']} (ID: {item['module_id']})")
            
            # Calculate success rate
            success_rate = (len(modules_with_jobs) / len(all_modules)) * 100 if all_modules else 0
            print(f"   📈 Migration job coverage: {success_rate:.1f}%")
            
            return success_rate > 50  # At least 50% should have jobs
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main test execution"""
    print("🚨 MIGRATION PIPELINE FIX VALIDATION")
    print("=" * 70)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Setup environment
    setup_environment()
    
    # Wait for Flask server
    print("\n🔄 Starting Flask server...")
    import subprocess
    import sys
    
    flask_process = subprocess.Popen([
        sys.executable, "-c",
        "from app import create_app; app = create_app(); app.run(host='0.0.0.0', port=5003, debug=False)"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    time.sleep(5)
    
    # Run tests
    results = {}
    
    # Test 1: Migration pipeline with existing modules
    results['migration_pipeline'] = test_migration_pipeline_with_real_modules()
    
    # Test 2: API endpoint with small batch
    results['api_endpoint'] = test_api_with_small_batch()
    
    # Test 3: Verify migration job coverage
    results['job_coverage'] = verify_migration_jobs_for_real_modules()
    
    # Cleanup
    try:
        flask_process.terminate()
        flask_process.wait(timeout=5)
    except:
        flask_process.kill()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 MIGRATION FIX VALIDATION SUMMARY")
    print("=" * 70)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name.replace('_', ' ').title()}")
    
    overall_success = all(results.values())
    
    if overall_success:
        print(f"\n🎉 ALL TESTS PASSED - Migration pipeline fix is working!")
        print(f"   ✅ Real modules can be migrated")
        print(f"   ✅ API timeout issue resolved")
        print(f"   ✅ Migration jobs created properly")
    else:
        print(f"\n⚠️  SOME TESTS FAILED - Further investigation needed")
        failed_tests = [name for name, success in results.items() if not success]
        print(f"   ❌ Failed: {', '.join(failed_tests)}")
    
    # Save results
    with open('migration_fix_validation.json', 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'results': results,
            'overall_success': overall_success
        }, f, indent=2)
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
