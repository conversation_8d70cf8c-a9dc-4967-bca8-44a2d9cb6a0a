<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}Odoo Module Analyzer</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <style>
        /* Sidebar Styles */
        body {
            padding-top: 56px; /* Account for fixed navbar */
        }

        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            overflow-y: auto;
            background-color: #6f42c1 !important; /* Purple background to match header */
        }

        /* Override Bootstrap's bg-light class */
        .sidebar.bg-light {
            background-color: #6f42c1 !important;
        }

        .sidebar .nav-link {
            color: #ffffff; /* White text for better contrast on purple background */
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin: 0.125rem 0.5rem;
            text-decoration: none;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #000000; /* Black text on hover for contrast */
            background-color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .sidebar .nav-link.active {
            color: #000000; /* Black text for active page for contrast */
            background-color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
        }

        .sidebar-heading {
            font-size: 0.75rem;
            text-transform: uppercase;
            color: #ffffff; /* White headings for better contrast */
            font-weight: 700;
        }

        /* Collapsible Section Styles */
        .sidebar-section {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-section-header {
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            color: #ffffff; /* White text for better contrast */
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.25rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .sidebar-section-header:hover {
            background-color: rgba(255, 255, 255, 0.8);
            color: #000000; /* Black text on hover for contrast */
        }

        .collapse-icon {
            transition: transform 0.3s ease;
        }

        .sidebar-section-header[aria-expanded="false"] .collapse-icon {
            transform: rotate(-90deg);
        }

        .sidebar-section .nav-link {
            padding-left: 2rem;
            font-size: 0.9rem;
        }

        /* Main content area */
        main {
            margin-top: 0;
            padding-top: 20px;
        }

        /* Mobile responsiveness */
        @media (max-width: 767.98px) {
            .sidebar {
                top: 56px;
                position: relative;
                height: auto;
                padding: 0;
            }
        }

        /* Dark theme adjustments - keep purple background */
        [data-bs-theme="dark"] .sidebar {
            background-color: #5a2d91; /* Darker purple for dark theme */
            border-right: 1px solid #6f42c1;
        }

        [data-bs-theme="dark"] .sidebar .nav-link {
            color: #ffffff; /* White text in dark theme for better contrast */
        }

        [data-bs-theme="dark"] .sidebar .nav-link:hover {
            color: #000000; /* Black text on hover in dark theme */
            background-color: rgba(255, 255, 255, 0.3);
        }

        [data-bs-theme="dark"] .sidebar .nav-link.active {
            color: #000000; /* Black text for active in dark theme */
            background-color: rgba(255, 255, 255, 0.4);
        }

        [data-bs-theme="dark"] .sidebar-heading {
            color: #ffffff; /* White headings in dark theme */
        }

        /* Dark theme collapsible sections */
        [data-bs-theme="dark"] .sidebar-section-header {
            color: #ffffff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        [data-bs-theme="dark"] .sidebar-section-header:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: #000000;
        }
    </style>
</head>
<body>
    <!-- Top Header Bar -->
    <nav class="navbar navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-cogs me-2"></i>Odoo Upgrade Engine
            </a>
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>

    <!-- Sidebar Navigation -->
    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- 📊 CORE WORKFLOW -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#coreWorkflow" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-chart-line me-2"></i>CORE WORKFLOW</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="coreWorkflow">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.index') }}">
                                        <i class="fas fa-home me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_orchestrator') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>Migration Orchestrator
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_types') }}">
                                        <i class="fas fa-route me-2"></i>Migration Types
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_jobs') }}">
                                        <i class="fas fa-tasks me-2"></i>Migration Jobs
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 📤 START MIGRATION -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#startMigration" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-play me-2 text-success"></i>START MIGRATION</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="startMigration">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.upload_modules_page') }}">
                                        <i class="fas fa-upload me-2"></i>Upload Modules
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.github_integration') }}">
                                        <i class="fab fa-github me-2"></i>GitHub Sync
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.bulk_migration') }}">
                                        <i class="fas fa-layer-group me-2"></i>Bulk Upload
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.contributor_upload') }}">
                                        <i class="fas fa-users me-2"></i>Contribute Modules
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 🎛️ PROCESS & MONITOR -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#processMonitor" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-cogs me-2 text-primary"></i>PROCESS & MONITOR</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="processMonitor">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_orchestrator') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>Migration Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_jobs') }}">
                                        <i class="fas fa-tasks me-2"></i>Active Jobs
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('automation.automation_dashboard') }}">
                                        <i class="fas fa-robot me-2"></i>AI Automation
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 👁️ REVIEW & APPROVE -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#reviewApprove" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-eye me-2 text-warning"></i>REVIEW & APPROVE</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="reviewApprove">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.manual_interventions') }}">
                                        <i class="fas fa-gavel me-2"></i>Pending Reviews
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.analyze_modules') }}">
                                        <i class="fas fa-search me-2"></i>Code Analysis
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_results') }}">
                                        <i class="fas fa-chart-line me-2"></i>Migration Results
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.review_queue') }}">
                                        <i class="fas fa-list-check me-2"></i>Review Queue
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- ✅ COMPLETED & HISTORY -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#completedHistory" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-check-circle me-2 text-success"></i>COMPLETED & HISTORY</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="completedHistory">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.completed_migrations') }}">
                                        <i class="fas fa-check-double me-2"></i>Completed Migrations
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.success_reports') }}">
                                        <i class="fas fa-chart-bar me-2"></i>Success Reports
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.performance_analytics') }}">
                                        <i class="fas fa-analytics me-2"></i>Performance Analytics
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.migration_history') }}">
                                        <i class="fas fa-history me-2"></i>Migration History
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 🧪 TESTING & VALIDATION -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#testingValidation" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-flask me-2 text-info"></i>TESTING & VALIDATION</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="testingValidation">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.testing_dashboard') }}">
                                        <i class="fas fa-vial me-2"></i>Testing Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.docker_environments') }}">
                                        <i class="fab fa-docker me-2"></i>Docker Environments
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.test_results') }}">
                                        <i class="fas fa-clipboard-check me-2"></i>Test Results
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- ⚙️ CONFIGURE & SETTINGS -->
                    <div class="sidebar-section mb-3">
                        <h6 class="sidebar-section-header d-flex justify-content-between align-items-center px-3 py-2 mb-1"
                           data-bs-toggle="collapse" data-bs-target="#configureSettings" aria-expanded="true" style="cursor: pointer;">
                            <span><i class="fas fa-cog me-2 text-secondary"></i>CONFIGURE & SETTINGS</span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </h6>
                        <div class="collapse show" id="configureSettings">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.ai_providers') }}">
                                        <i class="fas fa-brain me-2"></i>AI Providers
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.ai_learning_dashboard') }}">
                                        <i class="fas fa-chart-line me-2"></i>AI Learning
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.health_dashboard') }}">
                                        <i class="fas fa-heartbeat me-2"></i>Health Monitor
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('main.system_settings') }}">
                                        <i class="fas fa-sliders-h me-2"></i>System Settings
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="pt-3 pb-2 mb-3">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <!-- Page Content -->
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Odoo Module Analyzer</h5>
                    <p class="mb-0">Analyze and upgrade Odoo modules (v13-v18)</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-code me-1"></i>
                        Built with Flask & Bootstrap
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- AI Integration JS -->
    <script src="{{ url_for('static', filename='js/ai-integration.js') }}"></script>

    <!-- Real-Time Updates JS -->
    <script src="{{ url_for('static', filename='js/real-time-updates.js') }}"></script>

    <!-- Search and Filter JS -->
    <script src="{{ url_for('static', filename='js/search-filter.js') }}"></script>

    <script>
        // Highlight active navigation item and handle collapsible sections
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            // Highlight active navigation item
            navLinks.forEach(link => {
                link.classList.remove('active');
                const linkPath = new URL(link.href).pathname;

                if (currentPath === linkPath ||
                    (currentPath === '/' && linkPath === '/') ||
                    (currentPath.startsWith(linkPath) && linkPath !== '/')) {
                    link.classList.add('active');

                    // Ensure the section containing the active link is expanded
                    const activeSection = link.closest('.collapse');
                    if (activeSection && !activeSection.classList.contains('show')) {
                        const bsCollapse = new bootstrap.Collapse(activeSection, {show: true});
                    }
                }
            });

            // Handle section header clicks and remember state
            const sectionHeaders = document.querySelectorAll('.sidebar-section-header');
            sectionHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const target = this.getAttribute('data-bs-target');
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';

                    // Save state to localStorage
                    localStorage.setItem(target + '_collapsed', isExpanded);
                });

                // Restore state from localStorage
                const target = header.getAttribute('data-bs-target');
                const wasCollapsed = localStorage.getItem(target + '_collapsed') === 'true';

                if (wasCollapsed) {
                    const targetElement = document.querySelector(target);
                    if (targetElement) {
                        targetElement.classList.remove('show');
                        header.setAttribute('aria-expanded', 'false');
                    }
                }
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
