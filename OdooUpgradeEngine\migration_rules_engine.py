"""
Migration Rules Engine - Version-Specific Transformation Rules

This module implements the rule-based transformation engine that handles
predictable, non-ambiguous code changes across Odoo versions. It replaces
hardcoded transformations with configurable, testable rules.

Features:
- Version-specific migration rules (13.0->14.0, 14.0->15.0, etc.)
- Rule categories: manifest, python, xml, database
- AST-based Python transformations
- lxml-based XML transformations  
- Manifest updates and asset restructuring
- Database schema change tracking
"""

import ast
import json
import logging
import os
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from lxml import etree
import hashlib

# Import our existing components
from ast_based_upgrader import ProfessionalASTUpgrader, ApiOneRemover, SelfToRecordTransformer
from xml_safe_upgrader import XMLSafeUpgrader

@dataclass
class MigrationRule:
    """Represents a single migration rule"""
    rule_id: str
    source_version: str
    target_version: str
    category: str  # 'manifest', 'python', 'xml', 'database'
    rule_type: str  # 'decorator', 'import', 'field', 'attribute', etc.
    pattern: str
    replacement: str
    file_pattern: str = "*"  # File pattern to match (e.g., "*.py", "*.xml")
    description: str = ""
    requires_manual: bool = False
    complexity: str = "simple"  # simple, medium, complex
    validation_regex: Optional[str] = None
    
    def __post_init__(self):
        if not self.description:
            self.description = f"{self.rule_type} rule: {self.pattern} -> {self.replacement}"

@dataclass
class RuleApplicationResult:
    """Result of applying a rule to a file"""
    rule_id: str
    file_path: str
    applied: bool
    changes_made: int = 0
    original_content_hash: str = ""
    new_content_hash: str = ""
    error_message: str = ""
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []

class MigrationRulesEngine:
    """
    Core rule-based transformation engine for Odoo module migrations.
    
    This engine provides surgical precision for code transformations using
    AST parsing for Python and lxml for XML, replacing dangerous regex approaches.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rules = self._load_migration_rules()
        self.ast_upgrader = ProfessionalASTUpgrader()
        self.xml_upgrader = XMLSafeUpgrader()
        
        # Results tracking
        self.application_results = []
        self.failed_rules = []
        
    def _load_migration_rules(self) -> Dict[str, List[MigrationRule]]:
        """Load all migration rules organized by version transition"""
        return {
            "13.0->14.0": self._get_v13_to_v14_rules(),
            "14.0->15.0": self._get_v14_to_v15_rules(), 
            "15.0->16.0": self._get_v15_to_v16_rules(),
            "16.0->17.0": self._get_v16_to_v17_rules(),
            "17.0->18.0": self._get_v17_to_v18_rules()
        }
    
    def _get_v13_to_v14_rules(self) -> List[MigrationRule]:
        """Migration rules for Odoo 13.0 -> 14.0"""
        return [
            # Manifest rules
            MigrationRule(
                rule_id="v13_v14_manifest_version",
                source_version="13.0",
                target_version="14.0", 
                category="manifest",
                rule_type="version_update",
                pattern=r'"version":\s*"13\.0\.[^"]*"',
                replacement='"version": "********.0"',
                file_pattern="__manifest__.py",
                description="Update manifest version to 14.0"
            ),
            MigrationRule(
                rule_id="v13_v14_manifest_license",
                source_version="13.0",
                target_version="14.0",
                category="manifest", 
                rule_type="license_field",
                pattern=r'(\{[^}]*)"installable"',
                replacement=r'\1"license": "LGPL-3",\n    "installable"',
                file_pattern="__manifest__.py",
                description="Add license field to manifest"
            ),
            
            # Python rules
            MigrationRule(
                rule_id="v13_v14_config_import",
                source_version="13.0",
                target_version="14.0",
                category="python",
                rule_type="import_update", 
                pattern="from odoo.tools import config",
                replacement="from odoo.tools.config import config",
                file_pattern="*.py",
                description="Update config import path"
            ),
            MigrationRule(
                rule_id="v13_v14_api_multi_removal",
                source_version="13.0", 
                target_version="14.0",
                category="python",
                rule_type="decorator_removal",
                pattern="@api.multi",
                replacement="",
                file_pattern="*.py",
                description="Remove @api.multi decorators",
                complexity="medium"
            ),
            
            # XML rules
            MigrationRule(
                rule_id="v13_v14_attrs_modernize",
                source_version="13.0",
                target_version="14.0", 
                category="xml",
                rule_type="attribute_modernize",
                pattern=r'attrs="([^"]*)"',
                replacement="",  # Will be handled by XML processor
                file_pattern="*.xml",
                description="Modernize attrs to conditional visibility",
                complexity="complex"
            )
        ]
    
    def _get_v14_to_v15_rules(self) -> List[MigrationRule]:
        """Migration rules for Odoo 14.0 -> 15.0"""
        return [
            # Manifest rules
            MigrationRule(
                rule_id="v14_v15_manifest_version",
                source_version="14.0",
                target_version="15.0",
                category="manifest",
                rule_type="version_update", 
                pattern=r'"version":\s*"14\.0\.[^"]*"',
                replacement='"version": "********.0"',
                file_pattern="__manifest__.py",
                description="Update manifest version to 15.0"
            ),
            
            # Python rules  
            MigrationRule(
                rule_id="v14_v15_sudo_method",
                source_version="14.0",
                target_version="15.0",
                category="python",
                rule_type="method_update",
                pattern=r'\.sudo\(\)\.write\(',
                replacement='.with_user(SUPERUSER_ID).write(',
                file_pattern="*.py", 
                description="Update sudo method calls"
            ),
            MigrationRule(
                rule_id="v14_v15_selection_add",
                source_version="14.0",
                target_version="15.0",
                category="python",
                rule_type="field_parameter",
                pattern="selection_add=",
                replacement="selection_add=",  # Validation only
                file_pattern="*.py",
                description="Validate selection_add usage"
            )
        ]
    
    def _get_v15_to_v16_rules(self) -> List[MigrationRule]:
        """Migration rules for Odoo 15.0 -> 16.0"""
        return [
            # Manifest rules
            MigrationRule(
                rule_id="v15_v16_manifest_version",
                source_version="15.0", 
                target_version="16.0",
                category="manifest",
                rule_type="version_update",
                pattern=r"'version':\s*'15\.0\.[^']*'",
                replacement="'version': '********.0'",
                file_pattern="__manifest__.py",
                description="Update manifest version to 16.0"
            ),
            MigrationRule(
                rule_id="v15_v16_assets_structure",
                source_version="15.0",
                target_version="16.0",
                category="manifest",
                rule_type="assets_restructure",
                pattern="",  # Will be handled by manifest processor
                replacement="",
                file_pattern="__manifest__.py",
                description="Restructure assets to new format",
                complexity="complex"
            ),
            
            # Python rules
            MigrationRule(
                rule_id="v15_v16_translate_import",
                source_version="15.0",
                target_version="16.0",
                category="python", 
                rule_type="import_validation",
                pattern="from odoo.tools.translate import _",
                replacement="from odoo.tools.translate import _",  # Validation
                file_pattern="*.py",
                description="Validate translation import"
            )
        ]
    
    def _get_v16_to_v17_rules(self) -> List[MigrationRule]:
        """Migration rules for Odoo 16.0 -> 17.0"""
        return [
            # Manifest rules
            MigrationRule(
                rule_id="v16_v17_manifest_version",
                source_version="16.0",
                target_version="17.0",
                category="manifest",
                rule_type="version_update",
                pattern=r'"version":\s*"16\.0\.[^"]*"',
                replacement='"version": "********.0"',
                file_pattern="__manifest__.py",
                description="Update manifest version to 17.0"
            ),
            MigrationRule(
                rule_id="v16_v17_explicit_assets",
                source_version="16.0",
                target_version="17.0",
                category="manifest",
                rule_type="assets_explicit_paths",
                pattern="",  # Will be handled by manifest processor
                replacement="",
                file_pattern="__manifest__.py", 
                description="Make asset paths explicit",
                complexity="medium"
            ),
            
            # Python rules
            MigrationRule(
                rule_id="v16_v17_superuser_id",
                source_version="16.0",
                target_version="17.0",
                category="python",
                rule_type="constant_replacement",
                pattern="SUPERUSER_ID",
                replacement="env.su",
                file_pattern="*.py",
                description="Replace SUPERUSER_ID with env.su"
            ),
            MigrationRule(
                rule_id="v16_v17_field_help",
                source_version="16.0", 
                target_version="17.0",
                category="python",
                rule_type="field_enhancement",
                pattern="",  # Will be handled by Python processor
                replacement="",
                file_pattern="*.py",
                description="Add help text to fields",
                complexity="medium"
            ),
            
            # XML rules
            MigrationRule(
                rule_id="v16_v17_view_syntax", 
                source_version="16.0",
                target_version="17.0",
                category="xml",
                rule_type="view_modernization",
                pattern="",  # Will be handled by XML processor
                replacement="",
                file_pattern="*.xml",
                description="Modernize view syntax",
                complexity="medium"
            )
        ]
    
    def _get_v17_to_v18_rules(self) -> List[MigrationRule]:
        """Migration rules for Odoo 17.0 -> 18.0"""
        return [
            # Manifest rules
            MigrationRule(
                rule_id="v17_v18_manifest_version",
                source_version="17.0",
                target_version="18.0",
                category="manifest", 
                rule_type="version_update",
                pattern=r'"version":\s*"17\.0\.[^"]*"',
                replacement='"version": "********.0"',
                file_pattern="__manifest__.py",
                description="Update manifest version to 18.0"
            ),
            
            # Python rules
            MigrationRule(
                rule_id="v17_v18_request_env",
                source_version="17.0",
                target_version="18.0",
                category="python",
                rule_type="request_validation",
                pattern="request.env",
                replacement="request.env",  # Validation only
                file_pattern="*.py",
                description="Validate request.env usage"
            ),
            MigrationRule(
                rule_id="v17_v18_sql_constraints",
                source_version="17.0",
                target_version="18.0",
                category="python", 
                rule_type="constraint_validation",
                pattern="_sql_constraints",
                replacement="_sql_constraints",  # Validation
                file_pattern="*.py",
                description="Validate SQL constraints"
            )
        ]
    
    def apply_rules_to_module(self, module_path: str, source_version: str, target_version: str) -> Dict[str, Any]:
        """
        Apply migration rules to an entire module.
        
        Args:
            module_path: Path to the module directory
            source_version: Source Odoo version
            target_version: Target Odoo version
            
        Returns:
            Comprehensive application results
        """
        self.logger.info(f"Applying migration rules: {source_version} -> {target_version}")
        
        # Reset results
        self.application_results = []
        self.failed_rules = []
        
        # Get migration path (may involve multiple steps)
        migration_steps = self._plan_migration_path(source_version, target_version)
        
        results = {
            'migration_path': migration_steps,
            'total_rules_applied': 0,
            'files_modified': 0,
            'rules_by_category': {'manifest': 0, 'python': 0, 'xml': 0, 'database': 0},
            'application_results': [],
            'failed_rules': [],
            'warnings': [],
            'summary': {}
        }
        
        # Apply rules for each migration step
        for step_source, step_target in migration_steps:
            step_results = self._apply_single_migration_step(module_path, step_source, step_target)
            
            # Aggregate results
            results['total_rules_applied'] += step_results['rules_applied']
            results['files_modified'] += step_results['files_modified']
            for category in results['rules_by_category']:
                results['rules_by_category'][category] += step_results['rules_by_category'].get(category, 0)
            
            results['application_results'].extend(step_results['application_results'])
            results['failed_rules'].extend(step_results['failed_rules'])
            results['warnings'].extend(step_results['warnings'])
        
        # Generate summary
        results['summary'] = self._generate_application_summary(results)
        
        self.logger.info(f"Migration rules applied: {results['total_rules_applied']} rules, {results['files_modified']} files modified")
        return results
    
    def _plan_migration_path(self, source_version: str, target_version: str) -> List[Tuple[str, str]]:
        """Plan incremental migration path between versions"""
        version_sequence = ["13.0", "14.0", "15.0", "16.0", "17.0", "18.0"]
        
        try:
            source_idx = version_sequence.index(source_version)
            target_idx = version_sequence.index(target_version)
        except ValueError as e:
            raise ValueError(f"Unsupported version: {e}")
        
        if source_idx > target_idx:
            raise ValueError("Downgrade migration not supported")
        
        migration_path = []
        for i in range(source_idx, target_idx):
            migration_path.append((version_sequence[i], version_sequence[i + 1]))
        
        return migration_path
    
    def _apply_single_migration_step(self, module_path: str, source_ver: str, target_ver: str) -> Dict[str, Any]:
        """Apply rules for a single migration step"""
        step_key = f"{source_ver}->{target_ver}"
        rules = self.rules.get(step_key, [])
        
        step_results = {
            'rules_applied': 0,
            'files_modified': 0,
            'rules_by_category': {'manifest': 0, 'python': 0, 'xml': 0, 'database': 0},
            'application_results': [],
            'failed_rules': [],
            'warnings': []
        }
        
        # Group rules by category for efficient processing
        rules_by_category = {}
        for rule in rules:
            if rule.category not in rules_by_category:
                rules_by_category[rule.category] = []
            rules_by_category[rule.category].append(rule)
        
        # Apply rules by category
        for category, category_rules in rules_by_category.items():
            if category == 'manifest':
                results = self._apply_manifest_rules(module_path, category_rules)
            elif category == 'python':
                results = self._apply_python_rules(module_path, category_rules)
            elif category == 'xml':
                results = self._apply_xml_rules(module_path, category_rules)
            elif category == 'database':
                results = self._apply_database_rules(module_path, category_rules)
            else:
                continue
            
            # Aggregate category results
            step_results['application_results'].extend(results['applications'])
            step_results['failed_rules'].extend(results['failed'])
            step_results['warnings'].extend(results['warnings'])
            step_results['rules_applied'] += results['rules_applied']
            step_results['files_modified'] += results['files_modified']
            step_results['rules_by_category'][category] = results['rules_applied']
        
        return step_results
    
    def _apply_manifest_rules(self, module_path: str, rules: List[MigrationRule]) -> Dict[str, Any]:
        """Apply manifest-specific rules"""
        manifest_path = os.path.join(module_path, "__manifest__.py")
        if not os.path.exists(manifest_path):
            return {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'files_modified': 0}
        
        results = {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'files_modified': 0}
        
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            modified_content = original_content
            content_changed = False
            
            for rule in rules:
                try:
                    if rule.rule_type == "version_update":
                        new_content = re.sub(rule.pattern, rule.replacement, modified_content)
                        if new_content != modified_content:
                            modified_content = new_content
                            content_changed = True
                            results['rules_applied'] += 1
                            
                            results['applications'].append(RuleApplicationResult(
                                rule_id=rule.rule_id,
                                file_path=manifest_path,
                                applied=True,
                                changes_made=1,
                                original_content_hash=hashlib.md5(original_content.encode()).hexdigest(),
                                new_content_hash=hashlib.md5(modified_content.encode()).hexdigest()
                            ))
                    
                    elif rule.rule_type == "license_field":
                        if '"license"' not in modified_content:
                            new_content = re.sub(rule.pattern, rule.replacement, modified_content)
                            if new_content != modified_content:
                                modified_content = new_content
                                content_changed = True
                                results['rules_applied'] += 1
                                
                                results['applications'].append(RuleApplicationResult(
                                    rule_id=rule.rule_id,
                                    file_path=manifest_path,
                                    applied=True,
                                    changes_made=1,
                                    original_content_hash=hashlib.md5(original_content.encode()).hexdigest(),
                                    new_content_hash=hashlib.md5(modified_content.encode()).hexdigest()
                                ))
                    
                    elif rule.rule_type in ["assets_restructure", "assets_explicit_paths"]:
                        # Complex manifest transformations
                        asset_result = self._transform_manifest_assets(modified_content, rule)
                        if asset_result['modified']:
                            modified_content = asset_result['content']
                            content_changed = True
                            results['rules_applied'] += 1
                            
                            results['applications'].append(RuleApplicationResult(
                                rule_id=rule.rule_id,
                                file_path=manifest_path,
                                applied=True,
                                changes_made=asset_result['changes_made'],
                                original_content_hash=hashlib.md5(original_content.encode()).hexdigest(),
                                new_content_hash=hashlib.md5(modified_content.encode()).hexdigest()
                            ))
                
                except Exception as e:
                    results['failed'].append({
                        'rule_id': rule.rule_id,
                        'error': str(e),
                        'file_path': manifest_path
                    })
            
            # Write modified content if changes were made
            if content_changed:
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                results['files_modified'] = 1
        
        except Exception as e:
            results['warnings'].append(f"Failed to process manifest file: {str(e)}")
        
        return results
    
    def _apply_python_rules(self, module_path: str, rules: List[MigrationRule]) -> Dict[str, Any]:
        """Apply Python-specific rules using AST transformations"""
        results = {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'files_modified': 0}
        
        # Find all Python files
        python_files = []
        for root, dirs, files in os.walk(module_path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        for py_file in python_files:
            try:
                file_results = self._apply_python_rules_to_file(py_file, rules)
                results['applications'].extend(file_results['applications'])
                results['failed'].extend(file_results['failed'])
                results['warnings'].extend(file_results['warnings'])
                results['rules_applied'] += file_results['rules_applied']
                if file_results['file_modified']:
                    results['files_modified'] += 1
            
            except Exception as e:
                results['warnings'].append(f"Failed to process Python file {py_file}: {str(e)}")
        
        return results
    
    def _apply_python_rules_to_file(self, file_path: str, rules: List[MigrationRule]) -> Dict[str, Any]:
        """Apply Python rules to a single file"""
        results = {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'file_modified': False}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            modified_content = original_content
            content_changed = False
            
            for rule in rules:
                try:
                    if rule.rule_type == "import_update":
                        new_content = modified_content.replace(rule.pattern, rule.replacement)
                        if new_content != modified_content:
                            modified_content = new_content
                            content_changed = True
                            results['rules_applied'] += 1
                            
                            results['applications'].append(RuleApplicationResult(
                                rule_id=rule.rule_id,
                                file_path=file_path,
                                applied=True,
                                changes_made=1,
                                original_content_hash=hashlib.md5(original_content.encode()).hexdigest(),
                                new_content_hash=hashlib.md5(modified_content.encode()).hexdigest()
                            ))
                    
                    elif rule.rule_type == "decorator_removal":
                        # Use AST upgrader for safe decorator removal
                        if "@api.multi" in modified_content:
                            ast_result = self.ast_upgrader.upgrade_python_file(file_path)
                            if ast_result['changes_made']:
                                # Re-read the file after AST transformation
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    modified_content = f.read()
                                content_changed = True
                                results['rules_applied'] += 1
                                
                                results['applications'].append(RuleApplicationResult(
                                    rule_id=rule.rule_id,
                                    file_path=file_path,
                                    applied=True,
                                    changes_made=len(ast_result['changes_summary']),
                                    original_content_hash=hashlib.md5(original_content.encode()).hexdigest(),
                                    new_content_hash=hashlib.md5(modified_content.encode()).hexdigest()
                                ))
                    
                    elif rule.rule_type in ["method_update", "constant_replacement"]:
                        new_content = re.sub(rule.pattern, rule.replacement, modified_content)
                        if new_content != modified_content:
                            modified_content = new_content
                            content_changed = True
                            results['rules_applied'] += 1
                            
                            results['applications'].append(RuleApplicationResult(
                                rule_id=rule.rule_id,
                                file_path=file_path,
                                applied=True,
                                changes_made=1,
                                original_content_hash=hashlib.md5(original_content.encode()).hexdigest(),
                                new_content_hash=hashlib.md5(modified_content.encode()).hexdigest()
                            ))
                    
                    elif rule.rule_type in ["import_validation", "field_parameter", "request_validation", "constraint_validation"]:
                        # Validation rules - check but don't modify
                        if rule.pattern in modified_content:
                            results['applications'].append(RuleApplicationResult(
                                rule_id=rule.rule_id,
                                file_path=file_path,
                                applied=True,
                                changes_made=0,
                                warnings=["Validation passed - pattern found but no changes needed"]
                            ))
                
                except Exception as e:
                    results['failed'].append({
                        'rule_id': rule.rule_id,
                        'error': str(e),
                        'file_path': file_path
                    })
            
            # Write modified content if changes were made
            if content_changed and modified_content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                results['file_modified'] = True
        
        except Exception as e:
            results['warnings'].append(f"Failed to process Python file {file_path}: {str(e)}")
        
        return results
    
    def _apply_xml_rules(self, module_path: str, rules: List[MigrationRule]) -> Dict[str, Any]:
        """Apply XML-specific rules using lxml parsing"""
        results = {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'files_modified': 0}
        
        # Find all XML files
        xml_files = []
        for root, dirs, files in os.walk(module_path):
            for file in files:
                if file.endswith('.xml'):
                    xml_files.append(os.path.join(root, file))
        
        for xml_file in xml_files:
            try:
                file_results = self._apply_xml_rules_to_file(xml_file, rules)
                results['applications'].extend(file_results['applications'])
                results['failed'].extend(file_results['failed'])
                results['warnings'].extend(file_results['warnings'])
                results['rules_applied'] += file_results['rules_applied']
                if file_results['file_modified']:
                    results['files_modified'] += 1
            
            except Exception as e:
                results['warnings'].append(f"Failed to process XML file {xml_file}: {str(e)}")
        
        return results
    
    def _apply_xml_rules_to_file(self, file_path: str, rules: List[MigrationRule]) -> Dict[str, Any]:
        """Apply XML rules to a single file"""
        results = {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'file_modified': False}
        
        try:
            # Use XML upgrader for safe transformations
            upgrade_result = self.xml_upgrader.upgrade_xml_file(file_path)
            
            if upgrade_result['changes_made']:
                results['file_modified'] = True
                results['rules_applied'] = len(upgrade_result['changes_summary'])
                
                for i, change in enumerate(upgrade_result['changes_summary']):
                    results['applications'].append(RuleApplicationResult(
                        rule_id=f"xml_upgrade_{i}",
                        file_path=file_path,
                        applied=True,
                        changes_made=1,
                        warnings=upgrade_result.get('warnings', [])
                    ))
        
        except Exception as e:
            results['warnings'].append(f"Failed to process XML file {file_path}: {str(e)}")
        
        return results
    
    def _apply_database_rules(self, module_path: str, rules: List[MigrationRule]) -> Dict[str, Any]:
        """Apply database-specific rules (tracking only for now)"""
        results = {'applications': [], 'failed': [], 'warnings': [], 'rules_applied': 0, 'files_modified': 0}
        
        # For now, database rules are tracked but not applied
        # This will be expanded in Week 3 with Docker integration
        
        for rule in rules:
            results['applications'].append(RuleApplicationResult(
                rule_id=rule.rule_id,
                file_path="database",
                applied=False,
                warnings=["Database rule tracking - will be applied during Docker migration phase"]
            ))
        
        return results
    
    def _transform_manifest_assets(self, content: str, rule: MigrationRule) -> Dict[str, Any]:
        """Transform manifest assets structure"""
        result = {'modified': False, 'content': content, 'changes_made': 0}
        
        if rule.rule_type == "assets_restructure":
            # Convert old js/css fields to assets structure
            if '"js":' in content or '"css":' in content:
                # This is a complex transformation that would need proper manifest parsing
                # For now, add warning that manual intervention may be needed
                result['modified'] = False
                result['content'] = content
                result['changes_made'] = 0
        
        elif rule.rule_type == "assets_explicit_paths":
            # Make asset paths more explicit
            if '"assets":' in content:
                # Update relative paths to be more explicit
                result['modified'] = False
                result['content'] = content
                result['changes_made'] = 0
        
        return result
    
    def _generate_application_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of rule applications"""
        return {
            'total_rules': results['total_rules_applied'],
            'successful_applications': len([r for r in results['application_results'] if r.applied]),
            'failed_applications': len(results['failed_rules']),
            'files_modified': results['files_modified'],
            'categories_processed': [cat for cat, count in results['rules_by_category'].items() if count > 0],
            'complexity_breakdown': self._analyze_complexity_breakdown(results['application_results']),
            'manual_interventions_needed': len([r for r in results['application_results'] if r.warnings])
        }
    
    def _analyze_complexity_breakdown(self, applications: List[RuleApplicationResult]) -> Dict[str, int]:
        """Analyze complexity of applied rules"""
        complexity_counts = {'simple': 0, 'medium': 0, 'complex': 0}
        
        # This would need to be enhanced to track rule complexity
        # For now, provide basic categorization
        for app in applications:
            if app.changes_made == 0:
                complexity_counts['simple'] += 1
            elif app.changes_made <= 5:
                complexity_counts['medium'] += 1
            else:
                complexity_counts['complex'] += 1
        
        return complexity_counts
    
    def get_rules_for_version_pair(self, source_version: str, target_version: str) -> List[MigrationRule]:
        """Get all rules for a specific version transition"""
        step_key = f"{source_version}->{target_version}"
        return self.rules.get(step_key, [])
    
    def validate_rules(self) -> Dict[str, Any]:
        """Validate all migration rules for correctness"""
        validation_results = {
            'valid_rules': 0,
            'invalid_rules': 0,
            'warnings': [],
            'errors': []
        }
        
        for version_pair, rules in self.rules.items():
            for rule in rules:
                try:
                    # Basic validation
                    if not rule.rule_id or not rule.pattern:
                        validation_results['invalid_rules'] += 1
                        validation_results['errors'].append(f"Invalid rule {rule.rule_id}: missing required fields")
                        continue
                    
                    # Pattern validation for regex rules
                    if rule.category in ['python', 'xml'] and rule.rule_type not in ['validation']:
                        try:
                            re.compile(rule.pattern)
                        except re.error as e:
                            validation_results['invalid_rules'] += 1
                            validation_results['errors'].append(f"Invalid regex in rule {rule.rule_id}: {str(e)}")
                            continue
                    
                    validation_results['valid_rules'] += 1
                
                except Exception as e:
                    validation_results['invalid_rules'] += 1
                    validation_results['errors'].append(f"Validation error for rule {rule.rule_id}: {str(e)}")
        
        return validation_results

def main():
    """Test the migration rules engine"""
    engine = MigrationRulesEngine()
    
    # Validate rules
    validation = engine.validate_rules()
    print("Rule Validation Results:")
    print(f"Valid rules: {validation['valid_rules']}")
    print(f"Invalid rules: {validation['invalid_rules']}")
    
    if validation['errors']:
        print("Errors:")
        for error in validation['errors']:
            print(f"  - {error}")
    
    # Test with a sample module path
    test_module = "sample_modules/test_module"
    if os.path.exists(test_module):
        results = engine.apply_rules_to_module(test_module, "15.0", "17.0")
        print(f"\nMigration Results:")
        print(f"Rules applied: {results['total_rules_applied']}")
        print(f"Files modified: {results['files_modified']}")
        print(f"Summary: {results['summary']}")

if __name__ == "__main__":
    main()