#!/usr/bin/env python3
"""
GitHub Sync Script for Private Repository

This script handles pushing changes to the private OdooUpgradeEngine repository
using the stored GitHub token for authentication.
"""

import os
import subprocess
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GitHubSync:
    """
    GitHub synchronization class for managing repository operations.
    """
    
    def __init__(self):
        self.github_token = os.environ.get('GITHUB_TOKEN', '')
        self.repo_url = "https://github.com/yerenwgventures/OdooUpgradeEngine.git"
        
    def is_authenticated(self):
        """Check if GitHub token is available."""
        return bool(self.github_token)
    
    def sync_to_github(self, commit_message=None):
        """Sync local changes to GitHub repository."""
        if not self.is_authenticated():
            logger.error("No GitHub token available")
            return False
            
        return commit_and_push_changes(commit_message)
    
    def pull_from_github(self):
        """Pull latest changes from GitHub repository."""
        try:
            result = subprocess.run("git pull origin main", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("Successfully pulled from GitHub")
                return True
            else:
                logger.error(f"Git pull failed: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Pull operation failed: {str(e)}")
            return False

    def sync_upgraded_module(self, module_name, original_version, target_version, module_path, target_branch="upgraded-modules"):
        """Sync an upgraded module back to GitHub in organized folders."""
        if not self.is_authenticated():
            logger.error("No GitHub token available for sync")
            return False

        try:
            import shutil

            # Create target directory structure
            target_dir = f"upgraded_modules/{target_version}/{module_name}"
            os.makedirs(target_dir, exist_ok=True)

            # Copy upgraded module to target directory
            if os.path.exists(module_path):
                if os.path.isdir(module_path):
                    # Copy directory
                    if os.path.exists(target_dir):
                        shutil.rmtree(target_dir)
                    shutil.copytree(module_path, target_dir)
                else:
                    # Copy file
                    shutil.copy2(module_path, target_dir)

                # Commit and push changes
                commit_message = f"Sync upgraded module: {module_name} ({original_version} → {target_version})"
                return commit_and_push_changes(commit_message)
            else:
                logger.warning(f"Module path not found: {module_path}")
                return False

        except Exception as e:
            logger.error(f"Error syncing upgraded module {module_name}: {str(e)}")
            return False

def setup_git_config():
    """Set up git configuration for pushing to private repository."""
    try:
        # Get GitHub token from environment
        github_token = os.environ.get('GITHUB_TOKEN', '')
        if not github_token:
            logger.error("No GitHub token found in environment")
            return False
        
        logger.info("GitHub token found - ready for authenticated operations")
        return True
        
    except Exception as e:
        logger.error(f"Git configuration failed: {str(e)}")
        return False

def commit_and_push_changes(commit_message=None):
    """Commit and push all changes to the private repository."""
    try:
        if not commit_message:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            commit_message = f"Visual diff viewer integration completed - {timestamp}"
        
        # Check if there are any changes to commit
        result = subprocess.run("git status --porcelain", shell=True, capture_output=True, text=True)
        if not result.stdout.strip():
            logger.info("No changes to commit")
            return True
        
        # Get GitHub token and set up environment
        github_token = os.environ.get('GITHUB_TOKEN', '')
        if not github_token:
            logger.error("No GitHub token available for push operations")
            return False
        
        # Set up git environment with token
        env = os.environ.copy()
        env['GIT_ASKPASS'] = 'echo'
        env['GIT_USERNAME'] = github_token
        env['GIT_PASSWORD'] = ''
        
        # Add all changes
        subprocess.run("git add .", shell=True, check=True, env=env)
        logger.info("Added all changes to git")
        
        # Commit changes
        subprocess.run(f'git commit -m "{commit_message}"', shell=True, check=True, env=env)
        logger.info(f"Committed changes: {commit_message}")
        
        # Use token-based URL for push
        repo_url = "https://github.com/yerenwgventures/OdooUpgradeEngine.git"
        auth_url = f"https://{github_token}@github.com/yerenwgventures/OdooUpgradeEngine.git"
        
        # Push using the authenticated URL directly
        subprocess.run(f"git push {auth_url} main", shell=True, check=True, env=env)
        logger.info("Successfully pushed changes to GitHub")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Git push failed: {str(e)}")
        return False

def sync_to_private_repo():
    """Main function to sync changes to private repository."""
    logger.info("Starting GitHub sync to private repository...")
    
    # Setup git configuration
    if not setup_git_config():
        return False
    
    # Commit and push changes
    commit_message = """Visual diff viewer integration: Complete transparency for upgrade code changes

- Added visual diff generation after successful upgrades
- Created side-by-side HTML diff reports with syntax highlighting
- Added web route to serve visual diff reports (/visual_diff/)
- Enhanced module details page showing diff statistics
- Complete transparency for all code changes during upgrades
- Security validation results integrated into interface
- Production-ready workflow with visual validation"""
    
    return commit_and_push_changes(commit_message)

if __name__ == "__main__":
    success = sync_to_private_repo()
    if success:
        print("✓ Successfully synced changes to private repository")
    else:
        print("✗ Failed to sync changes")