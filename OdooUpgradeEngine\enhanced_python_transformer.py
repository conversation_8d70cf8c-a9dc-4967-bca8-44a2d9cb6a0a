"""
Enhanced Python Transformer - Advanced AST-based Code Migrations

This module extends the basic AST upgrader with sophisticated transformations
for complex business logic migrations across Odoo versions. It handles
semantic code changes that go beyond simple pattern replacement.

Features:
- Business logic pattern recognition and transformation
- API evolution handling (method signature changes, parameter updates)
- Cross-version compatibility layer generation
- Semantic analysis for context-aware transformations
- Integration with migration rules engine
"""

import ast
import logging
import os
import re
from typing import Dict, List, Any, Optional, Set, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

# Import our existing components
from ast_based_upgrader import ProfessionalASTUpgrader
from migration_rules_engine import MigrationRule, RuleApplicationResult

@dataclass
class PythonTransformation:
    """Represents a complex Python transformation"""
    transformation_id: str
    source_version: str
    target_version: str
    pattern_type: str  # 'api_change', 'business_logic', 'import_refactor', 'class_inheritance'
    source_pattern: str
    target_pattern: str
    requires_context: bool = False
    complexity: str = "medium"  # simple, medium, complex
    validation_rules: List[str] = None
    migration_notes: str = ""
    
    def __post_init__(self):
        if self.validation_rules is None:
            self.validation_rules = []

class EnhancedPythonTransformer:
    """
    Advanced Python code transformer for complex Odoo migrations.
    
    This transformer handles business logic evolution, API changes,
    and semantic transformations that require deep code understanding.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ast_upgrader = ProfessionalASTUpgrader()
        
        # Transformation registry organized by version
        self.transformations = self._load_transformations()
        
        # Results tracking
        self.transformation_results = []
        self.warnings = []
        self.errors = []
        
    def _load_transformations(self) -> Dict[str, List[PythonTransformation]]:
        """Load all Python transformations organized by version transition"""
        return {
            "13.0->14.0": self._get_v13_to_v14_transformations(),
            "14.0->15.0": self._get_v14_to_v15_transformations(),
            "15.0->16.0": self._get_v15_to_v16_transformations(),
            "16.0->17.0": self._get_v16_to_v17_transformations(),
            "17.0->18.0": self._get_v17_to_v18_transformations()
        }
    
    def _get_v13_to_v14_transformations(self) -> List[PythonTransformation]:
        """Advanced transformations for Odoo 13.0 -> 14.0"""
        return [
            PythonTransformation(
                transformation_id="v13_v14_api_multi_removal",
                source_version="13.0",
                target_version="14.0",
                pattern_type="api_change",
                source_pattern="@api.multi",
                target_pattern="# @api.multi removed",
                requires_context=True,
                complexity="medium",
                migration_notes="Remove @api.multi decorators and ensure methods handle recordsets properly"
            ),
            PythonTransformation(
                transformation_id="v13_v14_config_import_update",
                source_version="13.0",
                target_version="14.0",
                pattern_type="import_refactor",
                source_pattern="from odoo.tools import config",
                target_pattern="from odoo.tools.config import config",
                complexity="simple",
                migration_notes="Update config import path"
            ),
            PythonTransformation(
                transformation_id="v13_v14_mail_thread_updates",
                source_version="13.0",
                target_version="14.0",
                pattern_type="business_logic",
                source_pattern="mail.thread",
                target_pattern="mail.thread",
                requires_context=True,
                complexity="complex",
                validation_rules=["check_message_post_signature", "validate_tracking_fields"],
                migration_notes="Update mail.thread methods for new message tracking system"
            )
        ]
    
    def _get_v14_to_v15_transformations(self) -> List[PythonTransformation]:
        """Advanced transformations for Odoo 14.0 -> 15.0"""
        return [
            PythonTransformation(
                transformation_id="v14_v15_sudo_method_evolution",
                source_version="14.0",
                target_version="15.0",
                pattern_type="api_change",
                source_pattern=".sudo()",
                target_pattern=".with_user(SUPERUSER_ID)",
                requires_context=True,
                complexity="medium",
                validation_rules=["check_sudo_context", "validate_user_permissions"],
                migration_notes="Replace sudo() with more explicit user context management"
            ),
            PythonTransformation(
                transformation_id="v14_v15_field_selection_add",
                source_version="14.0",
                target_version="15.0",
                pattern_type="business_logic",
                source_pattern="selection_add=",
                target_pattern="selection_add=",
                requires_context=True,
                complexity="complex",
                validation_rules=["validate_selection_inheritance"],
                migration_notes="Ensure selection_add works correctly with field inheritance"
            )
        ]
    
    def _get_v15_to_v16_transformations(self) -> List[PythonTransformation]:
        """Advanced transformations for Odoo 15.0 -> 16.0"""
        return [
            PythonTransformation(
                transformation_id="v15_v16_assets_bundle_migration",
                source_version="15.0",
                target_version="16.0",
                pattern_type="business_logic",
                source_pattern="addons/web/static/",
                target_pattern="@web/",
                requires_context=True,
                complexity="complex",
                validation_rules=["validate_asset_paths"],
                migration_notes="Migrate asset bundle references to new format"
            ),
            PythonTransformation(
                transformation_id="v15_v16_translation_import_validation",
                source_version="15.0",
                target_version="16.0",
                pattern_type="import_refactor",
                source_pattern="from odoo.tools.translate import _",
                target_pattern="from odoo.tools.translate import _",
                complexity="simple",
                validation_rules=["check_translation_context"],
                migration_notes="Validate translation import usage"
            )
        ]
    
    def _get_v16_to_v17_transformations(self) -> List[PythonTransformation]:
        """Advanced transformations for Odoo 16.0 -> 17.0"""
        return [
            PythonTransformation(
                transformation_id="v16_v17_superuser_id_replacement",
                source_version="16.0",
                target_version="17.0",
                pattern_type="api_change",
                source_pattern="SUPERUSER_ID",
                target_pattern="env.su",
                requires_context=True,
                complexity="medium",
                validation_rules=["check_superuser_context"],
                migration_notes="Replace SUPERUSER_ID with env.su for better context management"
            ),
            PythonTransformation(
                transformation_id="v16_v17_field_help_enhancement",
                source_version="16.0",
                target_version="17.0",
                pattern_type="business_logic",
                source_pattern="fields\\.(Char|Text|Integer|Float|Boolean|Selection)",
                target_pattern="\\1",
                requires_context=True,
                complexity="complex",
                validation_rules=["ensure_field_help_exists"],
                migration_notes="Add help text to fields that don't have it"
            ),
            PythonTransformation(
                transformation_id="v16_v17_compute_method_updates",
                source_version="16.0",
                target_version="17.0",
                pattern_type="business_logic",
                source_pattern="@api.depends",
                target_pattern="@api.depends",
                requires_context=True,
                complexity="complex",
                validation_rules=["validate_compute_dependencies", "check_inverse_methods"],
                migration_notes="Update compute methods for improved dependency tracking"
            )
        ]
    
    def _get_v17_to_v18_transformations(self) -> List[PythonTransformation]:
        """Advanced transformations for Odoo 17.0 -> 18.0"""
        return [
            PythonTransformation(
                transformation_id="v17_v18_request_env_validation",
                source_version="17.0",
                target_version="18.0",
                pattern_type="api_change",
                source_pattern="request.env",
                target_pattern="request.env",
                requires_context=True,
                complexity="medium",
                validation_rules=["check_request_context_safety"],
                migration_notes="Validate request.env usage for security and context safety"
            ),
            PythonTransformation(
                transformation_id="v17_v18_sql_constraints_validation",
                source_version="17.0",
                target_version="18.0",
                pattern_type="business_logic",
                source_pattern="_sql_constraints",
                target_pattern="_sql_constraints",
                requires_context=True,
                complexity="complex",
                validation_rules=["validate_sql_constraint_syntax", "check_constraint_security"],
                migration_notes="Validate and update SQL constraints for v18 compatibility"
            ),
            PythonTransformation(
                transformation_id="v17_v18_orm_performance_optimization",
                source_version="17.0",
                target_version="18.0",
                pattern_type="business_logic",
                source_pattern="\\.(search|browse|create|write)\\(",
                target_pattern="\\1(",
                requires_context=True,
                complexity="complex",
                validation_rules=["check_orm_batch_operations", "validate_prefetch_usage"],
                migration_notes="Optimize ORM operations for v18 performance improvements"
            )
        ]
    
    def apply_transformations_to_module(self, module_path: str, source_version: str, target_version: str) -> Dict[str, Any]:
        """
        Apply advanced Python transformations to an entire module.
        
        Args:
            module_path: Path to the module directory
            source_version: Source Odoo version
            target_version: Target Odoo version
            
        Returns:
            Comprehensive transformation results
        """
        self.logger.info(f"Applying Python transformations: {source_version} -> {target_version}")
        
        # Reset results
        self.transformation_results = []
        self.warnings = []
        self.errors = []
        
        # Get transformation path (may involve multiple steps)
        transformation_steps = self._plan_transformation_path(source_version, target_version)
        
        results = {
            'transformation_path': transformation_steps,
            'total_transformations_applied': 0,
            'files_modified': 0,
            'transformations_by_type': {
                'api_change': 0, 
                'business_logic': 0, 
                'import_refactor': 0, 
                'class_inheritance': 0
            },
            'transformation_results': [],
            'validation_warnings': [],
            'errors': [],
            'summary': {}
        }
        
        # Apply transformations for each step
        for step_source, step_target in transformation_steps:
            step_results = self._apply_single_transformation_step(module_path, step_source, step_target)
            
            # Aggregate results
            results['total_transformations_applied'] += step_results['transformations_applied']
            results['files_modified'] += step_results['files_modified']
            for t_type in results['transformations_by_type']:
                results['transformations_by_type'][t_type] += step_results['transformations_by_type'].get(t_type, 0)
            
            results['transformation_results'].extend(step_results['transformation_results'])
            results['validation_warnings'].extend(step_results['validation_warnings'])
            results['errors'].extend(step_results['errors'])
        
        # Generate summary
        results['summary'] = self._generate_transformation_summary(results)
        
        self.logger.info(f"Python transformations applied: {results['total_transformations_applied']} transformations")
        return results
    
    def _plan_transformation_path(self, source_version: str, target_version: str) -> List[Tuple[str, str]]:
        """Plan incremental transformation path between versions"""
        version_sequence = ["13.0", "14.0", "15.0", "16.0", "17.0", "18.0"]
        
        try:
            source_idx = version_sequence.index(source_version)
            target_idx = version_sequence.index(target_version)
        except ValueError as e:
            raise ValueError(f"Unsupported version: {e}")
        
        if source_idx > target_idx:
            raise ValueError("Downgrade transformation not supported")
        
        transformation_path = []
        for i in range(source_idx, target_idx):
            transformation_path.append((version_sequence[i], version_sequence[i + 1]))
        
        return transformation_path
    
    def _apply_single_transformation_step(self, module_path: str, source_ver: str, target_ver: str) -> Dict[str, Any]:
        """Apply transformations for a single step"""
        step_key = f"{source_ver}->{target_ver}"
        transformations = self.transformations.get(step_key, [])
        
        step_results = {
            'transformations_applied': 0,
            'files_modified': 0,
            'transformations_by_type': {
                'api_change': 0, 
                'business_logic': 0, 
                'import_refactor': 0, 
                'class_inheritance': 0
            },
            'transformation_results': [],
            'validation_warnings': [],
            'errors': []
        }
        
        # Find all Python files
        python_files = []
        for root, dirs, files in os.walk(module_path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        for py_file in python_files:
            try:
                file_results = self._apply_transformations_to_file(py_file, transformations)
                step_results['transformation_results'].extend(file_results['transformation_results'])
                step_results['validation_warnings'].extend(file_results['validation_warnings'])
                step_results['errors'].extend(file_results['errors'])
                step_results['transformations_applied'] += file_results['transformations_applied']
                if file_results['file_modified']:
                    step_results['files_modified'] += 1
                
                # Count by type
                for result in file_results['transformation_results']:
                    if hasattr(result, 'transformation_type'):
                        step_results['transformations_by_type'][result.transformation_type] += 1
            
            except Exception as e:
                step_results['errors'].append(f"Failed to process file {py_file}: {str(e)}")
        
        return step_results
    
    def _apply_transformations_to_file(self, file_path: str, transformations: List[PythonTransformation]) -> Dict[str, Any]:
        """Apply transformations to a single Python file"""
        results = {
            'transformation_results': [],
            'validation_warnings': [],
            'errors': [],
            'transformations_applied': 0,
            'file_modified': False
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            modified_content = original_content
            content_changed = False
            
            # Parse AST for context-aware transformations
            try:
                tree = ast.parse(original_content)
                ast_context = self._analyze_ast_context(tree)
            except SyntaxError as e:
                results['errors'].append(f"Syntax error in {file_path}: {str(e)}")
                return results
            
            for transformation in transformations:
                try:
                    # Apply transformation based on type
                    if transformation.pattern_type == "import_refactor":
                        new_content, changes = self._apply_import_transformation(
                            modified_content, transformation, ast_context
                        )
                    elif transformation.pattern_type == "api_change":
                        new_content, changes = self._apply_api_transformation(
                            modified_content, transformation, ast_context
                        )
                    elif transformation.pattern_type == "business_logic":
                        new_content, changes = self._apply_business_logic_transformation(
                            modified_content, transformation, ast_context
                        )
                    elif transformation.pattern_type == "class_inheritance":
                        new_content, changes = self._apply_inheritance_transformation(
                            modified_content, transformation, ast_context
                        )
                    else:
                        continue
                    
                    if changes > 0:
                        modified_content = new_content
                        content_changed = True
                        results['transformations_applied'] += 1
                        
                        # Create transformation result
                        result = RuleApplicationResult(
                            rule_id=transformation.transformation_id,
                            file_path=file_path,
                            applied=True,
                            changes_made=changes
                        )
                        result.transformation_type = transformation.pattern_type
                        results['transformation_results'].append(result)
                        
                        # Run validation rules
                        if transformation.validation_rules:
                            validation_warnings = self._run_validation_rules(
                                modified_content, transformation.validation_rules, ast_context
                            )
                            results['validation_warnings'].extend(validation_warnings)
                
                except Exception as e:
                    results['errors'].append(f"Transformation {transformation.transformation_id} failed: {str(e)}")
            
            # Write modified content if changes were made
            if content_changed:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                results['file_modified'] = True
        
        except Exception as e:
            results['errors'].append(f"Failed to process file {file_path}: {str(e)}")
        
        return results
    
    def _analyze_ast_context(self, tree: ast.AST) -> Dict[str, Any]:
        """Analyze AST to provide context for transformations"""
        context = {
            'imports': [],
            'classes': [],
            'functions': [],
            'decorators': [],
            'field_definitions': [],
            'api_methods': []
        }
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    context['imports'].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    context['imports'].append(f"{module}.{alias.name}")
            elif isinstance(node, ast.ClassDef):
                context['classes'].append(node.name)
            elif isinstance(node, ast.FunctionDef):
                context['functions'].append(node.name)
                # Check for decorators
                for decorator in node.decorator_list:
                    if isinstance(decorator, ast.Name):
                        context['decorators'].append(decorator.id)
                    elif isinstance(decorator, ast.Attribute):
                        context['decorators'].append(f"{decorator.attr}")
            elif isinstance(node, ast.Assign):
                # Look for field definitions
                for target in node.targets:
                    if isinstance(target, ast.Name) and "fields." in ast.dump(node.value):
                        context['field_definitions'].append(target.id)
        
        return context
    
    def _apply_import_transformation(self, content: str, transformation: PythonTransformation, context: Dict[str, Any]) -> Tuple[str, int]:
        """Apply import refactoring transformation"""
        changes = 0
        new_content = content
        
        # Simple pattern replacement for imports
        if transformation.source_pattern in content:
            new_content = content.replace(transformation.source_pattern, transformation.target_pattern)
            changes = content.count(transformation.source_pattern)
        
        return new_content, changes
    
    def _apply_api_transformation(self, content: str, transformation: PythonTransformation, context: Dict[str, Any]) -> Tuple[str, int]:
        """Apply API change transformation"""
        changes = 0
        new_content = content
        
        if transformation.transformation_id == "v13_v14_api_multi_removal":
            # Remove @api.multi decorators
            pattern = r'@api\.multi\s*\n'
            matches = re.findall(pattern, content)
            if matches:
                new_content = re.sub(pattern, '', content)
                changes = len(matches)
        
        elif transformation.transformation_id == "v16_v17_superuser_id_replacement":
            # Replace SUPERUSER_ID with env.su (context-aware)
            if "SUPERUSER_ID" in content and "env" in context.get('imports', []):
                new_content = content.replace("SUPERUSER_ID", "env.su")
                changes = content.count("SUPERUSER_ID")
        
        elif transformation.transformation_id == "v14_v15_sudo_method_evolution":
            # Replace .sudo() with .with_user() where appropriate
            pattern = r'\.sudo\(\)'
            if re.search(pattern, content):
                new_content = re.sub(pattern, '.with_user(SUPERUSER_ID)', content)
                changes = len(re.findall(pattern, content))
        
        return new_content, changes
    
    def _apply_business_logic_transformation(self, content: str, transformation: PythonTransformation, context: Dict[str, Any]) -> Tuple[str, int]:
        """Apply business logic transformation"""
        changes = 0
        new_content = content
        
        if transformation.transformation_id == "v16_v17_field_help_enhancement":
            # Add help text to fields that don't have it
            field_pattern = r'(\w+)\s*=\s*fields\.(Char|Text|Integer|Float|Boolean|Selection)\([^)]*\)'
            matches = re.finditer(field_pattern, content)
            
            for match in matches:
                field_def = match.group(0)
                if 'help=' not in field_def:
                    # Add help parameter
                    field_name = match.group(1)
                    help_text = f", help='Help text for {field_name}'"
                    new_field_def = field_def[:-1] + help_text + field_def[-1]
                    new_content = new_content.replace(field_def, new_field_def)
                    changes += 1
        
        elif transformation.transformation_id == "v17_v18_orm_performance_optimization":
            # Look for potential ORM optimization opportunities
            orm_methods = ['search', 'browse', 'create', 'write']
            for method in orm_methods:
                pattern = f'\\.{method}\\('
                if re.search(pattern, content):
                    # Add comment suggesting optimization review
                    comment = f"# TODO: Review {method} operation for v18 performance optimization"
                    if comment not in content:
                        new_content = f"{comment}\n{new_content}"
                        changes += 1
        
        return new_content, changes
    
    def _apply_inheritance_transformation(self, content: str, transformation: PythonTransformation, context: Dict[str, Any]) -> Tuple[str, int]:
        """Apply class inheritance transformation"""
        changes = 0
        new_content = content
        
        # This would handle more complex inheritance changes
        # For now, return unchanged
        return new_content, changes
    
    def _run_validation_rules(self, content: str, validation_rules: List[str], context: Dict[str, Any]) -> List[str]:
        """Run validation rules and return warnings"""
        warnings = []
        
        for rule in validation_rules:
            if rule == "check_sudo_context":
                if ".sudo()" in content and "with_user" not in content:
                    warnings.append("Consider using with_user() instead of sudo() for better context management")
            
            elif rule == "validate_selection_inheritance":
                if "selection_add=" in content and "_inherit" not in content:
                    warnings.append("selection_add should typically be used with model inheritance")
            
            elif rule == "check_superuser_context":
                if "SUPERUSER_ID" in content and "env.su" not in content:
                    warnings.append("Consider migrating SUPERUSER_ID to env.su for better context handling")
            
            elif rule == "ensure_field_help_exists":
                field_pattern = r'fields\.(Char|Text|Integer|Float|Boolean|Selection)\([^)]*\)'
                matches = re.findall(field_pattern, content)
                for match in matches:
                    if 'help=' not in match[0]:
                        warnings.append(f"Field definition missing help text: {match}")
            
            elif rule == "check_request_context_safety":
                if "request.env" in content:
                    warnings.append("Verify request.env usage for security and context safety in v18")
        
        return warnings
    
    def _generate_transformation_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of transformations"""
        return {
            'total_transformations': results['total_transformations_applied'],
            'successful_transformations': len([r for r in results['transformation_results'] if r.applied]),
            'files_modified': results['files_modified'],
            'transformation_types': results['transformations_by_type'],
            'validation_warnings': len(results['validation_warnings']),
            'errors': len(results['errors']),
            'complexity_breakdown': self._analyze_transformation_complexity(results['transformation_results'])
        }
    
    def _analyze_transformation_complexity(self, transformation_results: List[RuleApplicationResult]) -> Dict[str, int]:
        """Analyze complexity of applied transformations"""
        complexity_counts = {'simple': 0, 'medium': 0, 'complex': 0}
        
        for result in transformation_results:
            # This would be enhanced to track actual transformation complexity
            if result.changes_made <= 1:
                complexity_counts['simple'] += 1
            elif result.changes_made <= 5:
                complexity_counts['medium'] += 1
            else:
                complexity_counts['complex'] += 1
        
        return complexity_counts

def main():
    """Test the enhanced Python transformer"""
    transformer = EnhancedPythonTransformer()
    
    # Test with sample module
    test_module = "sample_modules/test_module"
    if os.path.exists(test_module):
        results = transformer.apply_transformations_to_module(test_module, "15.0", "17.0")
        print(f"Python Transformation Results:")
        print(f"Transformations applied: {results['total_transformations_applied']}")
        print(f"Files modified: {results['files_modified']}")
        print(f"Summary: {results['summary']}")

if __name__ == "__main__":
    main()