#!/usr/bin/env python3
"""
Functional verification test - Check if pages display actual data and connect to models properly
"""
import requests
import json
from bs4 import BeautifulSoup

BASE_URL = "http://localhost:5000"

def test_page_content(url, expected_elements=None):
    """Test if a page loads and contains expected content"""
    try:
        response = requests.get(f"{BASE_URL}{url}", timeout=10)
        if response.status_code != 200:
            return {
                'url': url,
                'status': 'FAILED',
                'error': f'HTTP {response.status_code}',
                'content_check': False
            }
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Check for basic structure
        has_title = bool(soup.find('title'))
        has_navbar = bool(soup.find('nav') or soup.find('.navbar'))
        has_content = bool(soup.find('main') or soup.find('.container') or soup.find('.content'))
        
        # Check for specific elements if provided
        element_checks = {}
        if expected_elements:
            for element in expected_elements:
                element_checks[element] = bool(soup.find(class_=element) or soup.find(id=element))
        
        # Look for error messages or empty states
        error_indicators = soup.find_all(text=lambda text: text and ('error' in text.lower() or 'not found' in text.lower()))
        empty_indicators = soup.find_all(class_=['empty', 'no-data', 'no-modules'])
        
        return {
            'url': url,
            'status': 'SUCCESS',
            'has_title': has_title,
            'has_navbar': has_navbar,
            'has_content': has_content,
            'element_checks': element_checks,
            'error_indicators': len(error_indicators),
            'empty_indicators': len(empty_indicators),
            'page_size': len(response.content),
            'contains_data': len(soup.get_text().strip()) > 1000  # Rough check for content
        }
        
    except Exception as e:
        return {
            'url': url,
            'status': 'ERROR',
            'error': str(e),
            'content_check': False
        }

def test_api_endpoints():
    """Test API endpoints that should return actual data"""
    api_tests = []
    
    # Test migration jobs API
    try:
        response = requests.get(f"{BASE_URL}/api/migration-jobs", timeout=5)
        jobs_data = response.json() if response.status_code == 200 else None
        api_tests.append({
            'endpoint': '/api/migration-jobs',
            'status_code': response.status_code,
            'has_data': bool(jobs_data),
            'data_type': type(jobs_data).__name__ if jobs_data else None
        })
    except Exception as e:
        api_tests.append({
            'endpoint': '/api/migration-jobs',
            'status_code': 'ERROR',
            'error': str(e)
        })
    
    # Test AI providers status
    try:
        response = requests.get(f"{BASE_URL}/ai_providers/status", timeout=5)
        providers_data = response.json() if response.status_code == 200 else None
        api_tests.append({
            'endpoint': '/ai_providers/status',
            'status_code': response.status_code,
            'has_data': bool(providers_data),
            'data_type': type(providers_data).__name__ if providers_data else None
        })
    except Exception as e:
        api_tests.append({
            'endpoint': '/ai_providers/status',
            'status_code': 'ERROR',
            'error': str(e)
        })
    
    return api_tests

def main():
    print("FUNCTIONAL VERIFICATION TEST")
    print("=" * 50)
    
    # Test main pages with expected elements
    pages_to_test = [
        ('/', ['card', 'statistics', 'dashboard']),
        ('/upload_modules', ['file-upload', 'form']),
        ('/analyze_modules', ['table', 'module-list']),
        ('/migration-jobs', ['migration', 'jobs']),
        ('/ai_providers', ['provider', 'ai']),
        ('/docker-environments', ['docker', 'environment']),
        ('/github-integration', ['github', 'integration']),
        ('/manual-interventions', ['intervention', 'queue']),
        ('/bulk-migration', ['bulk', 'migration']),
    ]
    
    print("\n=== PAGE CONTENT VERIFICATION ===")
    page_results = []
    for url, expected_elements in pages_to_test:
        result = test_page_content(url, expected_elements)
        page_results.append(result)
        
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        print(f"{status_icon} {url} - {result['status']}")
        
        if result['status'] == 'SUCCESS':
            print(f"   Content: {result['page_size']} bytes, Data: {'Yes' if result['contains_data'] else 'No'}")
            if result['error_indicators'] > 0:
                print(f"   ⚠️  {result['error_indicators']} error indicators found")
            if result['empty_indicators'] > 0:
                print(f"   ⚠️  {result['empty_indicators']} empty state indicators")
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
    
    print("\n=== API ENDPOINT VERIFICATION ===")
    api_results = test_api_endpoints()
    for result in api_results:
        status_icon = "✅" if result['status_code'] == 200 else "❌"
        print(f"{status_icon} {result['endpoint']} - HTTP {result['status_code']}")
        if 'has_data' in result:
            print(f"   Data: {'Yes' if result['has_data'] else 'No'} ({result.get('data_type', 'Unknown')})")
        if 'error' in result:
            print(f"   Error: {result['error']}")
    
    # Summary
    successful_pages = sum(1 for r in page_results if r['status'] == 'SUCCESS')
    successful_apis = sum(1 for r in api_results if r['status_code'] == 200)
    
    print(f"\n=== SUMMARY ===")
    print(f"Pages tested: {len(page_results)}")
    print(f"Successful pages: {successful_pages}/{len(page_results)} ({successful_pages/len(page_results)*100:.1f}%)")
    print(f"APIs tested: {len(api_results)}")
    print(f"Successful APIs: {successful_apis}/{len(api_results)} ({successful_apis/len(api_results)*100:.1f}%)")
    
    # Identify issues
    issues = []
    for result in page_results:
        if result['status'] != 'SUCCESS':
            issues.append(f"Page {result['url']}: {result.get('error', 'Failed to load')}")
        elif not result['contains_data']:
            issues.append(f"Page {result['url']}: May be missing data")
        elif result['error_indicators'] > 2:
            issues.append(f"Page {result['url']}: Multiple error indicators")
    
    for result in api_results:
        if result['status_code'] != 200:
            issues.append(f"API {result['endpoint']}: HTTP {result['status_code']}")
        elif not result.get('has_data', False):
            issues.append(f"API {result['endpoint']}: No data returned")
    
    if issues:
        print(f"\n=== ISSUES FOUND ===")
        for issue in issues:
            print(f"⚠️  {issue}")
    else:
        print(f"\n✅ All pages and APIs working correctly!")

if __name__ == '__main__':
    main()