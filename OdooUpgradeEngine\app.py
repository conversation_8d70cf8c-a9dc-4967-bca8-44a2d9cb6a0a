# app.py
from flask import Flask
from celery import Task
import os
from dotenv import load_dotenv
from flask_migrate import Migrate

from extensions import db, celery

def create_app() -> Flask:
    """Application factory to create and configure the Flask application."""
    app = Flask(__name__)
    load_dotenv()

    # --- Configuration ---
    app.config['SECRET_KEY'] = os.environ.get('SESSION_SECRET', 'a-very-strong-secret-key-for-dev')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///odoo_upgrade.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

    # Set GitHub token from user provided token
    if not os.environ.get('GITHUB_TOKEN'):
        os.environ['GITHUB_TOKEN'] = '****************************************'

    # Add cache-busting headers
    @app.after_request
    def after_request(response):
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response
    app.config.update(
        CELERY_BROKER_URL=os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
        CELERY_RESULT_BACKEND=os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
        CELERY_TASK_IMPORTS=('tasks',)
    )

    # --- Initialize Extensions ---
    db.init_app(app)

    # Import models to ensure they are registered with SQLAlchemy
    from models import OdooModule, MigrationJob, DiffReport, ModuleAnalysis

    Migrate(app, db)

    # --- Configure Celery with App Context ---
    celery.conf.update(app.config)
    class FlaskTask(Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    celery.Task = FlaskTask

    # --- Import and Register Blueprints ---
    from routes import main_routes
    from automation_integration import automation_blueprint
    from testing_blueprint import testing_blueprint

    app.register_blueprint(main_routes)
    app.register_blueprint(automation_blueprint)
    app.register_blueprint(testing_blueprint)

    return app

if __name__ == '__main__':
    # Create the app instance only when running directly
    app = create_app()
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='127.0.0.1', port=5000)