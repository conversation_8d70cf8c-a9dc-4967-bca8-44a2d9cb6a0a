
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Module Upgrade Report: business_appointment_hr_backup_1751596538</title>
            <link href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css" rel="stylesheet">
            <style>
                body { background-color: #1a1a1a; color: #e0e0e0; }
                .diff-container { margin: 20px 0; }
                .change-summary { background-color: #2d2d2d; padding: 15px; border-radius: 8px; margin: 10px 0; }
                .security-badge { padding: 2px 8px; border-radius: 4px; font-size: 12px; }
                .security-high { background-color: #dc3545; }
                .security-medium { background-color: #ffc107; color: #000; }
                .security-improvement { background-color: #28a745; }
                .security-low { background-color: #6c757d; }
                .complexity-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin: 0 2px; }
                .complexity-1 { background-color: #28a745; }
                .complexity-2 { background-color: #20c997; }
                .complexity-3 { background-color: #ffc107; }
                .complexity-4 { background-color: #fd7e14; }
                .complexity-5 { background-color: #dc3545; }
            </style>
        </head>
        <body>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <h1 class="mt-4">Module Upgrade Report</h1>
                        <h2 class="text-primary">business_appointment_hr_backup_1751596538</h2>
                        
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Files Changed</h5>
                                        <h3 class="text-info">7</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Lines Added</h5>
                                        <h3 class="text-success">+30</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Lines Removed</h5>
                                        <h3 class="text-danger">-82</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-dark">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Net Change</h5>
                                        <h3 class="text-warning">-52</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-5">
                            <h3>Change Summary</h3>
                            
                <div class="change-summary">
                    <h5>Compatibility Fixes (2)</h5>
                    <ul>
                
                        <li><strong>__manifest__.py</strong>: Updated for Odoo 17+ compatibility</li>
                    
                        <li><strong>models/business_resource.py</strong>: Updated for Odoo 17+ compatibility</li>
                    </ul></div>
                        </div>
                        
                        <div class="mt-5">
                            <h3>File-by-File Changes</h3>
                            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        __init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span>
                            <small class="text-muted">+0 -2</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> general_upgrade
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to0__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: __init__.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: __init__.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to0__0"><a href="#difflib_chg_to0__top">t</a></td><td class="diff_header" id="from0_1">1</td><td nowrap="nowrap"><span class="diff_sub">#&nbsp;-*-&nbsp;coding:&nbsp;utf-8&nbsp;-*-</span></td><td class="diff_next"><a href="#difflib_chg_to0__top">t</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_2">2</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_3">3</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;models</td><td class="diff_next"></td><td class="diff_header" id="to0_1">1</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;models</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_4">4</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;controllers</td><td class="diff_next"></td><td class="diff_header" id="to0_2">2</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;controllers</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from0_5">5</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;reports</td><td class="diff_next"></td><td class="diff_header" id="to0_3">3</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;reports</td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        __manifest__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span><span class="complexity-indicator complexity-3"></span><span class="complexity-indicator complexity-4"></span>
                            <small class="text-muted">+1 -40</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> manifest_modernization, security_improvement
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to1__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: __manifest__.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: __manifest__.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to1__0"><a href="#difflib_chg_to1__top">t</a></td><td class="diff_header" id="from1_1">1</td><td nowrap="nowrap"><span class="diff_sub">#&nbsp;-*-&nbsp;coding:&nbsp;utf-8&nbsp;-*-</span></td><td class="diff_next"><a href="#difflib_chg_to1__top">t</a></td><td class="diff_header" id="to1_1">1</td><td nowrap="nowrap"><span class="diff_add">{'installable':&nbsp;True,&nbsp;'auto_install':&nbsp;False}</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_2">2</td><td nowrap="nowrap"><span class="diff_sub">{</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_3">3</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"name":&nbsp;"Universal&nbsp;Appointments:&nbsp;HR&nbsp;Bridge",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_4">4</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"version":&nbsp;"15.0.1.0.5",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_5">5</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"category":&nbsp;"Extra&nbsp;Tools",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_6">6</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"author":&nbsp;"faOtools",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_7">7</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"website":&nbsp;"https://faotools.com/apps/15.0/universal-appointments-hr-bridge-</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">15-0-business-appointment-hr-610",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_8">8</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"license":&nbsp;"Other&nbsp;proprietary",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_9">9</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"application":&nbsp;True,</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_10">10</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"installable":&nbsp;True,</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_11">11</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"auto_install":&nbsp;False,</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_12">12</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"depends":&nbsp;[</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_13">13</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"hr",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_14">14</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"business_appointment"</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_15">15</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;],</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_16">16</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"data":&nbsp;[</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_17">17</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"views/business_resource.xml",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_18">18</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"security/ir.model.access.csv",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_19">19</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"data/data.xml"</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_20">20</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;],</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_21">21</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"js":&nbsp;[</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_22">22</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_23">23</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;],</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_24">24</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"assets":&nbsp;{},</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_25">25</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"demo":&nbsp;[</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_26">26</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_27">27</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;],</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_28">28</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"external_dependencies":&nbsp;{},</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_29">29</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"summary":&nbsp;"The&nbsp;extension&nbsp;to&nbsp;the&nbsp;Universal&nbsp;Appointments&nbsp;app&nbsp;to&nbsp;apply&nbsp;employe</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">es&nbsp;as&nbsp;appointment&nbsp;resources",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_30">30</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"description":&nbsp;"""</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_31">31</td><td nowrap="nowrap"><span class="diff_sub">For&nbsp;the&nbsp;full&nbsp;details&nbsp;look&nbsp;at&nbsp;static/description/index.html</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_32">32</td><td nowrap="nowrap"><span class="diff_sub">*&nbsp;Features&nbsp;*</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_33">33</td><td nowrap="nowrap"><span class="diff_sub">#odootools_proprietary""",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_34">34</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"images":&nbsp;[</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_35">35</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"static/description/main.png"</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_36">36</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;],</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_37">37</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"price":&nbsp;"0.0",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_38">38</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"currency":&nbsp;"EUR",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_39">39</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;"live_test_url":&nbsp;"https://faotools.com/my/tickets/newticket?&amp;url_app_id=136&amp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">ticket_version=15.0&amp;url_type_id=3",</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from1_40">40</td><td nowrap="nowrap"><span class="diff_sub">}</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        controllers/__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span>
                            <small class="text-muted">+0 -1</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> general_upgrade
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to2__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: controllers/__init__.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: controllers/__init__.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to2__0"><a href="#difflib_chg_to2__top">t</a></td><td class="diff_header" id="from2_1">1</td><td nowrap="nowrap"><span class="diff_sub">#&nbsp;-*-&nbsp;coding:&nbsp;utf-8&nbsp;-*-</span></td><td class="diff_next"><a href="#difflib_chg_to2__top">t</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        models/business_resource.py
                        <span class="security-badge security-medium">medium</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span><span class="complexity-indicator complexity-2"></span><span class="complexity-indicator complexity-3"></span><span class="complexity-indicator complexity-4"></span><span class="complexity-indicator complexity-5"></span>
                            <small class="text-muted">+29 -35</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> import_modernization, field_modernization, security_improvement
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to3__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: models/business_resource.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: models/business_resource.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to3__0"><a href="#difflib_chg_to3__1">n</a></td><td class="diff_header" id="from3_1">1</td><td nowrap="nowrap"><span class="diff_sub">#coding:&nbsp;utf-8</span></td><td class="diff_next"><a href="#difflib_chg_to3__1">n</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_2">2</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_3">3</td><td nowrap="nowrap">from&nbsp;odoo&nbsp;import&nbsp;_,&nbsp;api,&nbsp;fields,&nbsp;models</td><td class="diff_next"></td><td class="diff_header" id="to3_1">1</td><td nowrap="nowrap">from&nbsp;odoo&nbsp;import&nbsp;_,&nbsp;api,&nbsp;fields,&nbsp;models</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_4">4</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_2">2</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_5">5</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_3">3</td><td nowrap="nowrap"></td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to3__1"></td><td class="diff_header" id="from3_7">7</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;"""</td><td class="diff_next"></td><td class="diff_header" id="to3_5">5</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;"""</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_8">8</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;Overwrite&nbsp;to&nbsp;link&nbsp;business&nbsp;resources&nbsp;with&nbsp;employee</td><td class="diff_next"></td><td class="diff_header" id="to3_6">6</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;Overwrite&nbsp;to&nbsp;link&nbsp;business&nbsp;resources&nbsp;with&nbsp;employee</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__2"></td><td class="diff_header" id="from3_9">9</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;"""</td><td class="diff_next"></td><td class="diff_header" id="to3_7">7</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;"""</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__2">n</a></td><td class="diff_header" id="from3_10">10</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;_inherit&nbsp;=&nbsp;<span class="diff_chg">"</span>business.resource<span class="diff_chg">"</span></td><td class="diff_next"><a href="#difflib_chg_to3__2">n</a></td><td class="diff_header" id="to3_8">8</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;_inherit&nbsp;=&nbsp;<span class="diff_chg">'</span>business.resource<span class="diff_chg">'</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_11">11</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_9">9</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__3">n</a></td><td class="diff_header" id="from3_12">12</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;@api.onchange(<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>)</td><td class="diff_next"><a href="#difflib_chg_to3__3">n</a></td><td class="diff_header" id="to3_10">10</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;@api.onchange(<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>)</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_13">13</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;_onchange_employee_id(self):</td><td class="diff_next"></td><td class="diff_header" id="to3_11">11</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;_onchange_employee_id(self):</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_14">14</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"""</td><td class="diff_next"></td><td class="diff_header" id="to3_12">12</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"""</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_15">15</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Onchange&nbsp;method&nbsp;for&nbsp;employee_id</td><td class="diff_next"></td><td class="diff_header" id="to3_13">13</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Onchange&nbsp;method&nbsp;for&nbsp;employee_id</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to3__3"></td><td class="diff_header" id="from3_20">20</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;resource.employee_id.user_id:</td><td class="diff_next"></td><td class="diff_header" id="to3_18">18</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;resource.employee_id.user_id:</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_21">21</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource.user_id&nbsp;=&nbsp;resource.employee_id.user_id</td><td class="diff_next"></td><td class="diff_header" id="to3_19">19</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource.user_id&nbsp;=&nbsp;resource.employee_id.user_id</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_22">22</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;resource.employee_id.resource_calendar_id:</td><td class="diff_next"></td><td class="diff_header" id="to3_20">20</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;resource.employee_id.resource_calendar_id:</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__4">n</a></td><td class="diff_header" id="from3_23">23</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource.resource_calendar_id&nbsp;=&nbsp;resource.employee_id.<span class="diff_sub">resourc</span></td><td class="diff_next"><a href="#difflib_chg_to3__4">n</a></td><td class="diff_header" id="to3_21">21</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource.resource_calendar_id&nbsp;=&nbsp;<span class="diff_add">(</span>resource.employee_id.</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">e_calendar_id</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_24">24</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;</span></td><td class="diff_next"></td><td class="diff_header" id="to3_22">22</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_calendar_id)</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_25">25</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;employee_id&nbsp;=&nbsp;fields.Many2one(</span></td><td class="diff_next"></td><td class="diff_header" id="to3_23">23</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;employee_id&nbsp;=&nbsp;fields.Many2one('hr.employee',&nbsp;string='Employee',</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_26">26</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"hr.employee",&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_27">27</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;string="Employee",&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_28">28</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ondelete=<span class="diff_chg">"</span>cascade<span class="diff_chg">"</span>,</td><td class="diff_next"></td><td class="diff_header" id="to3_24">24</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ondelete=<span class="diff_chg">'</span>cascade<span class="diff_chg">'</span>,<span class="diff_add">&nbsp;copy=False)</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_29">29</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;copy=False,</span></td><td class="diff_next"></td><td class="diff_header" id="to3_25">25</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;_sql_constraints&nbsp;=&nbsp;[('employee_id_uniq',&nbsp;'unique(employee_id)',&nbsp;_(</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_30">30</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;)</span></td><td class="diff_next"></td><td class="diff_header" id="to3_26">26</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'Resource&nbsp;per&nbsp;each&nbsp;employee&nbsp;should&nbsp;be&nbsp;unique!'))]</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_31">31</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_32">32</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;_sql_constraints&nbsp;=&nbsp;[("employee_id_uniq",&nbsp;"unique(employee_id)",&nbsp;_("Resource&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">per&nbsp;each&nbsp;employee&nbsp;should&nbsp;be&nbsp;unique!"))]</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_33">33</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_27">27</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;@api.model</td><td class="diff_next"></td><td class="diff_header" id="to3_28">28</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;@api.model</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_35">35</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;create(self,&nbsp;vals):</td><td class="diff_next"></td><td class="diff_header" id="to3_29">29</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;create(self,&nbsp;vals):</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__4"></td><td class="diff_header" id="from3_36">36</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"""</td><td class="diff_next"></td><td class="diff_header" id="to3_30">30</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"""</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_37">37</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Overwrite&nbsp;to&nbsp;retrieve&nbsp;resource&nbsp;from&nbsp;employee</td><td class="diff_next"></td><td class="diff_header" id="to3_31">31</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Overwrite&nbsp;to&nbsp;retrieve&nbsp;resource&nbsp;from&nbsp;employee</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_38">38</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"""</td><td class="diff_next"></td><td class="diff_header" id="to3_32">32</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"""</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__5">n</a></td><td class="diff_header" id="from3_39">39</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;vals.get(<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>):</td><td class="diff_next"><a href="#difflib_chg_to3__5">n</a></td><td class="diff_header" id="to3_33">33</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;vals.get(<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>):</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__5"></td><td class="diff_header" id="from3_40">40</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;vals.get(<span class="diff_chg">"</span>main_resource_id<span class="diff_chg">"</span>):</td><td class="diff_next"></td><td class="diff_header" id="to3_34">34</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;vals.get(<span class="diff_chg">'</span>main_resource_id<span class="diff_chg">'</span>):</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_41">41</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vals.pop(<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>)</td><td class="diff_next"></td><td class="diff_header" id="to3_35">35</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vals.pop(<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>)</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_42">42</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class="diff_next"></td><td class="diff_header" id="to3_36">36</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__6">n</a></td><td class="diff_header" id="from3_43">43</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_id&nbsp;=&nbsp;self.env[<span class="diff_chg">"</span>hr.employee<span class="diff_chg">"</span>].browse(vals.get(<span class="diff_sub">"employee_</span></td><td class="diff_next"><a href="#difflib_chg_to3__6">n</a></td><td class="diff_header" id="to3_37">37</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_id&nbsp;=&nbsp;self.env[<span class="diff_chg">'</span>hr.employee<span class="diff_chg">'</span>].browse(vals.get(</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">id")).resource_id</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_38">38</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'employee_id')).resource_id</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_44">44</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vals.update({<span class="diff_chg">"</span>resource_id<span class="diff_chg">"</span>:&nbsp;resource_id.id})</td><td class="diff_next"></td><td class="diff_header" id="to3_39">39</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vals.update({<span class="diff_chg">'</span>resource_id<span class="diff_chg">'</span>:&nbsp;resource_id.id})</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_45">45</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;vals.get(<span class="diff_chg">"</span>tz<span class="diff_chg">"</span>):</td><td class="diff_next"></td><td class="diff_header" id="to3_40">40</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;vals.get(<span class="diff_chg">'</span>tz<span class="diff_chg">'</span>):</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_46">46</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vals.update({<span class="diff_chg">"</span>tz<span class="diff_chg">"</span>:&nbsp;resource_id.tz})</td><td class="diff_next"></td><td class="diff_header" id="to3_41">41</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vals.update({<span class="diff_chg">'</span>tz<span class="diff_chg">'</span>:&nbsp;resource_id.tz})</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_47">47</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;super(business_resource,&nbsp;self).create(vals)</td><td class="diff_next"></td><td class="diff_header" id="to3_42">42</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;super(business_resource,&nbsp;self).create(vals)</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_48">48</td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_43">43</td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_49">49</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;write(self,&nbsp;vals):</td><td class="diff_next"></td><td class="diff_header" id="to3_44">44</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;def&nbsp;write(self,&nbsp;vals):</td></tr>
        </tbody>        
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to3__6"></td><td class="diff_header" id="from3_56">56</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res&nbsp;=&nbsp;False</td><td class="diff_next"></td><td class="diff_header" id="to3_51">51</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res&nbsp;=&nbsp;False</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_57">57</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;resource&nbsp;in&nbsp;self:</td><td class="diff_next"></td><td class="diff_header" id="to3_52">52</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for&nbsp;resource&nbsp;in&nbsp;self:</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_58">58</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals&nbsp;=&nbsp;vals.copy()</td><td class="diff_next"></td><td class="diff_header" id="to3_53">53</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals&nbsp;=&nbsp;vals.copy()</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__7">n</a></td><td class="diff_header" id="from3_59">59</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;main_resource_id&nbsp;=&nbsp;resource_vals.get(<span class="diff_chg">"</span>main_resource_id<span class="diff_chg">")&nbsp;\</span></td><td class="diff_next"><a href="#difflib_chg_to3__7">n</a></td><td class="diff_header" id="to3_54">54</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;main_resource_id&nbsp;=&nbsp;resource_vals.get(<span class="diff_chg">'</span>main_resource_id<span class="diff_chg">'</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_60">60</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;or&nbsp;(resource_vals.get("main_resource_id")&nbsp;is&nbsp;None</span></td><td class="diff_next"></td><td class="diff_header" id="to3_55">55</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;or&nbsp;resource_vals.get('main_resource_id'</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">&nbsp;and&nbsp;resource.main_resource_id)</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__7"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_56">56</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;is&nbsp;None&nbsp;and&nbsp;resource.main_resource_id</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_61">61</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;resource_vals.get(<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>):</td><td class="diff_next"></td><td class="diff_header" id="to3_57">57</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;resource_vals.get(<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>):</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_62">62</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;main_resource_id:</td><td class="diff_next"></td><td class="diff_header" id="to3_58">58</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;main_resource_id:</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__8"><a href="#difflib_chg_to3__8">n</a></td><td class="diff_header" id="from3_63">63</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;0</span></td><td class="diff_next"><a href="#difflib_chg_to3__8">n</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_64">64</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.pop(<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>)</td><td class="diff_next"></td><td class="diff_header" id="to3_59">59</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.pop(<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>)</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_65">65</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else:</td><td class="diff_next"></td><td class="diff_header" id="to3_60">60</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else:</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__9">n</a></td><td class="diff_header" id="from3_66">66</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;1</span></td><td class="diff_next"><a href="#difflib_chg_to3__9">n</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_67">67</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_id&nbsp;=&nbsp;self.env[<span class="diff_chg">"</span>hr.employee<span class="diff_chg">"</span>].browse(resource_vals<span class="diff_sub">.g</span></td><td class="diff_next"></td><td class="diff_header" id="to3_61">61</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_id&nbsp;=&nbsp;self.env[<span class="diff_chg">'</span>hr.employee<span class="diff_chg">'</span>].browse(resource_vals</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header">></td><td nowrap="nowrap"><span class="diff_sub">et("employee_id")).resource_id.id</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap">&nbsp;</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_62">62</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;.get('employee_id')).resource_id.id</span></td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__9"></td><td class="diff_header" id="from3_68">68</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.update({<span class="diff_chg">"</span>resource_id<span class="diff_chg">"</span>:&nbsp;resource_id})</td><td class="diff_next"></td><td class="diff_header" id="to3_63">63</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.update({<span class="diff_chg">'</span>resource_id<span class="diff_chg">'</span>:&nbsp;resource_id})</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_69">69</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;resource.employee_id:</td><td class="diff_next"></td><td class="diff_header" id="to3_64">64</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;not&nbsp;resource.employee_id:</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_70">70</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource.resource_id.active&nbsp;=&nbsp;False</td><td class="diff_next"></td><td class="diff_header" id="to3_65">65</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource.resource_id.active&nbsp;=&nbsp;False</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__10">n</a></td><td class="diff_header" id="from3_71">71</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;elif&nbsp;resource_vals.get(<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>)&nbsp;is&nbsp;not&nbsp;None:</td><td class="diff_next"><a href="#difflib_chg_to3__10">n</a></td><td class="diff_header" id="to3_66">66</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;elif&nbsp;resource_vals.get(<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>)&nbsp;is&nbsp;not&nbsp;None:</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_72">72</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;2</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_73">73</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_id&nbsp;=&nbsp;resource.resource_id.copy({<span class="diff_chg">"</span>name<span class="diff_chg">"</span>:&nbsp;resource.name}<span class="diff_sub">)</span></td><td class="diff_next"></td><td class="diff_header" id="to3_67">67</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_id&nbsp;=&nbsp;resource.resource_id.copy({<span class="diff_chg">'</span>name<span class="diff_chg">'</span>:&nbsp;resource.name}</td></tr>
            <tr><td class="diff_next" id="difflib_chg_to3__10"></td><td class="diff_header"></td><td nowrap="nowrap"></td><td class="diff_next"></td><td class="diff_header" id="to3_68">68</td><td nowrap="nowrap"><span class="diff_add">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_74">74</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.update({<span class="diff_chg">"</span>resource_id<span class="diff_chg">"</span>:&nbsp;resource_id.id})</td><td class="diff_next"></td><td class="diff_header" id="to3_69">69</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.update({<span class="diff_chg">'</span>resource_id<span class="diff_chg">'</span>:&nbsp;resource_id.id})</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_75">75</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;elif&nbsp;main_resource_id&nbsp;and&nbsp;resource.employee_id:</td><td class="diff_next"></td><td class="diff_header" id="to3_70">70</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;elif&nbsp;main_resource_id&nbsp;and&nbsp;resource.employee_id:</td></tr>
            <tr><td class="diff_next"><a href="#difflib_chg_to3__top">t</a></td><td class="diff_header" id="from3_76">76</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#&nbsp;0</span></td><td class="diff_next"><a href="#difflib_chg_to3__top">t</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_77">77</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.update({<span class="diff_chg">"</span>employee_id<span class="diff_chg">"</span>:&nbsp;False})</td><td class="diff_next"></td><td class="diff_header" id="to3_71">71</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;resource_vals.update({<span class="diff_chg">'</span>employee_id<span class="diff_chg">'</span>:&nbsp;False})</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_78">78</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res&nbsp;=&nbsp;super(business_resource,&nbsp;resource).write(resource_vals)</td><td class="diff_next"></td><td class="diff_header" id="to3_72">72</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;res&nbsp;=&nbsp;super(business_resource,&nbsp;resource).write(resource_vals)</td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from3_79">79</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;res</td><td class="diff_next"></td><td class="diff_header" id="to3_73">73</td><td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;res</td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        models/__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span>
                            <small class="text-muted">+0 -2</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> general_upgrade
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to4__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: models/__init__.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: models/__init__.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to4__0"><a href="#difflib_chg_to4__top">t</a></td><td class="diff_header" id="from4_1">1</td><td nowrap="nowrap"><span class="diff_sub">#&nbsp;-*-&nbsp;coding:&nbsp;utf-8&nbsp;-*-</span></td><td class="diff_next"><a href="#difflib_chg_to4__top">t</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_2">2</td><td nowrap="nowrap"><span class="diff_sub">&nbsp;</span></td><td class="diff_next"></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
            <tr><td class="diff_next"></td><td class="diff_header" id="from4_3">3</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;business_resource</td><td class="diff_next"></td><td class="diff_header" id="to4_1">1</td><td nowrap="nowrap">from&nbsp;.&nbsp;import&nbsp;business_resource</td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        reports/__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span>
                            <small class="text-muted">+0 -1</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> general_upgrade
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to5__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: reports/__init__.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: reports/__init__.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to5__0"><a href="#difflib_chg_to5__top">t</a></td><td class="diff_header" id="from5_1">1</td><td nowrap="nowrap"><span class="diff_sub">#&nbsp;-*-&nbsp;coding:&nbsp;utf-8&nbsp;-*-</span></td><td class="diff_next"><a href="#difflib_chg_to5__top">t</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
            <div class="card bg-dark mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        wizard/__init__.py
                        <span class="security-badge security-low">low</span>
                        <span class="float-end">
                            <span class="complexity-indicator complexity-1"></span>
                            <small class="text-muted">+0 -1</small>
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Change Types:</strong> general_upgrade
                    </div>
                    
        <div class="diff-container">
            <style>
                .diff-container table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                }
                .diff-container td {
                    padding: 2px 8px;
                    border: 1px solid #ddd;
                    vertical-align: top;
                }
                .diff-container .diff_add {
                    background-color: #d4edda;
                    color: #155724;
                }
                .diff-container .diff_sub {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .diff-container .diff_chg {
                    background-color: #fff3cd;
                    color: #856404;
                }
                .diff-container .diff_header {
                    background-color: #e9ecef;
                    font-weight: bold;
                    text-align: center;
                }
            </style>
            
    <table class="diff" id="difflib_chg_to6__top"
           cellspacing="0" cellpadding="0" rules="groups" >
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <colgroup></colgroup> <colgroup></colgroup> <colgroup></colgroup>
        <thead><tr><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Original: wizard/__init__.py</th><th class="diff_next"><br /></th><th colspan="2" class="diff_header">Upgraded: wizard/__init__.py</th></tr></thead>
        <tbody>
            <tr><td class="diff_next" id="difflib_chg_to6__0"><a href="#difflib_chg_to6__top">t</a></td><td class="diff_header" id="from6_1">1</td><td nowrap="nowrap"><span class="diff_sub">#&nbsp;-*-&nbsp;coding:&nbsp;utf-8&nbsp;-*-</span></td><td class="diff_next"><a href="#difflib_chg_to6__top">t</a></td><td class="diff_header"></td><td nowrap="nowrap"></td></tr>
        </tbody>
    </table>
        </div>
        
                </div>
            </div>
            
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        