"""
Module Sync Manager - GitHub Integration & Deduplication System

This module handles:
1. Syncing uploaded modules with GitHub automation folders
2. Version deduplication (keeping only newest versions)
3. Automatic cleanup of older module versions
4. Proper folder organization for automation pipeline
"""

import os
import shutil
import re
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import git
from packaging import version

logger = logging.getLogger(__name__)

class ModuleSyncManager:
    """
    Manages module synchronization between web uploads and GitHub automation system
    """
    
    def __init__(self, config_path: str = "automation_config.json"):
        """Initialize the sync manager"""
        self.config = self._load_config(config_path)
        self.setup_logging()
        
        # Directory paths
        self.uploads_dir = Path("uploads")
        self.automation_base = Path("automation_modules")
        self.github_repo_path = Path("github_repo")
        
        # Version directories for automation
        self.version_dirs = {
            "13.0": self.automation_base / "v13_originals",
            "14.0": self.automation_base / "v14_originals", 
            "15.0": self.automation_base / "v15_originals",
            "16.0": self.automation_base / "v16_originals",
            "17.0": self.automation_base / "v17_originals",
            "18.0": self.automation_base / "v18_originals"
        }
        
        self._ensure_directories()
        
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self._create_default_config(config_path)
    
    def _create_default_config(self, config_path: str) -> Dict:
        """Create default configuration"""
        config = {
            "github": {
                "repo_url": "https://github.com/yerenwgventures/OdooUpgradeEngine.git",
                "branch": "main",
                "auto_sync": True,
                "sync_interval_hours": 1
            },
            "sync": {
                "cleanup_older_versions": True,
                "backup_before_cleanup": True,
                "max_versions_per_module": 1
            },
            "logging": {
                "level": "INFO",
                "file": "automation_logs/sync_manager.log"
            }
        }
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        return config
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = Path("automation_logs")
        log_dir.mkdir(exist_ok=True)
        
        # Get logging config with defaults
        log_config = self.config.get("logging", {})
        log_level = log_config.get("level", "INFO")
        log_file = log_config.get("file", "automation_logs/sync_manager.log")
        
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        for path in [self.uploads_dir, self.automation_base, self.github_repo_path]:
            path.mkdir(exist_ok=True)
        
        for version_dir in self.version_dirs.values():
            version_dir.mkdir(parents=True, exist_ok=True)
    
    def sync_uploaded_modules(self) -> Dict[str, any]:
        """
        Sync all uploaded modules to appropriate automation folders
        
        Returns:
            Summary of sync operations
        """
        logger.info("Starting module sync process...")
        
        sync_summary = {
            "processed_modules": 0,
            "deduplicated_modules": 0,
            "moved_modules": 0,
            "errors": [],
            "operations": []
        }
        
        try:
            # Get all uploaded modules
            uploaded_modules = self._discover_uploaded_modules()
            logger.info(f"Found {len(uploaded_modules)} uploaded modules")
            
            for module_info in uploaded_modules:
                try:
                    result = self._process_module(module_info)
                    sync_summary["operations"].append(result)
                    sync_summary["processed_modules"] += 1
                    
                    if result.get("deduplicated"):
                        sync_summary["deduplicated_modules"] += 1
                    if result.get("moved"):
                        sync_summary["moved_modules"] += 1
                        
                except Exception as e:
                    error_msg = f"Error processing {module_info.get('name', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    sync_summary["errors"].append(error_msg)
            
            # Sync with GitHub if configured
            github_config = self.config.get("github", {})
            if github_config.get("auto_sync", False):
                self._sync_with_github()
                
        except Exception as e:
            logger.error(f"Critical error in sync process: {str(e)}")
            sync_summary["errors"].append(f"Critical error: {str(e)}")
        
        logger.info(f"Sync completed: {sync_summary}")
        return sync_summary
    
    def _discover_uploaded_modules(self) -> List[Dict]:
        """Discover all uploaded modules and their metadata"""
        modules = []
        
        if not self.uploads_dir.exists():
            return modules
        
        for item in self.uploads_dir.iterdir():
            if item.is_file() and item.suffix.lower() in ['.zip', '.tar', '.gz']:
                module_info = self._analyze_module_file(item)
                if module_info:
                    modules.append(module_info)
        
        return modules
    
    def _analyze_module_file(self, file_path: Path) -> Optional[Dict]:
        """Analyze a module file to extract metadata"""
        try:
            # Extract basic info from filename
            filename = file_path.stem
            
            # Try to detect version from filename patterns
            version_pattern = r'(\d+\.\d+(?:\.\d+)*)'
            version_match = re.search(version_pattern, filename)
            
            module_info = {
                "file_path": file_path,
                "filename": filename,
                "size": file_path.stat().st_size,
                "modified": datetime.fromtimestamp(file_path.stat().st_mtime),
                "detected_version": None,
                "module_name": None
            }
            
            if version_match:
                module_info["detected_version"] = version_match.group(1)
            
            # Try to extract module name (remove version and common suffixes)
            clean_name = filename
            if version_match:
                clean_name = clean_name.replace(version_match.group(0), '')
            
            # Remove common patterns
            clean_name = re.sub(r'[_\-\.]*(v\d+|ver\d+|version\d+)[_\-\.]*', '', clean_name, flags=re.IGNORECASE)
            clean_name = re.sub(r'[_\-\.]+$', '', clean_name)
            clean_name = re.sub(r'^[_\-\.]+', '', clean_name)
            
            module_info["module_name"] = clean_name or filename
            
            return module_info
            
        except Exception as e:
            logger.warning(f"Could not analyze module file {file_path}: {str(e)}")
            return None
    
    def _process_module(self, module_info: Dict) -> Dict:
        """Process a single module for deduplication and organization"""
        result = {
            "module_name": module_info["module_name"],
            "filename": module_info["filename"],
            "action": "none",
            "moved": False,
            "deduplicated": False,
            "target_location": None
        }
        
        # Determine target version directory
        target_version = self._determine_target_version(module_info)
        if not target_version:
            result["action"] = "skipped - unknown version"
            return result
        
        target_dir = self.version_dirs[target_version]
        
        # Check for existing versions of this module
        existing_modules = self._find_existing_modules(module_info["module_name"], target_dir)
        
        if existing_modules:
            # Handle deduplication
            dedup_result = self._handle_deduplication(module_info, existing_modules, target_dir)
            result.update(dedup_result)
        else:
            # Move new module to target directory
            move_result = self._move_module(module_info, target_dir)
            result.update(move_result)
        
        return result
    
    def _determine_target_version(self, module_info: Dict) -> Optional[str]:
        """Determine which version directory this module belongs to"""
        detected_version = module_info.get("detected_version")
        
        if not detected_version:
            # Try to analyze the module content for version info
            detected_version = self._analyze_module_content_version(module_info["file_path"])
        
        if detected_version:
            # Map to our version directories
            version_major_minor = ".".join(detected_version.split(".")[:2])
            if version_major_minor in self.version_dirs:
                return version_major_minor
            
            # Try to find closest match
            for ver in ["18.0", "17.0", "16.0", "15.0", "14.0", "13.0"]:
                if detected_version.startswith(ver.split(".")[0]):
                    return ver
        
        # Default to latest version if can't determine
        return "18.0"
    
    def _analyze_module_content_version(self, file_path: Path) -> Optional[str]:
        """Analyze module content to detect version"""
        # This would involve extracting and reading manifest files
        # For now, return None and rely on filename detection
        return None
    
    def _find_existing_modules(self, module_name: str, target_dir: Path) -> List[Path]:
        """Find existing modules with the same name in target directory"""
        existing = []
        
        if not target_dir.exists():
            return existing
        
        # Look for files with similar names
        for file_path in target_dir.iterdir():
            if file_path.is_file():
                # Extract module name from existing file
                existing_name = self._extract_module_name(file_path.stem)
                if existing_name.lower() == module_name.lower():
                    existing.append(file_path)
        
        return existing
    
    def _extract_module_name(self, filename: str) -> str:
        """Extract clean module name from filename"""
        # Remove version patterns
        clean_name = re.sub(r'[_\-\.]*(v?\d+\.\d+(?:\.\d+)*)[_\-\.]*', '', filename, flags=re.IGNORECASE)
        clean_name = re.sub(r'[_\-\.]+$', '', clean_name)
        clean_name = re.sub(r'^[_\-\.]+', '', clean_name)
        return clean_name or filename
    
    def _handle_deduplication(self, new_module: Dict, existing_modules: List[Path], target_dir: Path) -> Dict:
        """Handle deduplication when module already exists"""
        result = {
            "action": "deduplication",
            "moved": False,
            "deduplicated": False
        }
        
        # Compare versions to determine which to keep
        new_version = self._extract_version_from_filename(new_module["filename"])
        
        modules_to_remove = []
        should_replace = False
        
        for existing_path in existing_modules:
            existing_version = self._extract_version_from_filename(existing_path.stem)
            
            if new_version and existing_version:
                try:
                    if version.parse(new_version) > version.parse(existing_version):
                        # New version is higher, mark existing for removal
                        modules_to_remove.append(existing_path)
                        should_replace = True
                    elif version.parse(new_version) <= version.parse(existing_version):
                        # Existing version is higher or equal, skip new module
                        result["action"] = "skipped - older or equal version exists"
                        return result
                except Exception as e:
                    logger.warning(f"Could not compare versions {new_version} vs {existing_version}: {str(e)}")
                    # If can't compare, check by modification time
                    if new_module["modified"] > datetime.fromtimestamp(existing_path.stat().st_mtime):
                        modules_to_remove.append(existing_path)
                        should_replace = True
            else:
                # No version info, compare by modification time
                if new_module["modified"] > datetime.fromtimestamp(existing_path.stat().st_mtime):
                    modules_to_remove.append(existing_path)
                    should_replace = True
        
        if should_replace:
            # Backup old modules before removal
            if self.config["sync"]["backup_before_cleanup"]:
                self._backup_modules(modules_to_remove)
            
            # Remove old modules
            for old_module in modules_to_remove:
                try:
                    old_module.unlink()
                    logger.info(f"Removed older version: {old_module}")
                except Exception as e:
                    logger.error(f"Could not remove {old_module}: {str(e)}")
            
            # Move new module
            move_result = self._move_module(new_module, target_dir)
            result.update(move_result)
            result["deduplicated"] = True
        
        return result
    
    def _extract_version_from_filename(self, filename: str) -> Optional[str]:
        """Extract version string from filename"""
        # Look for version patterns like 17.0.1.2, 16.0.0.1, etc.
        patterns = [
            r'(\d+\.\d+\.\d+\.\d+)',  # 17.0.1.2
            r'(\d+\.\d+\.\d+)',       # 17.0.1
            r'(\d+\.\d+)',            # 17.0
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                return match.group(1)
        
        return None
    
    def _backup_modules(self, modules_to_backup: List[Path]):
        """Backup modules before removal"""
        backup_dir = Path("automation_logs/backups") / datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        for module_path in modules_to_backup:
            try:
                backup_path = backup_dir / module_path.name
                shutil.copy2(module_path, backup_path)
                logger.info(f"Backed up {module_path} to {backup_path}")
            except Exception as e:
                logger.error(f"Could not backup {module_path}: {str(e)}")
    
    def _move_module(self, module_info: Dict, target_dir: Path) -> Dict:
        """Move module to target directory"""
        result = {
            "action": "moved",
            "moved": False,
            "target_location": None
        }
        
        try:
            source_path = module_info["file_path"]
            target_path = target_dir / source_path.name
            
            # Ensure target directory exists
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Move the file
            shutil.move(str(source_path), str(target_path))
            
            result["moved"] = True
            result["target_location"] = str(target_path)
            
            logger.info(f"Moved {source_path} to {target_path}")
            
        except Exception as e:
            logger.error(f"Could not move {module_info['filename']}: {str(e)}")
            result["action"] = f"failed - {str(e)}"
        
        return result
    
    def _sync_with_github(self):
        """Sync automation folders with GitHub repository"""
        try:
            github_token = os.getenv("GITHUB_TOKEN")
            if not github_token:
                logger.warning("GITHUB_TOKEN not set, skipping GitHub sync")
                return
            
            repo_url = self.config["github"]["repo_url"]
            repo_url_with_token = repo_url.replace("https://", f"https://{github_token}@")
            
            # Clone or pull repository
            if self.github_repo_path.exists() and (self.github_repo_path / ".git").exists():
                # Pull latest changes
                repo = git.Repo(self.github_repo_path)
                repo.remotes.origin.pull()
                logger.info("Pulled latest changes from GitHub")
            else:
                # Clone repository
                if self.github_repo_path.exists():
                    shutil.rmtree(self.github_repo_path)
                git.Repo.clone_from(repo_url_with_token, self.github_repo_path)
                logger.info("Cloned repository from GitHub")
            
            # Sync automation folders to GitHub repo
            self._sync_folders_to_github()
            
            # Commit and push changes
            self._commit_and_push_changes()
            
        except Exception as e:
            logger.error(f"GitHub sync failed: {str(e)}")
    
    def _sync_folders_to_github(self):
        """Sync automation folders to GitHub repository"""
        for version, source_dir in self.version_dirs.items():
            if source_dir.exists():
                target_dir = self.github_repo_path / f"automation_modules/v{version.replace('.', '')}_originals"
                target_dir.mkdir(parents=True, exist_ok=True)
                
                # Copy files
                for file_path in source_dir.iterdir():
                    if file_path.is_file():
                        target_path = target_dir / file_path.name
                        shutil.copy2(file_path, target_path)
                
                logger.info(f"Synced {source_dir} to {target_dir}")
    
    def _commit_and_push_changes(self):
        """Commit and push changes to GitHub"""
        try:
            repo = git.Repo(self.github_repo_path)
            
            # Add all changes
            repo.git.add(A=True)
            
            # Check if there are changes to commit
            if repo.is_dirty():
                commit_message = f"Automated sync: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                repo.index.commit(commit_message)
                
                # Push changes
                repo.remotes.origin.push()
                logger.info(f"Committed and pushed changes: {commit_message}")
            else:
                logger.info("No changes to commit")
                
        except Exception as e:
            logger.error(f"Could not commit/push changes: {str(e)}")
    
    def cleanup_old_versions(self) -> Dict[str, any]:
        """Clean up old versions based on configuration"""
        cleanup_summary = {
            "cleaned_modules": 0,
            "freed_space": 0,
            "errors": []
        }
        
        if not self.config["sync"]["cleanup_older_versions"]:
            return cleanup_summary
        
        max_versions = self.config["sync"]["max_versions_per_module"]
        
        for version_dir in self.version_dirs.values():
            if not version_dir.exists():
                continue
            
            # Group modules by name
            module_groups = {}
            for file_path in version_dir.iterdir():
                if file_path.is_file():
                    module_name = self._extract_module_name(file_path.stem)
                    if module_name not in module_groups:
                        module_groups[module_name] = []
                    module_groups[module_name].append(file_path)
            
            # Clean up each group
            for module_name, files in module_groups.items():
                if len(files) > max_versions:
                    # Sort by modification time, keep newest
                    files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                    files_to_remove = files[max_versions:]
                    
                    for file_path in files_to_remove:
                        try:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            cleanup_summary["cleaned_modules"] += 1
                            cleanup_summary["freed_space"] += file_size
                            logger.info(f"Cleaned up old version: {file_path}")
                        except Exception as e:
                            error_msg = f"Could not remove {file_path}: {str(e)}"
                            cleanup_summary["errors"].append(error_msg)
                            logger.error(error_msg)
        
        return cleanup_summary
    
    def get_sync_status(self) -> Dict[str, any]:
        """Get current sync status"""
        status = {
            "upload_dir_modules": 0,
            "automation_modules": {},
            "github_sync_enabled": self.config["github"]["auto_sync"],
            "last_sync": None
        }
        
        # Count uploaded modules
        if self.uploads_dir.exists():
            status["upload_dir_modules"] = len([f for f in self.uploads_dir.iterdir() if f.is_file()])
        
        # Count automation modules by version
        for version, version_dir in self.version_dirs.items():
            if version_dir.exists():
                status["automation_modules"][version] = len([f for f in version_dir.iterdir() if f.is_file()])
            else:
                status["automation_modules"][version] = 0
        
        return status