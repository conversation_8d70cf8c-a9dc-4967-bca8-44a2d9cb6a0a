{% extends "base.html" %}
{% set title = "Dashboard" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-4">
            <i class="fas fa-tachometer-alt me-3"></i>
            Dashboard
        </h1>
        <p class="lead">Odoo Module Analysis & Version Migration Platform</p>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <!-- Docker Environments Status -->
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fab fa-docker fa-3x text-info"></i>
                </div>
                <h5 class="card-title">Docker Environments</h5>
                <p class="card-text">
                    <span class="badge bg-info">Multi-Version Support</span>
                </p>
                <small class="text-muted">Odoo v13 - v18 Testing</small>
                <div class="mt-3">
                    <a href="{{ url_for('main.docker_environments') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fab fa-docker me-1"></i>Manage
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Statistics -->
    <div class="col-md-8 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-cubes me-2"></i>
                    Module Statistics
                </h5>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-archive fa-2x text-primary"></i>
                        </div>
                        <h4 class="mb-1">{{ total_modules }}</h4>
                        <small class="text-muted">Total Modules</small>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                        <h4 class="mb-1">{{ analyzed_modules }}</h4>
                        <small class="text-muted">Analyzed</small>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                        <h4 class="mb-1">{{ pending_modules }}</h4>
                        <small class="text-muted">Pending</small>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        </div>
                        <h4 class="mb-1">{{ error_modules }}</h4>
                        <small class="text-muted">Errors</small>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>View Analysis
                    </a>
                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-outline-primary">
                        <i class="fas fa-upload me-1"></i>Upload Modules
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- My Active Migrations Widget -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tasks me-2"></i>
                    My Active Migrations
                    <span class="badge bg-primary ms-2" id="active-count">{{ active_migrations|length }}</span>
                </h5>
                <div id="active-migrations-container">
                    {% if active_migrations %}
                        <div class="list-group list-group-flush">
                            {% for job in active_migrations[:5] %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ job.module.name }}</h6>
                                    <p class="mb-1 text-muted small">{{ job.module.description or 'No description' }}</p>
                                    <small class="text-muted">
                                        Status: <span class="badge bg-{{ 'warning' if job.status == 'ANALYSIS' else 'info' if job.status == 'QUEUED' else 'success' if job.status == 'COMPLETED' else 'danger' }}">{{ job.status }}</span>
                                        {% if job.target_version %}| Target: v{{ job.target_version }}{% endif %}
                                    </small>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('main.migration_jobs') }}#job-{{ job.id }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if job.status in ['FAILED', 'ERROR'] %}
                                    <button class="btn btn-outline-warning btn-sm" onclick="rerunWithAI({{ job.id }})">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                            {% if active_migrations|length > 5 %}
                            <div class="list-group-item text-center">
                                <a href="{{ url_for('main.migration_jobs') }}" class="btn btn-outline-primary btn-sm">
                                    View All {{ active_migrations|length }} Active Migrations
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">No active migrations</p>
                            <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>Start New Migration
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Items Needing Attention -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-exclamation-circle me-2 text-warning"></i>
                    Needs Attention
                    <span class="badge bg-warning ms-2" id="attention-count">{{ items_needing_attention|length }}</span>
                </h5>
                <div id="attention-items-container">
                    {% if items_needing_attention %}
                        <div class="list-group list-group-flush">
                            {% for item in items_needing_attention[:3] %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ item.title }}</h6>
                                    <small class="text-danger">{{ item.priority }}</small>
                                </div>
                                <p class="mb-1 small">{{ item.description }}</p>
                                <small class="text-muted">{{ item.created_at.strftime('%Y-%m-%d %H:%M') if item.created_at else 'Recently' }}</small>
                            </div>
                            {% endfor %}
                            {% if items_needing_attention|length > 3 %}
                            <div class="list-group-item text-center">
                                <a href="{{ url_for('main.manual_interventions') }}" class="btn btn-outline-warning btn-sm">
                                    View All {{ items_needing_attention|length }} Items
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted small">All good!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recommended Path & Quick Actions -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-route me-2"></i>
                    Recommended Path
                </h5>
                <div id="recommended-path-container">
                    {% if recommended_path %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-lightbulb fa-2x text-warning"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ recommended_path.title }}</h6>
                                <p class="mb-1">{{ recommended_path.description }}</p>
                                <div class="mt-2">
                                    <a href="{{ recommended_path.action_url }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>{{ recommended_path.action_text }}
                                    </a>
                                    {% if recommended_path.secondary_action %}
                                    <a href="{{ recommended_path.secondary_action.url }}" class="btn btn-outline-secondary btn-sm ms-2">
                                        {{ recommended_path.secondary_action.text }}
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Default recommendations based on current state -->
                        {% if total_modules == 0 %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-upload fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Start by uploading modules</h6>
                                <p class="mb-1">Upload your Odoo modules or sync from GitHub to begin the migration process.</p>
                                <div class="mt-2">
                                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-upload me-1"></i>Upload Modules
                                    </a>
                                    <a href="{{ url_for('main.github_integration') }}" class="btn btn-outline-primary btn-sm ms-2">
                                        <i class="fab fa-github me-1"></i>GitHub Sync
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% elif pending_modules > 0 %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-cogs fa-2x text-warning"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Analyze pending modules</h6>
                                <p class="mb-1">You have {{ pending_modules }} modules waiting for analysis. Start the migration process.</p>
                                <div class="mt-2">
                                    <a href="{{ url_for('main.analyze_all') }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-cogs me-1"></i>Analyze All Pending
                                    </a>
                                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-outline-secondary btn-sm ms-2">
                                        <i class="fas fa-list me-1"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% elif items_needing_attention %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-user-check fa-2x text-info"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Review pending items</h6>
                                <p class="mb-1">{{ items_needing_attention|length }} items need your attention for manual review.</p>
                                <div class="mt-2">
                                    <a href="{{ url_for('main.manual_interventions') }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye me-1"></i>Review Items
                                    </a>
                                    <a href="{{ url_for('main.review_queue') }}" class="btn btn-outline-secondary btn-sm ms-2">
                                        <i class="fas fa-list me-1"></i>Review Queue
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-chart-line fa-2x text-success"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Review your results</h6>
                                <p class="mb-1">Great! Check your migration results and performance analytics.</p>
                                <div class="mt-2">
                                    <a href="{{ url_for('main.migration_results') }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-chart-bar me-1"></i>View Results
                                    </a>
                                    <a href="{{ url_for('main.performance_analytics') }}" class="btn btn-outline-secondary btn-sm ms-2">
                                        <i class="fas fa-analytics me-1"></i>Analytics
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
                <div class="d-grid gap-2">
                    {% if not odoo_installation or odoo_installation.status != 'active' %}
                        <a href="{{ url_for('main.install_odoo') }}" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Install Odoo
                        </a>
                    {% endif %}

                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>Upload Modules
                    </a>

                    {% if pending_modules > 0 %}
                        <a href="{{ url_for('main.analyze_all') }}" class="btn btn-warning">
                            <i class="fas fa-cogs me-1"></i>Analyze All Pending
                        </a>
                    {% endif %}

                    <a href="{{ url_for('main.github_integration') }}" class="btn btn-outline-primary">
                        <i class="fab fa-github me-1"></i>GitHub Sync
                    </a>

                    <button class="btn btn-outline-secondary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh Status
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Modules -->
{% if recent_modules %}
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Uploads
                </h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Module Name</th>
                                <th>Upload Date</th>
                                <th>File Size</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for module in recent_modules %}
                            <tr>
                                <td>
                                    <i class="fas fa-cube me-2 text-primary"></i>
                                    {{ module.name }}
                                </td>
                                <td>{{ module.timestamp.strftime('%Y-%m-%d %H:%M') if module.timestamp }}</td>
                                <td>N/A</td>
                                <td>
                                    {% set analysis_status = 'completed' if module.analyses else 'pending' %}
                                    <span class="badge bg-{{ 'success' if analysis_status == 'completed' else 'warning' }}">
                                        {{ analysis_status.title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('main.module_details', module_id=module.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if not module.analyses %}
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}" class="btn btn-outline-success" title="True Migration System">
                                                <i class="fas fa-robot"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
function refreshStatus() {
    location.reload();
}
</script>
{% endblock %}
