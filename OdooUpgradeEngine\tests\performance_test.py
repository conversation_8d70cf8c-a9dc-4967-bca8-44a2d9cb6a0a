#!/usr/bin/env python3
"""
Performance Test for Odoo Upgrade Engine
Tests system performance and response times
"""

import requests
import time
import statistics
import sys
import threading
from datetime import datetime
from typing import Dict, List, Tuple

class PerformanceTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = []
        
    def measure_response_time(self, url: str, method: str = 'GET', data: dict = None) -> Tuple[float, int, bool]:
        """Measure response time for a request"""
        start_time = time.time()
        try:
            if method == 'GET':
                response = self.session.get(url)
            elif method == 'POST':
                response = self.session.post(url, json=data)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            end_time = time.time()
            response_time = end_time - start_time
            
            return response_time, response.status_code, response.status_code == 200
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            return response_time, 0, False
    
    def test_page_load_performance(self):
        """Test page load performance"""
        print("📊 Testing Page Load Performance")
        
        pages = [
            ('/', 'Dashboard'),
            ('/upload_modules', 'Upload Modules'),
            ('/migration_jobs', 'Migration Jobs'),
            ('/ai_providers', 'AI Providers'),
            ('/ai_learning_dashboard', 'AI Learning Dashboard'),
            ('/health_dashboard', 'Health Dashboard')
        ]
        
        page_results = {}
        
        for path, name in pages:
            url = f"{self.base_url}{path}"
            times = []
            
            # Test each page 5 times
            for i in range(5):
                response_time, status_code, success = self.measure_response_time(url)
                times.append(response_time)
                time.sleep(0.1)  # Small delay between requests
            
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            page_results[name] = {
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'times': times
            }
            
            status = "✅" if avg_time < 2.0 else "⚠️" if avg_time < 5.0 else "❌"
            print(f"  {status} {name}: {avg_time:.3f}s avg (min: {min_time:.3f}s, max: {max_time:.3f}s)")
        
        return page_results
    
    def test_api_performance(self):
        """Test API endpoint performance"""
        print("\n🔌 Testing API Performance")
        
        apis = [
            ('/api/dashboard-data', 'GET', None, 'Dashboard Data'),
            ('/api/migration-status', 'GET', None, 'Migration Status'),
            ('/api/migration-jobs', 'GET', None, 'Migration Jobs'),
            ('/api/ai-learning-insights', 'GET', None, 'AI Learning Insights')
        ]
        
        api_results = {}
        
        for path, method, data, name in apis:
            url = f"{self.base_url}{path}"
            times = []
            
            # Test each API 10 times
            for i in range(10):
                response_time, status_code, success = self.measure_response_time(url, method, data)
                if success:
                    times.append(response_time)
                time.sleep(0.05)  # Small delay between requests
            
            if times:
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                
                api_results[name] = {
                    'avg_time': avg_time,
                    'min_time': min_time,
                    'max_time': max_time,
                    'success_rate': len(times) / 10
                }
                
                status = "✅" if avg_time < 1.0 else "⚠️" if avg_time < 3.0 else "❌"
                print(f"  {status} {name}: {avg_time:.3f}s avg (success: {len(times)}/10)")
            else:
                api_results[name] = {'avg_time': 0, 'success_rate': 0}
                print(f"  ❌ {name}: Failed all requests")
        
        return api_results
    
    def test_concurrent_load(self, num_threads: int = 5, requests_per_thread: int = 10):
        """Test concurrent load handling"""
        print(f"\n⚡ Testing Concurrent Load ({num_threads} threads, {requests_per_thread} requests each)")
        
        results = []
        threads = []
        
        def worker_thread(thread_id: int):
            thread_results = []
            for i in range(requests_per_thread):
                start_time = time.time()
                try:
                    response = self.session.get(f"{self.base_url}/")
                    end_time = time.time()
                    response_time = end_time - start_time
                    thread_results.append({
                        'thread_id': thread_id,
                        'request_id': i,
                        'response_time': response_time,
                        'success': response.status_code == 200
                    })
                except Exception as e:
                    end_time = time.time()
                    response_time = end_time - start_time
                    thread_results.append({
                        'thread_id': thread_id,
                        'request_id': i,
                        'response_time': response_time,
                        'success': False
                    })
            results.extend(thread_results)
        
        # Start all threads
        start_time = time.time()
        for i in range(num_threads):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        success_rate = len(successful_requests) / len(results)
        requests_per_second = len(results) / total_time
        
        print(f"  📈 Total requests: {len(results)}")
        print(f"  ✅ Successful: {len(successful_requests)} ({success_rate:.1%})")
        print(f"  ❌ Failed: {len(failed_requests)}")
        print(f"  ⏱️  Average response time: {avg_response_time:.3f}s")
        print(f"  🚀 Requests per second: {requests_per_second:.2f}")
        
        return {
            'total_requests': len(results),
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time,
            'requests_per_second': requests_per_second,
            'total_time': total_time
        }
    
    def test_memory_usage_simulation(self):
        """Simulate memory usage with multiple requests"""
        print("\n🧠 Testing Memory Usage Simulation")
        
        # Make many requests to different endpoints to simulate memory usage
        endpoints = [
            '/',
            '/migration_jobs',
            '/ai_providers',
            '/api/dashboard-data',
            '/api/migration-status'
        ]
        
        total_requests = 50
        successful_requests = 0
        total_time = 0
        
        for i in range(total_requests):
            endpoint = endpoints[i % len(endpoints)]
            url = f"{self.base_url}{endpoint}"
            
            response_time, status_code, success = self.measure_response_time(url)
            total_time += response_time
            
            if success:
                successful_requests += 1
            
            # Small delay to prevent overwhelming the server
            time.sleep(0.01)
        
        avg_response_time = total_time / total_requests
        success_rate = successful_requests / total_requests
        
        print(f"  📊 Total requests: {total_requests}")
        print(f"  ✅ Success rate: {success_rate:.1%}")
        print(f"  ⏱️  Average response time: {avg_response_time:.3f}s")
        
        status = "✅" if success_rate > 0.95 and avg_response_time < 2.0 else "⚠️" if success_rate > 0.8 else "❌"
        print(f"  {status} Memory usage simulation: {'PASSED' if status == '✅' else 'NEEDS ATTENTION' if status == '⚠️' else 'FAILED'}")
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time
        }
    
    def run_performance_tests(self):
        """Run all performance tests"""
        print("🚀 Starting Performance Tests")
        print("=" * 50)
        
        # Test basic connectivity first
        response_time, status_code, success = self.measure_response_time(f"{self.base_url}/")
        if not success:
            print("❌ Cannot connect to server. Please ensure the application is running.")
            return False
        
        print(f"✅ Server connectivity: {response_time:.3f}s")
        
        # Run performance tests
        page_results = self.test_page_load_performance()
        api_results = self.test_api_performance()
        concurrent_results = self.test_concurrent_load()
        memory_results = self.test_memory_usage_simulation()
        
        # Generate performance summary
        self.generate_performance_summary(page_results, api_results, concurrent_results, memory_results)
        
        return True
    
    def generate_performance_summary(self, page_results, api_results, concurrent_results, memory_results):
        """Generate performance test summary"""
        print("\n" + "=" * 50)
        print("📊 PERFORMANCE TEST SUMMARY")
        print("=" * 50)
        
        # Page load performance assessment
        page_times = [result['avg_time'] for result in page_results.values()]
        avg_page_load = statistics.mean(page_times) if page_times else 0
        
        print(f"📄 Average page load time: {avg_page_load:.3f}s")
        
        # API performance assessment
        api_times = [result['avg_time'] for result in api_results.values() if result['avg_time'] > 0]
        avg_api_time = statistics.mean(api_times) if api_times else 0
        
        print(f"🔌 Average API response time: {avg_api_time:.3f}s")
        
        # Concurrent load assessment
        print(f"⚡ Concurrent load success rate: {concurrent_results['success_rate']:.1%}")
        print(f"🚀 Requests per second: {concurrent_results['requests_per_second']:.2f}")
        
        # Memory simulation assessment
        print(f"🧠 Memory simulation success rate: {memory_results['success_rate']:.1%}")
        
        # Overall performance assessment
        performance_score = 0
        
        # Page load score (40% weight)
        if avg_page_load < 1.0:
            performance_score += 40
        elif avg_page_load < 2.0:
            performance_score += 30
        elif avg_page_load < 5.0:
            performance_score += 20
        else:
            performance_score += 10
        
        # API performance score (30% weight)
        if avg_api_time < 0.5:
            performance_score += 30
        elif avg_api_time < 1.0:
            performance_score += 25
        elif avg_api_time < 2.0:
            performance_score += 20
        else:
            performance_score += 10
        
        # Concurrent load score (20% weight)
        if concurrent_results['success_rate'] > 0.95:
            performance_score += 20
        elif concurrent_results['success_rate'] > 0.8:
            performance_score += 15
        elif concurrent_results['success_rate'] > 0.6:
            performance_score += 10
        else:
            performance_score += 5
        
        # Memory simulation score (10% weight)
        if memory_results['success_rate'] > 0.95:
            performance_score += 10
        elif memory_results['success_rate'] > 0.8:
            performance_score += 8
        else:
            performance_score += 5
        
        print(f"\n🎯 Overall Performance Score: {performance_score}/100")
        
        if performance_score >= 90:
            print("🎉 PERFORMANCE: EXCELLENT")
            print("✅ System performs exceptionally well under all conditions")
        elif performance_score >= 75:
            print("👍 PERFORMANCE: GOOD")
            print("✅ System performs well with minor optimization opportunities")
        elif performance_score >= 60:
            print("⚠️  PERFORMANCE: ACCEPTABLE")
            print("🔧 System is functional but could benefit from optimization")
        else:
            print("🚨 PERFORMANCE: NEEDS IMPROVEMENT")
            print("❌ System performance issues detected")
        
        # Save performance report
        self.save_performance_report(page_results, api_results, concurrent_results, memory_results, performance_score)
    
    def save_performance_report(self, page_results, api_results, concurrent_results, memory_results, performance_score):
        """Save performance test report"""
        import json
        
        report = {
            'test_summary': {
                'timestamp': datetime.now().isoformat(),
                'performance_score': performance_score,
                'overall_assessment': 'EXCELLENT' if performance_score >= 90 else 'GOOD' if performance_score >= 75 else 'ACCEPTABLE' if performance_score >= 60 else 'NEEDS IMPROVEMENT'
            },
            'page_performance': page_results,
            'api_performance': api_results,
            'concurrent_load': concurrent_results,
            'memory_simulation': memory_results
        }
        
        with open('performance_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Performance report saved to: performance_test_report.json")

def main():
    """Main performance test execution"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"🎯 Testing Performance at: {base_url}")
    print("⏳ Please ensure the application is running...")
    print()
    
    tester = PerformanceTester(base_url)
    success = tester.run_performance_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
