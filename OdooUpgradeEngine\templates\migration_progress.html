{% extends "base.html" %}

{% block title %}Migration Progress - {{ job.module.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-chart-line me-2"></i>Migration Progress</h2>
                    <p class="text-muted mb-0">{{ job.module.name }} → {{ job.target_version }}</p>
                </div>
                <a href="{{ url_for('main.migration_orchestrator') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tasks me-2"></i>Migration Steps
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-4" style="height: 20px;">
                                <div class="progress-bar bg-primary" role="progressbar" 
                                     style="width: {% if job.status == 'COMPLETED' %}100{% elif job.status == 'FAILED' %}0{% else %}75{% endif %}%">
                                    {% if job.status == 'COMPLETED' %}100{% elif job.status == 'FAILED' %}0{% else %}75{% endif %}%
                                </div>
                            </div>

                            <div class="timeline">
                                <div class="timeline-item completed">
                                    <div class="timeline-marker bg-success">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Module Upload</h6>
                                        <p class="text-muted">Module successfully uploaded and validated</p>
                                    </div>
                                </div>

                                <div class="timeline-item {% if job.status in ['ANALYSIS', 'CODE_TRANSFORMATION', 'VERSION_UPDATE', 'VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}completed{% else %}active{% endif %}">
                                    <div class="timeline-marker {% if job.status in ['ANALYSIS', 'CODE_TRANSFORMATION', 'VERSION_UPDATE', 'VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}bg-success{% else %}bg-primary{% endif %}">
                                        <i class="fas {% if job.status in ['ANALYSIS', 'CODE_TRANSFORMATION', 'VERSION_UPDATE', 'VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}fa-check{% else %}fa-spinner fa-spin{% endif %}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Code Analysis</h6>
                                        <p class="text-muted">Analyzing module structure and dependencies</p>
                                    </div>
                                </div>

                                <div class="timeline-item {% if job.status in ['CODE_TRANSFORMATION', 'VERSION_UPDATE', 'VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}completed{% elif job.status == 'ANALYSIS' %}active{% endif %}">
                                    <div class="timeline-marker {% if job.status in ['CODE_TRANSFORMATION', 'VERSION_UPDATE', 'VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}bg-success{% elif job.status == 'ANALYSIS' %}bg-primary{% else %}bg-secondary{% endif %}">
                                        <i class="fas {% if job.status in ['CODE_TRANSFORMATION', 'VERSION_UPDATE', 'VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}fa-check{% elif job.status == 'ANALYSIS' %}fa-spinner fa-spin{% else %}fa-clock{% endif %}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Code Transformation</h6>
                                        <p class="text-muted">Upgrading code to target version</p>
                                    </div>
                                </div>

                                <div class="timeline-item {% if job.status in ['VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}completed{% elif job.status in ['CODE_TRANSFORMATION', 'VERSION_UPDATE'] %}active{% endif %}">
                                    <div class="timeline-marker {% if job.status in ['VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}bg-success{% elif job.status in ['CODE_TRANSFORMATION', 'VERSION_UPDATE'] %}bg-primary{% else %}bg-secondary{% endif %}">
                                        <i class="fas {% if job.status in ['VISUAL_DIFF', 'AWAITING_APPROVAL', 'DIFF_APPROVED', 'DB_MIGRATION', 'TESTING', 'COMPLETED'] %}fa-check{% elif job.status in ['CODE_TRANSFORMATION', 'VERSION_UPDATE'] %}fa-spinner fa-spin{% else %}fa-clock{% endif %}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Review & Approval</h6>
                                        <p class="text-muted">Generating diff and awaiting approval</p>
                                    </div>
                                </div>

                                <div class="timeline-item {% if job.status in ['DB_MIGRATION', 'TESTING', 'COMPLETED'] %}completed{% elif job.status == 'DIFF_APPROVED' %}active{% endif %}">
                                    <div class="timeline-marker {% if job.status in ['DB_MIGRATION', 'TESTING', 'COMPLETED'] %}bg-success{% elif job.status == 'DIFF_APPROVED' %}bg-primary{% else %}bg-secondary{% endif %}">
                                        <i class="fas {% if job.status in ['DB_MIGRATION', 'TESTING', 'COMPLETED'] %}fa-check{% elif job.status == 'DIFF_APPROVED' %}fa-spinner fa-spin{% else %}fa-clock{% endif %}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Database Migration</h6>
                                        <p class="text-muted">Updating database schema and data</p>
                                    </div>
                                </div>

                                <div class="timeline-item {% if job.status == 'COMPLETED' %}completed{% elif job.status in ['TESTING'] %}active{% endif %}">
                                    <div class="timeline-marker {% if job.status == 'COMPLETED' %}bg-success{% elif job.status in ['TESTING'] %}bg-primary{% else %}bg-secondary{% endif %}">
                                        <i class="fas {% if job.status == 'COMPLETED' %}fa-check{% elif job.status in ['TESTING'] %}fa-spinner fa-spin{% else %}fa-clock{% endif %}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Testing & Validation</h6>
                                        <p class="text-muted">Running tests and final validation</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Migration Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Module:</strong><br>
                                <span class="text-muted">{{ job.module.name }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Current Version:</strong><br>
                                <span class="badge bg-secondary">{{ job.module.version }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Target Version:</strong><br>
                                <span class="badge bg-primary">{{ job.target_version }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                <span class="badge {% if job.status == 'COMPLETED' %}bg-success{% elif job.status == 'FAILED' %}bg-danger{% else %}bg-warning text-dark{% endif %}">
                                    {{ job.status.replace('_', ' ').title() }}
                                </span>
                            </div>
                            <div class="mb-3">
                                <strong>Started:</strong><br>
                                <span class="text-muted">{{ job.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </div>
                        </div>
                    </div>

                    {% if job.status == 'AWAITING_APPROVAL' %}
                    <div class="card mt-3">
                        <div class="card-header bg-warning">
                            <h5 class="card-title mb-0 text-dark">
                                <i class="fas fa-exclamation-triangle me-2"></i>Action Required
                            </h5>
                        </div>
                        <div class="card-body">
                            <p>This migration is awaiting your approval.</p>
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('main.view_diff', job_id=job.id) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>Review Changes
                                </a>
                                <button class="btn btn-success" onclick="approveMigration({{ job.id }})">
                                    <i class="fas fa-check me-1"></i>Approve Migration
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-content p {
    margin-bottom: 0;
    font-size: 14px;
}
</style>

<script>
function approveMigration(jobId) {
    if (confirm('Are you sure you want to approve this migration?')) {
        fetch(`/migration/${jobId}/approve`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Migration approved successfully!');
                location.reload();
            } else {
                alert('Error approving migration: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving migration: ' + error.message);
        });
    }
}
</script>
{% endblock %}
