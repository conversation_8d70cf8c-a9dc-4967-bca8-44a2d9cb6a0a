#!/usr/bin/env python3
"""
Quick test to verify our critical fixes are working
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dependency_resolver_fix():
    """Test that dependency resolver handles string parameters correctly"""
    print("Testing dependency resolver fix...")
    
    try:
        from dependency_resolver import DependencyResolver
        
        resolver = DependencyResolver()
        
        # Test 1: Non-existent path should handle gracefully
        result = resolver.analyze_module_dependencies("/non/existent/path")
        assert result['analysis_status'] == 'error', f"Expected error status, got {result['analysis_status']}"
        print("✅ Test 1 passed: Non-existent path handled gracefully")
        
        # Test 2: Current directory should return success
        result = resolver.analyze_module_dependencies(".")
        assert result['analysis_status'] == 'success', f"Expected success status, got {result['analysis_status']}"
        assert 'total_files' in result, "Expected total_files in result"
        print(f"✅ Test 2 passed: Current directory analyzed successfully (found {result['total_files']} files)")
        
        # Test 3: List parameter should still work (backward compatibility)
        result = resolver.analyze_module_dependencies([])
        assert result['analysis_status'] == 'success', f"Expected success status, got {result['analysis_status']}"
        print("✅ Test 3 passed: List parameter still works")
        
        return True
        
    except Exception as e:
        print(f"❌ Dependency resolver test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_orchestrator_result_structure():
    """Test that orchestrator returns expected result structure"""
    print("\nTesting orchestrator result structure...")
    
    try:
        from true_migration_orchestrator import TrueMigrationOrchestrator
        
        orchestrator = TrueMigrationOrchestrator()
        
        # Test the _finalize_migration method directly with a mock job
        class MockJob:
            def __init__(self):
                self.id = "test-job-123"
                self.source_version = "15.0"
                self.target_version = "17.0"
                self.files = []
                self.transformation_results = {
                    'rules_application': {'total_rules_applied': 5, 'files_modified': 3},
                    'python_transformations': {'total_transformations_applied': 2}
                }
                self.visual_diff_data = {'diff_report_path': '/path/to/diff.html'}
                self.status = 'SUCCESS'
                self.started_at = None
                self.completed_at = None
                
            def update_status(self, status, phase=None, progress=None):
                self.status = status
                
        mock_job = MockJob()
        
        # Test finalize_migration
        result = orchestrator._finalize_migration(mock_job)
        
        # Check all expected fields are present
        expected_fields = [
            'migration_id', 'success', 'status', 'execution_time',
            'visual_diff_path', 'transformation_summary', 'phases',
            'target_version', 'source_version', 'job_id', 'completed_at'
        ]
        
        for field in expected_fields:
            assert field in result, f"Missing field: {field}"
            
        # Check specific values
        assert result['migration_id'] == "test-job-123"
        assert result['success'] is True
        assert result['target_version'] == "17.0"
        assert result['source_version'] == "15.0"
        assert 'transformation_summary' in result
        assert 'total_files_processed' in result['transformation_summary']
        assert result['transformation_summary']['total_rules_applied'] == 5
        
        print("✅ Test passed: Orchestrator returns correct result structure")
        print(f"   - Migration ID: {result['migration_id']}")
        print(f"   - Success: {result['success']}")
        print(f"   - Rules applied: {result['transformation_summary']['total_rules_applied']}")
        print(f"   - Visual diff path: {result['visual_diff_path']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test that all critical imports work"""
    print("\nTesting critical imports...")
    
    try:
        # Test all the core modules can be imported
        from dependency_resolver import DependencyResolver
        from true_migration_orchestrator import TrueMigrationOrchestrator, MigrationRequest
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("QUICK FIX VERIFICATION TEST")
    print("=" * 60)
    
    test_results = []
    
    # Test imports first
    test_results.append(test_imports())
    
    # Test dependency resolver fix
    test_results.append(test_dependency_resolver_fix())
    
    # Test orchestrator result structure
    test_results.append(test_orchestrator_result_structure())
    
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED ({passed}/{total})")
        print("✅ Critical fixes are working correctly!")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("❌ There are still issues to resolve")
        
    print("=" * 60)