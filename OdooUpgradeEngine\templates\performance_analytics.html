{% extends "base.html" %}
{% set title = "Performance Analytics" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-analytics me-3"></i>
            Performance Analytics
        </h1>
        <p class="lead">Detailed performance metrics and migration analytics</p>
    </div>
</div>

<!-- Performance Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-stopwatch text-primary fa-2x mb-2"></i>
                <h5 class="card-title">Avg Migration Time</h5>
                <h3 class="text-primary">2h 30m</h3>
                <small class="text-muted">Per migration</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-memory text-success fa-2x mb-2"></i>
                <h5 class="card-title">Memory Usage</h5>
                <h3 class="text-success">2.1 GB</h3>
                <small class="text-muted">Peak usage</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-microchip text-info fa-2x mb-2"></i>
                <h5 class="card-title">CPU Usage</h5>
                <h3 class="text-info">65%</h3>
                <small class="text-muted">Average load</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-database text-warning fa-2x mb-2"></i>
                <h5 class="card-title">DB Operations</h5>
                <h3 class="text-warning">1,247</h3>
                <small class="text-muted">Per migration</small>
            </div>
        </div>
    </div>
</div>

<!-- Performance Charts -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    Migration Duration Trends
                </h5>
            </div>
            <div class="card-body">
                <canvas id="durationChart" width="400" height="250"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Resource Usage Over Time
                </h5>
            </div>
            <div class="card-body">
                <canvas id="resourceChart" width="400" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Performance Breakdown -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    Migration Phase Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Phase</th>
                                <th>Avg Duration</th>
                                <th>CPU Usage</th>
                                <th>Memory</th>
                                <th>Success Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><i class="fas fa-search text-info"></i> Analysis</td>
                                <td>15 minutes</td>
                                <td>45%</td>
                                <td>512 MB</td>
                                <td><span class="badge bg-success">98%</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-code text-primary"></i> Code Transformation</td>
                                <td>45 minutes</td>
                                <td>75%</td>
                                <td>1.2 GB</td>
                                <td><span class="badge bg-success">92%</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-tag text-warning"></i> Version Update</td>
                                <td>10 minutes</td>
                                <td>30%</td>
                                <td>256 MB</td>
                                <td><span class="badge bg-success">99%</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-shield-alt text-danger"></i> Security Scan</td>
                                <td>20 minutes</td>
                                <td>60%</td>
                                <td>800 MB</td>
                                <td><span class="badge bg-success">95%</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-eye text-success"></i> Visual Diff</td>
                                <td>5 minutes</td>
                                <td>25%</td>
                                <td>128 MB</td>
                                <td><span class="badge bg-success">100%</span></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-vial text-info"></i> Testing</td>
                                <td>55 minutes</td>
                                <td>80%</td>
                                <td>2.1 GB</td>
                                <td><span class="badge bg-warning">85%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    Performance Insights
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-success">
                        <i class="fas fa-check-circle"></i> Optimization Opportunities
                    </h6>
                    <ul class="list-unstyled">
                        <li><small>• Testing phase can be optimized</small></li>
                        <li><small>• Memory usage spikes during transformation</small></li>
                        <li><small>• Parallel processing potential</small></li>
                    </ul>
                </div>
                <div class="mb-3">
                    <h6 class="text-info">
                        <i class="fas fa-lightbulb"></i> Recommendations
                    </h6>
                    <ul class="list-unstyled">
                        <li><small>• Implement caching for analysis phase</small></li>
                        <li><small>• Use incremental testing approach</small></li>
                        <li><small>• Consider memory optimization</small></li>
                    </ul>
                </div>
                <div>
                    <h6 class="text-warning">
                        <i class="fas fa-exclamation-triangle"></i> Bottlenecks
                    </h6>
                    <ul class="list-unstyled">
                        <li><small>• Testing phase duration</small></li>
                        <li><small>• Memory allocation spikes</small></li>
                        <li><small>• Database query optimization</small></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Performance Data -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>
            Recent Migration Performance
        </h5>
    </div>
    <div class="card-body">
        {% if recent_jobs %}
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead>
                        <tr>
                            <th>Module</th>
                            <th>Version</th>
                            <th>Duration</th>
                            <th>CPU Peak</th>
                            <th>Memory Peak</th>
                            <th>Status</th>
                            <th>Performance Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in recent_jobs[:10] %}
                        <tr>
                            <td><strong>{{ job.module.name }}</strong></td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                →
                                <span class="badge bg-success">{{ job.target_version }}</span>
                            </td>
                            <td>2h 15m</td>
                            <td>72%</td>
                            <td>1.8 GB</td>
                            <td>
                                {% if job.status == 'COMPLETED' %}
                                    <span class="badge bg-success">{{ job.status }}</span>
                                {% elif job.status == 'FAILED' %}
                                    <span class="badge bg-danger">{{ job.status }}</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ job.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 85%">85%</div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No performance data available</h5>
                <p class="text-muted">Performance metrics will appear after migrations are completed</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Placeholder for chart initialization
console.log('Performance analytics page loaded');

document.addEventListener('DOMContentLoaded', function() {
    // Duration Chart placeholder
    const durationCanvas = document.getElementById('durationChart');
    if (durationCanvas) {
        const ctx = durationCanvas.getContext('2d');
        ctx.strokeStyle = '#007bff';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(20, 200);
        ctx.lineTo(80, 150);
        ctx.lineTo(140, 120);
        ctx.lineTo(200, 100);
        ctx.lineTo(260, 90);
        ctx.lineTo(320, 85);
        ctx.stroke();
        
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.fillText('Duration trends chart - Phase 4 implementation', 50, 230);
    }
    
    // Resource Chart placeholder
    const resourceCanvas = document.getElementById('resourceChart');
    if (resourceCanvas) {
        const ctx = resourceCanvas.getContext('2d');
        // CPU line
        ctx.strokeStyle = '#28a745';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(20, 180);
        ctx.lineTo(100, 160);
        ctx.lineTo(200, 140);
        ctx.lineTo(300, 130);
        ctx.stroke();
        
        // Memory line
        ctx.strokeStyle = '#dc3545';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(20, 200);
        ctx.lineTo(100, 190);
        ctx.lineTo(200, 170);
        ctx.lineTo(300, 160);
        ctx.stroke();
        
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.fillText('Resource usage chart - Phase 4 implementation', 50, 230);
    }
});
</script>
{% endblock %}
