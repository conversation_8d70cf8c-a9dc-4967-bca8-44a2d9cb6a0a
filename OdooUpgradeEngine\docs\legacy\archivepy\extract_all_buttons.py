#!/usr/bin/env python3
"""
Extract all buttons, forms, and links from HTML templates for comprehensive testing
"""
import os
import re
from collections import defaultdict

def extract_buttons_from_template(filepath):
    """Extract all interactive elements from an HTML template"""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    buttons = []
    
    # Extract button elements
    button_pattern = r'<button[^>]*>([^<]+)</button>'
    for match in re.finditer(button_pattern, content, re.DOTALL):
        full_tag = match.group(0)
        text = match.group(1).strip()
        buttons.append({
            'type': 'button',
            'text': text,
            'full_tag': full_tag,
            'line': content[:match.start()].count('\n') + 1
        })
    
    # Extract submit buttons in forms
    input_submit_pattern = r'<input[^>]*type=["\']submit["\'][^>]*>'
    for match in re.finditer(input_submit_pattern, content):
        full_tag = match.group(0)
        value_match = re.search(r'value=["\']([^"\']+)["\']', full_tag)
        text = value_match.group(1) if value_match else 'Submit'
        buttons.append({
            'type': 'input_submit',
            'text': text,
            'full_tag': full_tag,
            'line': content[:match.start()].count('\n') + 1
        })
    
    # Extract form actions
    form_pattern = r'<form[^>]*action=["\']([^"\']+)["\'][^>]*>'
    for match in re.finditer(form_pattern, content):
        full_tag = match.group(0)
        action = match.group(1)
        buttons.append({
            'type': 'form',
            'text': f'Form action: {action}',
            'full_tag': full_tag,
            'line': content[:match.start()].count('\n') + 1,
            'action': action
        })
    
    # Extract links with btn class
    link_btn_pattern = r'<a[^>]*class=["\'][^"\']*btn[^"\']*["\'][^>]*>([^<]+)</a>'
    for match in re.finditer(link_btn_pattern, content, re.DOTALL):
        full_tag = match.group(0)
        text = match.group(1).strip()
        href_match = re.search(r'href=["\']([^"\']+)["\']', full_tag)
        href = href_match.group(1) if href_match else '#'
        buttons.append({
            'type': 'link_button',
            'text': text,
            'full_tag': full_tag,
            'line': content[:match.start()].count('\n') + 1,
            'href': href
        })
    
    # Extract dropdown items
    dropdown_pattern = r'<li><[^>]*class=["\'][^"\']*dropdown-item[^"\']*["\'][^>]*>([^<]+)</[^>]*></li>'
    for match in re.finditer(dropdown_pattern, content, re.DOTALL):
        full_tag = match.group(0)
        text = match.group(1).strip()
        buttons.append({
            'type': 'dropdown_item',
            'text': text,
            'full_tag': full_tag,
            'line': content[:match.start()].count('\n') + 1
        })
    
    return buttons

def main():
    templates_dir = 'templates'
    all_buttons = defaultdict(list)
    
    # Get all HTML files
    template_files = []
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                template_files.append(os.path.join(root, file))
    
    print(f"# COMPLETE BUTTON EXTRACTION REPORT")
    print(f"## Found {len(template_files)} template files")
    print()
    
    total_buttons = 0
    
    for template_file in sorted(template_files):
        print(f"### {template_file}")
        buttons = extract_buttons_from_template(template_file)
        all_buttons[template_file] = buttons
        total_buttons += len(buttons)
        
        if buttons:
            for i, btn in enumerate(buttons, 1):
                print(f"{i}. **{btn['type'].upper()}** (Line {btn['line']}): `{btn['text']}`")
                if btn['type'] == 'form':
                    print(f"   - Action: `{btn['action']}`")
                elif btn['type'] == 'link_button':
                    print(f"   - Href: `{btn['href']}`")
                print()
        else:
            print("No interactive elements found.")
            print()
    
    print(f"## SUMMARY")
    print(f"- **Total templates**: {len(template_files)}")
    print(f"- **Total interactive elements**: {total_buttons}")
    
    # Group by type
    type_counts = defaultdict(int)
    for template_buttons in all_buttons.values():
        for btn in template_buttons:
            type_counts[btn['type']] += 1
    
    print(f"\n### By Type:")
    for btn_type, count in sorted(type_counts.items()):
        print(f"- **{btn_type}**: {count}")

if __name__ == '__main__':
    main()