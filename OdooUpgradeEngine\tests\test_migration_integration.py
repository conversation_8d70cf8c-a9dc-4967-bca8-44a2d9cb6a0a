#!/usr/bin/env python3
"""
Integration test for the True Migration System
Tests the complete workflow from module upload to migration completion
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import UploadedModule, ModuleAnalysis, MigrationJob, MigrationStatus
from true_migration_orchestrator import TrueMigrationOrchestrator, MigrationRequest

def create_test_module():
    """Create a test Odoo module for migration testing"""
    test_dir = tempfile.mkdtemp()
    module_path = Path(test_dir) / "test_module"
    module_path.mkdir()
    
    # Create manifest file
    manifest_content = """{
    'name': 'Test Module',
    'version': '********.0',
    'depends': ['base'],
    'installable': True,
    'auto_install': False,
}"""
    (module_path / "__manifest__.py").write_text(manifest_content)
    
    # Create a simple Python file
    python_content = """from odoo import models, fields

class TestModel(models.Model):
    _name = 'test.model'
    _description = 'Test Model'
    
    name = fields.Char('Name')
"""
    (module_path / "models.py").write_text(python_content)
    
    return str(module_path)

def test_migration_workflow():
    """Test the complete migration workflow"""
    print("Testing True Migration System Integration...")
    
    with app.app_context():
        # Create test module
        test_module_path = create_test_module()
        print(f"Created test module at: {test_module_path}")
        
        try:
            # Create UploadedModule entry
            uploaded_module = UploadedModule(
                name="test_module",
                file_path=test_module_path,
                file_size=1024,
                status="uploaded"
            )
            db.session.add(uploaded_module)
            db.session.commit()
            print(f"Created UploadedModule with ID: {uploaded_module.id}")
            
            # Create ModuleAnalysis entry
            analysis = ModuleAnalysis(
                module_id=uploaded_module.id,
                target_version="17.0",
                compatibility_score=75.0,
                analysis_data={}
            )
            db.session.add(analysis)
            db.session.commit()
            print(f"Created ModuleAnalysis with ID: {analysis.id}")
            
            # Create migration request
            migration_request = MigrationRequest(
                module_id=uploaded_module.id,
                source_version="15.0",
                target_version="17.0",
                options={"include_database": False, "dry_run": False}
            )
            
            # Initialize orchestrator
            orchestrator = TrueMigrationOrchestrator()
            print("Initialized TrueMigrationOrchestrator")
            
            # Test dependency resolver fix
            print("\n1. Testing dependency resolver...")
            dependency_result = orchestrator.dependency_resolver.analyze_module_dependencies(test_module_path)
            print(f"   Dependency analysis status: {dependency_result.get('analysis_status', 'unknown')}")
            print(f"   Total files found: {dependency_result.get('total_files', 0)}")
            
            # Create migration job
            print("\n2. Creating migration job...")
            job_id = orchestrator.create_migration_job(migration_request)
            print(f"   Created migration job: {job_id}")
            
            # Execute migration
            print("\n3. Executing migration workflow...")
            try:
                migration_result = orchestrator.continue_migration(job_id)
                print(f"   Migration completed successfully!")
                print(f"   Migration ID: {migration_result.get('migration_id')}")
                print(f"   Success: {migration_result.get('success')}")
                print(f"   Execution time: {migration_result.get('execution_time', 0):.2f}s")
                
                # Check transformation summary
                transform_summary = migration_result.get('transformation_summary', {})
                print(f"   Files processed: {transform_summary.get('total_files_processed', 0)}")
                print(f"   Rules applied: {transform_summary.get('total_rules_applied', 0)}")
                
                # Check if visual diff was generated
                if migration_result.get('visual_diff_path'):
                    print(f"   Visual diff generated: {migration_result.get('visual_diff_path')}")
                else:
                    print("   No visual diff generated")
                
                print("\n✅ Migration workflow completed successfully!")
                return True
                
            except Exception as e:
                print(f"\n❌ Migration workflow failed: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            # Cleanup
            if os.path.exists(test_module_path):
                shutil.rmtree(os.path.dirname(test_module_path))
                print(f"Cleaned up test module: {test_module_path}")

def test_individual_components():
    """Test individual components that were fixed"""
    print("\n\nTesting Individual Components...")
    
    with app.app_context():
        # Test dependency resolver with string parameter
        from dependency_resolver import DependencyResolver
        
        print("\n1. Testing DependencyResolver with string parameter...")
        resolver = DependencyResolver()
        
        # Test with non-existent path (should handle gracefully)
        result = resolver.analyze_module_dependencies("/non/existent/path")
        print(f"   Non-existent path result: {result.get('analysis_status', 'unknown')}")
        
        # Test with current directory
        result = resolver.analyze_module_dependencies(".")
        print(f"   Current directory result: {result.get('analysis_status', 'unknown')}")
        print(f"   Files found: {result.get('total_files', 0)}")
        
        print("\n✅ Individual component tests completed!")

if __name__ == "__main__":
    print("=" * 60)
    print("TRUE MIGRATION SYSTEM INTEGRATION TEST")
    print("=" * 60)
    
    # Test individual components first
    test_individual_components()
    
    # Test complete workflow
    workflow_success = test_migration_workflow()
    
    print("\n" + "=" * 60)
    if workflow_success:
        print("🎉 ALL TESTS PASSED - Migration system is working correctly!")
    else:
        print("❌ TESTS FAILED - Check error messages above")
    print("=" * 60)