"""
Professional Dependency Resolver - Circular Dependency Detection

This module provides robust dependency resolution with explicit circular
dependency detection, addressing the critical production risk identified
in the review.
"""

import logging
from typing import Dict, List, Set, Tuple, Optional, Any
from pathlib import Path
import json

class CircularDependencyError(Exception):
    """Raised when circular dependencies are detected."""
    pass

class DependencyResolver:
    """
    Professional dependency resolver with explicit circular dependency detection.
    
    This replaces the dangerous "group remaining modules" approach with
    proper error handling and user notification.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.dependency_graph = {}
        self.circular_dependencies = []
    
    def analyze_module_dependencies(self, modules_info) -> Dict[str, Any]:
        """
        Analyze module dependencies - accepts either a file path or list of modules.
        
        Args:
            modules_info: Either a file path (str) or list of module dictionaries
            
        Returns:
            Comprehensive dependency analysis
        """
        # Handle single module path (compatibility with orchestrator)
        if isinstance(modules_info, str):
            return self._analyze_single_module(modules_info)
        
        # Handle list of modules (original functionality)
        return self._analyze_multiple_modules(modules_info)
    
    def _analyze_single_module(self, module_path: str) -> Dict[str, Any]:
        """Analyze a single module for basic file structure"""
        try:
            from pathlib import Path
            module_path = Path(module_path)
            
            analysis = {
                'module_path': str(module_path),
                'python_files': [],
                'xml_files': [],
                'manifest_files': [],
                'static_files': [],
                'total_files': 0,
                'analysis_status': 'success'
            }
            
            if module_path.exists():
                # Find Python files
                analysis['python_files'] = [str(f) for f in module_path.glob('**/*.py')]
                
                # Find XML files
                analysis['xml_files'] = [str(f) for f in module_path.glob('**/*.xml')]
                
                # Find manifest files
                manifest_files = list(module_path.glob('**/__manifest__.py')) + list(module_path.glob('**/__openerp__.py'))
                analysis['manifest_files'] = [str(f) for f in manifest_files]
                
                # Find static files
                static_files = []
                for ext in ['*.js', '*.css', '*.scss', '*.less']:
                    static_files.extend(module_path.glob(f'**/{ext}'))
                analysis['static_files'] = [str(f) for f in static_files]
                
                analysis['total_files'] = len(analysis['python_files']) + len(analysis['xml_files']) + len(analysis['static_files'])
                
                self.logger.info(f"Single module analysis complete: {analysis['total_files']} files found")
            else:
                analysis['analysis_status'] = 'error'
                analysis['error_message'] = f"Module path not found: {module_path}"
                
            return analysis
            
        except Exception as e:
            self.logger.error(f"Dependency analysis failed: {str(e)}")
            return {
                'analysis_status': 'error',
                'error_message': str(e),
                'python_files': [],
                'xml_files': [],
                'manifest_files': [],
                'static_files': [],
                'total_files': 0
            }
    
    def _analyze_multiple_modules(self, modules_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze module dependencies and detect circular references.
        
        Args:
            modules_info: List of module information dictionaries
            
        Returns:
            Comprehensive dependency analysis
        """
        analysis = {
            'total_modules': len(modules_info),
            'dependency_graph': {},
            'migration_phases': [],
            'circular_dependencies': [],
            'orphaned_modules': [],
            'critical_path': [],
            'analysis_status': 'success'
        }
        
        try:
            # Build dependency graph
            self._build_dependency_graph(modules_info)
            analysis['dependency_graph'] = self.dependency_graph.copy()
            
            # Detect circular dependencies
            circular_deps = self._detect_circular_dependencies()
            
            if circular_deps:
                analysis['circular_dependencies'] = circular_deps
                analysis['analysis_status'] = 'blocked_circular_dependencies'
                analysis['error_message'] = self._format_circular_dependency_error(circular_deps)
                
                # Do NOT proceed with migration planning if circular dependencies exist
                self.logger.error(f"Circular dependencies detected: {len(circular_deps)} cycles")
                return analysis
            
            # Calculate migration phases using topological sort
            phases = self._calculate_migration_phases()
            analysis['migration_phases'] = phases
            
            # Identify orphaned modules
            analysis['orphaned_modules'] = self._find_orphaned_modules()
            
            # Calculate critical path
            analysis['critical_path'] = self._calculate_critical_path()
            
            self.logger.info(f"Dependency analysis complete: {len(phases)} phases, "
                           f"{analysis['total_modules']} modules")
            
        except Exception as e:
            self.logger.error(f"Dependency analysis failed: {str(e)}")
            analysis['analysis_status'] = 'error'
            analysis['error_message'] = str(e)
        
        return analysis
    
    def _build_dependency_graph(self, modules_info: List[Dict[str, Any]]):
        """Build dependency graph from module information."""
        self.dependency_graph = {}
        
        for module in modules_info:
            module_name = module.get('name', 'unknown')
            dependencies = module.get('depends', [])
            
            # Filter out standard Odoo modules and non-existent dependencies
            filtered_deps = []
            module_names = {m.get('name') for m in modules_info}
            
            for dep in dependencies:
                if dep in module_names and dep != module_name:
                    filtered_deps.append(dep)
            
            self.dependency_graph[module_name] = filtered_deps
        
        self.logger.info(f"Built dependency graph with {len(self.dependency_graph)} modules")
    
    def _detect_circular_dependencies(self) -> List[List[str]]:
        """
        Detect circular dependencies using depth-first search.
        
        Returns:
            List of circular dependency cycles
        """
        visited = set()
        rec_stack = set()
        cycles = []
        
        def dfs(node: str, path: List[str]) -> bool:
            """DFS helper to detect cycles."""
            if node in rec_stack:
                # Found a cycle - extract the cycle from path
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            rec_stack.add(node)
            
            # Explore dependencies
            for neighbor in self.dependency_graph.get(node, []):
                if dfs(neighbor, path + [neighbor]):
                    # Continue searching for more cycles
                    pass
            
            rec_stack.remove(node)
            return False
        
        # Check each node for cycles
        for module in self.dependency_graph:
            if module not in visited:
                dfs(module, [module])
        
        # Remove duplicate cycles
        unique_cycles = []
        for cycle in cycles:
            # Normalize cycle (start from lexicographically smallest element)
            min_idx = cycle.index(min(cycle[:-1]))  # Exclude last element (duplicate)
            normalized = cycle[min_idx:-1] + cycle[:min_idx]
            
            if normalized not in unique_cycles:
                unique_cycles.append(normalized)
        
        self.circular_dependencies = unique_cycles
        return unique_cycles
    
    def _calculate_migration_phases(self) -> List[List[str]]:
        """
        Calculate migration phases using topological sort.
        
        This method ONLY works if no circular dependencies exist.
        
        Returns:
            List of migration phases (each phase is a list of modules)
        """
        if self.circular_dependencies:
            raise CircularDependencyError(
                "Cannot calculate migration phases with circular dependencies present"
            )
        
        # Kahn's algorithm for topological sorting
        in_degree = {module: 0 for module in self.dependency_graph}
        
        # Calculate in-degrees
        for module in self.dependency_graph:
            for dependency in self.dependency_graph[module]:
                if dependency in in_degree:
                    in_degree[dependency] += 1
        
        phases = []
        remaining_modules = set(self.dependency_graph.keys())
        
        while remaining_modules:
            # Find modules with no dependencies (in-degree 0)
            current_phase = []
            for module in list(remaining_modules):
                if in_degree[module] == 0:
                    current_phase.append(module)
                    remaining_modules.remove(module)
            
            if not current_phase:
                # This should not happen if circular dependencies were properly detected
                remaining_list = list(remaining_modules)
                raise CircularDependencyError(
                    f"Cannot resolve dependencies for modules: {remaining_list}. "
                    f"Possible undetected circular dependency."
                )
            
            phases.append(sorted(current_phase))
            
            # Update in-degrees for next phase
            for module in current_phase:
                for dependency in self.dependency_graph[module]:
                    if dependency in in_degree:
                        in_degree[dependency] -= 1
        
        return phases
    
    def _find_orphaned_modules(self) -> List[str]:
        """Find modules that have no dependencies and are not dependencies of others."""
        all_dependencies = set()
        for deps in self.dependency_graph.values():
            all_dependencies.update(deps)
        
        orphaned = []
        for module in self.dependency_graph:
            has_dependencies = bool(self.dependency_graph[module])
            is_dependency = module in all_dependencies
            
            if not has_dependencies and not is_dependency:
                orphaned.append(module)
        
        return sorted(orphaned)
    
    def _calculate_critical_path(self) -> List[str]:
        """Calculate critical path (longest dependency chain)."""
        def get_chain_length(module: str, visited: Set[str] = None) -> int:
            if visited is None:
                visited = set()
            
            if module in visited:
                return 0  # Avoid infinite recursion
            
            visited.add(module)
            dependencies = self.dependency_graph.get(module, [])
            
            if not dependencies:
                return 1
            
            max_length = 0
            for dep in dependencies:
                length = get_chain_length(dep, visited.copy())
                max_length = max(max_length, length)
            
            return max_length + 1
        
        # Find module with longest chain
        critical_module = None
        max_chain_length = 0
        
        for module in self.dependency_graph:
            chain_length = get_chain_length(module)
            if chain_length > max_chain_length:
                max_chain_length = chain_length
                critical_module = module
        
        if critical_module:
            return self._get_dependency_chain(critical_module)
        
        return []
    
    def _get_dependency_chain(self, module: str) -> List[str]:
        """Get the full dependency chain for a module."""
        chain = [module]
        current = module
        
        while self.dependency_graph.get(current):
            # Follow the first dependency (could be enhanced to follow longest path)
            next_module = self.dependency_graph[current][0]
            if next_module in chain:  # Avoid cycles
                break
            chain.append(next_module)
            current = next_module
        
        return chain
    
    def _format_circular_dependency_error(self, cycles: List[List[str]]) -> str:
        """Format circular dependency error message for user."""
        error_parts = [
            "🚫 CIRCULAR DEPENDENCY DETECTED",
            "",
            "The following circular dependencies must be resolved before migration can proceed:",
            ""
        ]
        
        for i, cycle in enumerate(cycles, 1):
            cycle_str = " → ".join(cycle + [cycle[0]])
            error_parts.append(f"Cycle {i}: {cycle_str}")
        
        error_parts.extend([
            "",
            "Resolution Options:",
            "1. Remove unnecessary dependencies between these modules",
            "2. Refactor modules to break circular references",
            "3. Merge circularly dependent modules into a single module",
            "",
            "⚠️  Migration cannot proceed until ALL circular dependencies are resolved."
        ])
        
        return "\n".join(error_parts)
    
    def suggest_dependency_fixes(self, cycles: List[List[str]]) -> List[Dict[str, Any]]:
        """
        Suggest specific fixes for circular dependencies.
        
        Args:
            cycles: List of circular dependency cycles
            
        Returns:
            List of suggested fixes
        """
        suggestions = []
        
        for i, cycle in enumerate(cycles):
            suggestion = {
                'cycle_id': i + 1,
                'modules': cycle,
                'severity': 'high' if len(cycle) <= 3 else 'critical',
                'suggested_fixes': []
            }
            
            # Analyze the cycle and suggest fixes
            if len(cycle) == 2:
                # Simple two-module cycle
                suggestion['suggested_fixes'] = [
                    f"Review if {cycle[0]} really needs {cycle[1]} or vice versa",
                    f"Consider merging {cycle[0]} and {cycle[1]} into a single module",
                    f"Extract common functionality into a separate base module"
                ]
            else:
                # Complex multi-module cycle
                suggestion['suggested_fixes'] = [
                    f"Identify the weakest link in the cycle for removal",
                    f"Consider creating a new integration module for {', '.join(cycle)}",
                    f"Refactor shared functionality into abstract base classes"
                ]
            
            suggestions.append(suggestion)
        
        return suggestions

def main():
    """Test the dependency resolver"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python dependency_resolver.py <modules_json_file>")
        print("Or create test data with circular dependencies")
        
        # Create test data with circular dependencies
        test_modules = [
            {'name': 'module_a', 'depends': ['module_b', 'base']},
            {'name': 'module_b', 'depends': ['module_c', 'base']},
            {'name': 'module_c', 'depends': ['module_a', 'base']},  # Creates cycle
            {'name': 'module_d', 'depends': ['base']},
            {'name': 'module_e', 'depends': ['module_d', 'base']},
        ]
        
        resolver = DependencyResolver()
        analysis = resolver.analyze_module_dependencies(test_modules)
        
        print("=== DEPENDENCY ANALYSIS RESULTS ===")
        print(f"Status: {analysis['analysis_status']}")
        print(f"Total modules: {analysis['total_modules']}")
        
        if analysis['circular_dependencies']:
            print(f"\n❌ CIRCULAR DEPENDENCIES DETECTED:")
            print(analysis['error_message'])
            
            suggestions = resolver.suggest_dependency_fixes(analysis['circular_dependencies'])
            print(f"\n💡 SUGGESTED FIXES:")
            for suggestion in suggestions:
                print(f"\nCycle {suggestion['cycle_id']} ({suggestion['severity']} severity):")
                for fix in suggestion['suggested_fixes']:
                    print(f"  • {fix}")
        else:
            print(f"\n✅ NO CIRCULAR DEPENDENCIES")
            print(f"Migration phases: {len(analysis['migration_phases'])}")
            for i, phase in enumerate(analysis['migration_phases'], 1):
                print(f"  Phase {i}: {', '.join(phase)}")
        
        return
    
    # Load modules from JSON file
    modules_file = sys.argv[1]
    with open(modules_file, 'r') as f:
        modules_data = json.load(f)
    
    resolver = DependencyResolver()
    analysis = resolver.analyze_module_dependencies(modules_data)
    
    print(json.dumps(analysis, indent=2))

if __name__ == "__main__":
    main()