#!/usr/bin/env python3
"""
Celery Worker Startup Script for Odoo Upgrade Engine

This script starts the Celery worker for background task processing.
It ensures the worker is properly configured and provides helpful output.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    try:
        import celery
        import redis
        print("✅ Celery and Redis dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install dependencies with: pip install -r requirements.txt")
        return False

def check_redis_connection():
    """Check if Redis is running and accessible"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("Please ensure Redis is running:")
        print("  - On Windows: Download and run Redis from https://github.com/microsoftarchive/redis/releases")
        print("  - On macOS: brew install redis && brew services start redis")
        print("  - On Linux: sudo apt-get install redis-server && sudo systemctl start redis")
        return False

def start_worker():
    """Start the Celery worker"""
    print("🚀 Starting Celery worker for Odoo Upgrade Engine...")
    
    # Change to the project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Set environment variables
    os.environ.setdefault('FLASK_APP', 'app.py')
    
    # Start the worker
    cmd = [
        sys.executable, '-m', 'celery',
        '-A', 'main.celery',
        'worker',
        '--loglevel=info',
        '--pool=solo'  # Use solo pool for Windows compatibility
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print("=" * 60)
    print("🔄 Worker is starting... Press Ctrl+C to stop")
    print("=" * 60)
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Worker stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Worker failed to start: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("🔧 Odoo Upgrade Engine - Celery Worker Startup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check Redis connection
    if not check_redis_connection():
        sys.exit(1)
    
    # Start worker
    if not start_worker():
        sys.exit(1)

if __name__ == '__main__':
    main()
