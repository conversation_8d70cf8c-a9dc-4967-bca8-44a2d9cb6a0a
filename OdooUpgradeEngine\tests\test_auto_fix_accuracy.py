#!/usr/bin/env python3
"""
Test Auto-Fix Accuracy - Verify counts are correct
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from module_fixer import ModuleFixer
import tempfile
import shutil

def test_auto_fix_counts():
    """Test that auto-fix counts are accurate"""
    
    # Create test module with known issues
    test_module_dir = tempfile.mkdtemp()
    
    # Create test Python file with sudo patterns
    test_python_file = os.path.join(test_module_dir, "test_model.py")
    with open(test_python_file, 'w') as f:
        f.write("""
# Test file with sudo patterns
class TestModel:
    def test_method(self):
        # This should be fixed
        records = self.env['res.partner'].sudo().search([])
        # This should also be fixed
        partner = self.env['res.partner'].sudo()
        return records
""")
    
    # Create fixer and directly test the sudo fix method
    fixer = ModuleFixer()
    
    # Test the _fix_sudo_usage method directly
    print("Testing sudo patterns fix directly...")
    fixer._fix_sudo_usage(test_module_dir, "test_model.py")
    
    # Now run the full fix_module method with proper issue format
    result = fixer.fix_module(test_module_dir, {
        'compatibility_issues': ['sudo() detected in test_model.py'],
        'compatibility_warnings': []
    }, target_version="18.0")
    
    print("=== AUTO-FIX ACCURACY TEST ===")
    print(f"Total fixes attempted: {len(result['fixes_applied'])}")
    print(f"Successful fixes: {len(result['success_fixes'])}")
    print(f"Failed fixes: {len(result['error_fixes'])}")
    
    print("\nFixes applied:")
    for fix in result['fixes_applied']:
        print(f"  - {fix}")
    
    print("\nSuccessful fixes:")
    for fix in result['success_fixes']:
        print(f"  ✓ {fix}")
    
    print("\nError fixes:")
    for fix in result['error_fixes']:
        print(f"  ✗ {fix}")
    
    # Verify the fixes were actually applied
    with open(test_python_file, 'r') as f:
        fixed_content = f.read()
    
    print(f"\nFixed content contains 'with_user': {'with_user' in fixed_content}")
    print(f"Fixed content contains 'sudo()': {'sudo()' in fixed_content}")
    
    # Cleanup
    shutil.rmtree(test_module_dir)
    
    # Test results
    success_count = len(result['success_fixes'])
    error_count = len(result['error_fixes'])
    
    print(f"\n=== RESULTS ===")
    print(f"SUCCESS: {success_count} fixes applied successfully")
    print(f"ERRORS: {error_count} fixes failed")
    
    if success_count > 0:
        print("✓ Auto-fix accuracy WORKING - shows correct successful fix count")
    else:
        print("✗ Auto-fix accuracy FAILED - no successful fixes counted")
    
    return success_count > 0

if __name__ == "__main__":
    success = test_auto_fix_counts()
    sys.exit(0 if success else 1)