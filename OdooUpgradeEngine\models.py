# models.py
from extensions import db
from datetime import datetime

class OdooModule(db.Model):
    __tablename__ = 'odoo_module'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), unique=True, nullable=False)
    version = db.Column(db.String(32), nullable=True)
    path = db.Column(db.String(256), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    migration_jobs = db.relationship('MigrationJob', backref='module', lazy=True, cascade="all, delete-orphan")
    analyses = db.relationship('ModuleAnalysis', backref='module', lazy=True, cascade="all, delete-orphan")

class MigrationJob(db.Model):
    __tablename__ = 'migration_job'
    id = db.Column(db.Integer, primary_key=True)
    module_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('odoo_module.id'), nullable=False)
    target_version = db.Column(db.String(32), nullable=False)
    status = db.Column(db.String(64), default='QUEUED', nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    log = db.Column(db.Text, nullable=True)
    upgraded_module_path = db.Column(db.String(256), nullable=True)
    security_report = db.Column(db.Text, nullable=True)
    diff_report = db.relationship('DiffReport', backref='job', uselist=False, lazy=True, cascade="all, delete-orphan")

class DiffReport(db.Model):
    __tablename__ = 'diff_report'
    id = db.Column(db.Integer, primary_key=True)
    migration_job_id = db.Column(db.Integer, db.ForeignKey('migration_job.id'), nullable=False)
    report_path = db.Column(db.String(256), nullable=False)
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

class ModuleAnalysis(db.Model):
    __tablename__ = 'module_analysis'
    id = db.Column(db.Integer, primary_key=True)
    module_id = db.Column(db.Integer, db.ForeignKey('odoo_module.id'), nullable=False)
    report = db.Column(db.JSON, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

class HealthCheck(db.Model):
    __tablename__ = 'health_check'
    id = db.Column(db.Integer, primary_key=True)
    component = db.Column(db.String(128), nullable=False)  # e.g., 'database', 'ai_provider', 'docker'
    status = db.Column(db.String(32), nullable=False)  # 'healthy', 'warning', 'critical'
    message = db.Column(db.Text, nullable=True)
    details = db.Column(db.JSON, nullable=True)  # Additional health check details
    response_time = db.Column(db.Float, nullable=True)  # Response time in seconds
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<HealthCheck {self.component}: {self.status}>'

    @classmethod
    def get_latest_status(cls, component):
        """Get the latest health status for a component"""
        return cls.query.filter_by(component=component).order_by(cls.timestamp.desc()).first()

    @classmethod
    def get_system_health(cls):
        """Get overall system health summary"""
        latest_checks = {}
        components = ['database', 'ai_provider', 'docker', 'storage', 'network']

        for component in components:
            latest = cls.get_latest_status(component)
            if latest:
                latest_checks[component] = {
                    'status': latest.status,
                    'message': latest.message,
                    'timestamp': latest.timestamp,
                    'response_time': latest.response_time
                }
            else:
                latest_checks[component] = {
                    'status': 'unknown',
                    'message': 'No health check data available',
                    'timestamp': None,
                    'response_time': None
                }

        # Determine overall system status
        statuses = [check['status'] for check in latest_checks.values()]
        if 'critical' in statuses:
            overall_status = 'critical'
        elif 'warning' in statuses:
            overall_status = 'warning'
        elif all(status == 'healthy' for status in statuses if status != 'unknown'):
            overall_status = 'healthy'
        else:
            overall_status = 'unknown'

        return {
            'overall_status': overall_status,
            'components': latest_checks,
            'last_updated': max([check['timestamp'] for check in latest_checks.values() if check['timestamp']], default=None)
        }