# --- Python ---
__pycache__/
*.py[cod]
*$py.class
*.so

# --- Distribution / packaging ---
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# --- Python virtual environments ---
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# --- Database ---
*.db
*.sqlite3
instance/

# --- OdooUpgradeEngine Specific Runtime Directories ---
# These directories are generated by the application at runtime and must NOT be version-controlled.
uploads/*.zip
uploads/modules/
!uploads/samples/
migration_logs/
automation_logs/
backups/
temp/
github_repo/
odoo_modules/v*/

# --- Log files ---
*.log
logs/
*.log.*

# --- IDE / Editor specific files ---
.vscode/
.idea/
*.swp
*.swo

# --- OS-generated files ---
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# --- Sensitive configuration files ---
# Never commit environment files containing secrets
*.env
.env.*
config.json

# --- Testing ---
.coverage
.pytest_cache/
htmlcov/
.tox/

# --- Performance ---
.prof

# --- Redis ---
dump.rdb

# --- Celery ---
celerybeat-schedule
celerybeat.pid

# --- Keep sample files ---
!uploads/samples/
!sample_modules/

# --- Development artifacts ---
*.tmp
*.bak
*.orig
debug_*.py
test_*.txt
*_results.txt
*_validation.txt
troubleshooting_*.json
