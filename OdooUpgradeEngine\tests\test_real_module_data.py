#!/usr/bin/env python3
"""
Real Module Data Testing - End-to-End Validation

This script tests the Odoo Upgrade Engine with actual module data:
1. Tests GitHub integration with real repositories
2. Tests module pulling and analysis
3. Tests AI integration with real module code
4. Tests migration workflow with actual Odoo modules
5. Validates the complete end-to-end process
"""

import os
import sys
import time
import json
import requests
import subprocess
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5003"
TEST_REPOSITORIES = [
    "https://github.com/OCA/server-tools",
    "https://github.com/OCA/web", 
    "https://github.com/OCA/account-financial-tools"
]

class RealModuleDataTester:
    def __init__(self):
        self.results = []
        self.start_time = datetime.now()
        
    def log_test(self, test_name, status, details=""):
        """Log test results"""
        result = {
            'test': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"   {status_emoji} {test_name}: {details}")
        
    def wait_for_server(self, max_wait=30):
        """Wait for the Flask server to be ready"""
        print("🔄 Waiting for Flask server to start...")
        
        for i in range(max_wait):
            try:
                response = requests.get(f"{BASE_URL}/", timeout=5)
                if response.status_code == 200:
                    print("✅ Flask server is ready")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
            if i % 5 == 0:
                print(f"   Still waiting... ({i}/{max_wait}s)")
        
        print("❌ Flask server failed to start")
        return False
    
    def test_github_repository_scan(self):
        """Test scanning real GitHub repositories for modules"""
        print("\n🔍 Testing GitHub Repository Scanning...")
        
        for repo_url in TEST_REPOSITORIES:
            print(f"\n📁 Testing repository: {repo_url}")
            
            try:
                response = requests.post(
                    f"{BASE_URL}/api/github-scan-repository",
                    json={"repository_url": repo_url},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        modules = data.get('modules', [])
                        self.log_test(
                            f"Scan {repo_url.split('/')[-1]}", 
                            "PASS", 
                            f"Found {len(modules)} modules"
                        )
                        
                        # Log some module details
                        for module in modules[:3]:  # First 3 modules
                            print(f"      - {module.get('name', 'Unknown')}: {module.get('version', 'No version')}")
                            
                    else:
                        self.log_test(
                            f"Scan {repo_url.split('/')[-1]}", 
                            "FAIL", 
                            data.get('error', 'Unknown error')
                        )
                else:
                    self.log_test(
                        f"Scan {repo_url.split('/')[-1]}", 
                        "FAIL", 
                        f"HTTP {response.status_code}"
                    )
                    
            except Exception as e:
                self.log_test(
                    f"Scan {repo_url.split('/')[-1]}", 
                    "FAIL", 
                    f"Exception: {str(e)}"
                )
    
    def test_module_pulling(self):
        """Test pulling actual modules from GitHub"""
        print("\n📥 Testing Module Pulling...")
        
        # Use the first repository for detailed testing
        test_repo = TEST_REPOSITORIES[0]
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/github/pull-modules",
                json={
                    "repository_url": test_repo,
                    "target_version": "18.0",
                    "migration_mode": "direct",  # Use direct mode instead of pipeline
                    "limit": 1  # Test with just 1 module
                },
                timeout=30  # Shorter timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    modules = data.get('modules', [])
                    jobs = data.get('migration_jobs', [])
                    
                    self.log_test(
                        "Module Pulling", 
                        "PASS", 
                        f"Pulled {len(modules)} modules, created {len(jobs)} jobs"
                    )
                    
                    # Return job IDs for further testing
                    return [job.get('id') for job in jobs if job.get('id')]
                    
                else:
                    self.log_test(
                        "Module Pulling", 
                        "FAIL", 
                        data.get('error', 'Unknown error')
                    )
            else:
                self.log_test(
                    "Module Pulling", 
                    "FAIL", 
                    f"HTTP {response.status_code}"
                )
                
        except Exception as e:
            self.log_test(
                "Module Pulling", 
                "FAIL", 
                f"Exception: {str(e)}"
            )
        
        return []
    
    def test_ai_analysis(self, job_ids):
        """Test AI analysis with real module data"""
        print("\n🤖 Testing AI Analysis...")
        
        if not job_ids:
            self.log_test("AI Analysis", "SKIP", "No migration jobs to analyze")
            return
        
        for job_id in job_ids[:2]:  # Test first 2 jobs
            try:
                response = requests.post(
                    f"{BASE_URL}/api/ai-analyze-module",
                    json={"job_id": job_id},
                    timeout=60
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        analysis = data.get('analysis', {})
                        self.log_test(
                            f"AI Analysis Job {job_id}", 
                            "PASS", 
                            f"Compatibility: {analysis.get('compatibility_score', 'N/A')}"
                        )
                    else:
                        self.log_test(
                            f"AI Analysis Job {job_id}", 
                            "FAIL", 
                            data.get('error', 'Unknown error')
                        )
                else:
                    self.log_test(
                        f"AI Analysis Job {job_id}", 
                        "FAIL", 
                        f"HTTP {response.status_code}"
                    )
                    
            except Exception as e:
                self.log_test(
                    f"AI Analysis Job {job_id}", 
                    "FAIL", 
                    f"Exception: {str(e)}"
                )
    
    def test_migration_status(self, job_ids):
        """Test migration status tracking"""
        print("\n📊 Testing Migration Status...")
        
        try:
            response = requests.get(f"{BASE_URL}/api/migration-status", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    active_jobs = data.get('active_migrations', [])
                    completed_jobs = data.get('completed_migrations', [])
                    
                    self.log_test(
                        "Migration Status", 
                        "PASS", 
                        f"Active: {len(active_jobs)}, Completed: {len(completed_jobs)}"
                    )
                else:
                    self.log_test(
                        "Migration Status", 
                        "FAIL", 
                        data.get('error', 'Unknown error')
                    )
            else:
                self.log_test(
                    "Migration Status", 
                    "FAIL", 
                    f"HTTP {response.status_code}"
                )
                
        except Exception as e:
            self.log_test(
                "Migration Status", 
                "FAIL", 
                f"Exception: {str(e)}"
            )
    
    def test_dashboard_data(self):
        """Test dashboard with real data"""
        print("\n📈 Testing Dashboard Data...")

        try:
            response = requests.get(f"{BASE_URL}/api/dashboard-data", timeout=30)

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    stats = data.get('stats', {})
                    self.log_test(
                        "Dashboard Data",
                        "PASS",
                        f"Modules: {stats.get('total_modules', 0)}, Jobs: {stats.get('total_jobs', 0)}"
                    )
                else:
                    self.log_test(
                        "Dashboard Data",
                        "FAIL",
                        data.get('error', 'Unknown error')
                    )
            else:
                self.log_test(
                    "Dashboard Data",
                    "FAIL",
                    f"HTTP {response.status_code}"
                )

        except Exception as e:
            self.log_test(
                "Dashboard Data",
                "FAIL",
                f"Exception: {str(e)}"
            )

    def test_github_sync_functionality(self):
        """Test GitHub sync back functionality - CRITICAL MISSING TEST"""
        print("\n🔄 Testing GitHub Sync Back Functionality...")

        # Test 1: Check GitHub sync endpoint exists
        try:
            response = requests.post(
                f"{BASE_URL}/api/github/sync-upgraded",
                json={"module_ids": [], "target_branch": "test-branch"},
                timeout=30
            )

            if response.status_code == 400:  # Expected - no modules selected
                self.log_test(
                    "GitHub Sync Endpoint",
                    "PASS",
                    "Endpoint exists and validates input"
                )
            else:
                self.log_test(
                    "GitHub Sync Endpoint",
                    "FAIL",
                    f"Unexpected response: HTTP {response.status_code}"
                )

        except Exception as e:
            self.log_test(
                "GitHub Sync Endpoint",
                "FAIL",
                f"Exception: {str(e)}"
            )

        # Test 2: Check GitHub authentication status
        try:
            # This would normally require GITHUB_TOKEN env var
            response = requests.post(
                f"{BASE_URL}/api/github/sync-upgraded",
                json={"module_ids": [999], "target_branch": "test-branch"},  # Non-existent module
                timeout=30
            )

            if response.status_code in [400, 401]:  # Expected - no auth or no modules
                data = response.json()
                if 'authentication' in data.get('error', '').lower() or 'github_token' in data.get('error', '').lower():
                    self.log_test(
                        "GitHub Authentication Check",
                        "PASS",
                        "Properly checks for GitHub token"
                    )
                else:
                    self.log_test(
                        "GitHub Authentication Check",
                        "PASS",
                        "Validates module existence"
                    )
            else:
                self.log_test(
                    "GitHub Authentication Check",
                    "WARN",
                    f"Unexpected response: HTTP {response.status_code}"
                )

        except Exception as e:
            self.log_test(
                "GitHub Authentication Check",
                "FAIL",
                f"Exception: {str(e)}"
            )

        # Test 3: Check GitHub integration page loads
        try:
            response = requests.get(f"{BASE_URL}/github_integration", timeout=30)

            if response.status_code == 200:
                content = response.text
                if 'syncUpgradedModules' in content and 'github/sync-upgraded' in content:
                    self.log_test(
                        "GitHub Integration Page",
                        "PASS",
                        "Page loads with sync functionality"
                    )
                else:
                    self.log_test(
                        "GitHub Integration Page",
                        "WARN",
                        "Page loads but sync functions may be missing"
                    )
            else:
                self.log_test(
                    "GitHub Integration Page",
                    "FAIL",
                    f"HTTP {response.status_code}"
                )

        except Exception as e:
            self.log_test(
                "GitHub Integration Page",
                "FAIL",
                f"Exception: {str(e)}"
            )
    
    def generate_report(self):
        """Generate comprehensive test report"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        passed = len([r for r in self.results if r['status'] == 'PASS'])
        failed = len([r for r in self.results if r['status'] == 'FAIL'])
        skipped = len([r for r in self.results if r['status'] == 'SKIP'])
        total = len(self.results)
        
        report = {
            'test_summary': {
                'total_tests': total,
                'passed': passed,
                'failed': failed,
                'skipped': skipped,
                'success_rate': f"{(passed/total*100):.1f}%" if total > 0 else "0%",
                'duration_seconds': duration
            },
            'test_results': self.results,
            'timestamp': end_time.isoformat()
        }
        
        # Save detailed report
        with open('real_module_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        return report

def main():
    """Main test execution"""
    print("🧪 REAL MODULE DATA TESTING")
    print("=" * 50)
    print(f"🎯 Testing Odoo Upgrade Engine with actual module data")
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tester = RealModuleDataTester()
    
    # Wait for server to be ready
    if not tester.wait_for_server():
        print("❌ Cannot proceed without Flask server")
        return False
    
    # Run comprehensive tests
    tester.test_github_repository_scan()
    job_ids = tester.test_module_pulling()
    tester.test_ai_analysis(job_ids)
    tester.test_migration_status(job_ids)
    tester.test_dashboard_data()
    tester.test_github_sync_functionality()  # CRITICAL MISSING TEST ADDED
    
    # Generate report
    report = tester.generate_report()
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 REAL MODULE DATA TEST SUMMARY")
    print("=" * 50)
    
    summary = report['test_summary']
    print(f"🕒 Duration: {summary['duration_seconds']:.1f} seconds")
    print(f"📈 Success Rate: {summary['success_rate']}")
    print(f"✅ Passed: {summary['passed']}")
    print(f"❌ Failed: {summary['failed']}")
    print(f"⚠️  Skipped: {summary['skipped']}")
    
    if summary['failed'] == 0:
        print("\n🎉 ALL TESTS PASSED! Real module data testing successful.")
        return True
    else:
        print(f"\n⚠️  {summary['failed']} tests failed. Check the detailed report.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
