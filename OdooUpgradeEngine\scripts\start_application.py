#!/usr/bin/env python3
"""
Complete Application Startup Script for Odoo Upgrade Engine

This script starts all required services for the Odoo Upgrade Engine:
1. Flask web application
2. Celery worker (optional)
3. Ollama server (optional)

Usage:
    python start_application.py                    # Start Flask only
    python start_application.py --with-worker     # Start Flask + Celery worker
    python start_application.py --with-ollama     # Start Flask + Ollama
    python start_application.py --all             # Start everything
"""

import os
import sys
import time
import signal
import argparse
import subprocess
import threading
from pathlib import Path

class ApplicationStarter:
    def __init__(self):
        self.processes = []
        self.project_dir = Path(__file__).parent
        
    def check_dependencies(self):
        """Check if required dependencies are available"""
        try:
            import flask
            import celery
            print("✅ Flask and Celery dependencies found")
            return True
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            print("Please install dependencies with: pip install -r requirements.txt")
            return False
    
    def check_redis(self):
        """Check if Redis is available for Celery"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            print("✅ Redis connection successful")
            return True
        except Exception as e:
            print(f"⚠️  Redis not available: {e}")
            print("Celery worker will not be able to start without Redis")
            return False
    
    def check_ollama(self):
        """Check if Ollama is available"""
        try:
            result = subprocess.run(['ollama', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ Ollama is available")
                return True
            else:
                print("⚠️  Ollama command failed")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️  Ollama not found in PATH")
            print("Install from: https://ollama.ai/")
            return False
    
    def start_flask(self):
        """Start the Flask web application"""
        print("🌐 Starting Flask web application...")
        
        os.chdir(self.project_dir)
        os.environ.setdefault('FLASK_APP', 'app.py')
        
        cmd = [sys.executable, 'app.py']
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                     stderr=subprocess.STDOUT, text=True)
            self.processes.append(('Flask', process))
            
            # Wait a moment and check if it started successfully
            time.sleep(2)
            if process.poll() is None:
                print("✅ Flask application started successfully")
                print("🌐 Access the application at: http://localhost:5000")
                return True
            else:
                print("❌ Flask application failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start Flask: {e}")
            return False
    
    def start_celery_worker(self):
        """Start the Celery worker"""
        print("🔄 Starting Celery worker...")
        
        os.chdir(self.project_dir)
        
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'main.celery',
            'worker',
            '--loglevel=info',
            '--pool=solo'  # Windows compatibility
        ]
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                     stderr=subprocess.STDOUT, text=True)
            self.processes.append(('Celery Worker', process))
            
            time.sleep(3)
            if process.poll() is None:
                print("✅ Celery worker started successfully")
                return True
            else:
                print("❌ Celery worker failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start Celery worker: {e}")
            return False
    
    def start_ollama(self):
        """Start Ollama server"""
        print("🤖 Starting Ollama server...")
        
        cmd = ['ollama', 'serve']
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                     stderr=subprocess.STDOUT, text=True)
            self.processes.append(('Ollama', process))
            
            time.sleep(3)
            if process.poll() is None:
                print("✅ Ollama server started successfully")
                return True
            else:
                print("❌ Ollama server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start Ollama: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor running processes and show their output"""
        def monitor_process(name, process):
            try:
                for line in process.stdout:
                    print(f"[{name}] {line.strip()}")
            except Exception as e:
                print(f"[{name}] Monitor error: {e}")
        
        # Start monitoring threads
        for name, process in self.processes:
            thread = threading.Thread(target=monitor_process, args=(name, process))
            thread.daemon = True
            thread.start()
    
    def cleanup(self):
        """Clean up all processes"""
        print("\n🛑 Shutting down services...")
        
        for name, process in self.processes:
            try:
                print(f"Stopping {name}...")
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"Force killing {name}...")
                process.kill()
            except Exception as e:
                print(f"Error stopping {name}: {e}")
        
        print("✅ All services stopped")
    
    def run(self, with_worker=False, with_ollama=False):
        """Run the application with specified services"""
        print("🚀 Odoo Upgrade Engine - Application Startup")
        print("=" * 50)
        
        # Check dependencies
        if not self.check_dependencies():
            return False
        
        # Start Flask (always required)
        if not self.start_flask():
            return False
        
        # Start optional services
        if with_worker:
            if self.check_redis():
                self.start_celery_worker()
            else:
                print("⚠️  Skipping Celery worker due to Redis unavailability")
        
        if with_ollama:
            if self.check_ollama():
                self.start_ollama()
            else:
                print("⚠️  Skipping Ollama due to unavailability")
        
        # Monitor processes
        self.monitor_processes()
        
        try:
            print("\n" + "=" * 50)
            print("🎉 Application is running!")
            print("🌐 Web Interface: http://localhost:5000")
            print("Press Ctrl+C to stop all services")
            print("=" * 50)
            
            # Wait for interrupt
            while True:
                time.sleep(1)
                # Check if any critical process died
                for name, process in self.processes:
                    if name == 'Flask' and process.poll() is not None:
                        print(f"❌ Critical service {name} died!")
                        return False
                        
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested by user")
        finally:
            self.cleanup()
        
        return True

def main():
    parser = argparse.ArgumentParser(description='Start Odoo Upgrade Engine')
    parser.add_argument('--with-worker', action='store_true', 
                       help='Start Celery worker for background tasks')
    parser.add_argument('--with-ollama', action='store_true', 
                       help='Start Ollama server for local AI')
    parser.add_argument('--all', action='store_true', 
                       help='Start all services (Flask + Celery + Ollama)')
    
    args = parser.parse_args()
    
    if args.all:
        args.with_worker = True
        args.with_ollama = True
    
    starter = ApplicationStarter()
    success = starter.run(with_worker=args.with_worker, with_ollama=args.with_ollama)
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
