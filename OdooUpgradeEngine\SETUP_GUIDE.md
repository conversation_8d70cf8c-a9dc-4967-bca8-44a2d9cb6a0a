# Complete Setup Guide: Odoo v13→v18 Automation System

## Overview
This guide will help you set up the complete automated Odoo module upgrade system that processes modules from v13 through v18 with intelligent version detection and automated folder placement.

## Prerequisites
- GitHub repository for storing modules
- GitHub account with appropriate permissions
- Basic understanding of Odoo module structure

## Directory Structure You'll Create

```
your-github-repo/
├── odoo_modules/
│   ├── v13_original/          # Place your Odoo 13.0 modules here
│   ├── v14_original/          # Place your Odoo 14.0 modules here  
│   ├── v15_original/          # Place your Odoo 15.0 modules here
│   ├── v16_original/          # Place your Odoo 16.0 modules here
│   ├── v17_original/          # Place your Odoo 17.0 modules here
│   ├── v18_original/          # Place your Odoo 18.0 modules here
│   ├── v14_upgraded/          # Auto-generated: v13→v14 upgrades
│   ├── v15_upgraded/          # Auto-generated: v14→v15 upgrades
│   ├── v16_upgraded/          # Auto-generated: v15→v16 upgrades
│   ├── v17_upgraded/          # Auto-generated: v16→v17 upgrades
│   ├── v18_upgraded/          # Auto-generated: v17→v18 upgrades
│   ├── backups/               # Auto-generated: Safety backups
│   └── automation_logs/       # Auto-generated: Processing logs
├── .github/workflows/         # GitHub Actions automation
├── automation_config.json     # System configuration
└── automation_system.py       # Core automation engine
```

## Step 1: GitHub Repository Setup

### 1.1 Create Your Repository
```bash
# Create a new repository on GitHub or clone existing one
git clone https://github.com/YOUR-USERNAME/YOUR-REPO-NAME.git
cd YOUR-REPO-NAME
```

### 1.2 Set Up Directory Structure
```bash
# Create all required directories
mkdir -p odoo_modules/{v13_original,v14_original,v15_original,v16_original,v17_original,v18_original}
mkdir -p odoo_modules/{v14_upgraded,v15_upgraded,v16_upgraded,v17_upgraded,v18_upgraded}
mkdir -p odoo_modules/{backups,automation_logs}
mkdir -p .github/workflows

# Create placeholder files to ensure directories are tracked by Git
touch odoo_modules/v13_original/.gitkeep
touch odoo_modules/v14_original/.gitkeep
touch odoo_modules/v15_original/.gitkeep
touch odoo_modules/v16_original/.gitkeep
touch odoo_modules/v17_original/.gitkeep
touch odoo_modules/v18_original/.gitkeep
```

## Step 2: Module Placement Guide

### 2.1 Don't Worry About Version Detection!
Our system uses **intelligent version detection** that analyzes:
- API patterns (@api.one, @api.multi for v13-v14)
- Import statements (openerp vs odoo)
- Asset structure (manifest.py assets for v15+)
- JavaScript frameworks (OWL components for v16+)
- File organization (__openerp__.py vs __manifest__.py)

### 2.2 Where to Place Your Modules

**If you know the version:**
- v13 modules → `odoo_modules/v13_original/`
- v14 modules → `odoo_modules/v14_original/`
- v15 modules → `odoo_modules/v15_original/`
- v16 modules → `odoo_modules/v16_original/`
- v17 modules → `odoo_modules/v17_original/`
- v18 modules → `odoo_modules/v18_original/`

**If you're unsure:**
- Use the **Contributor Upload Form** at `/contribute`
- System will automatically detect version and place correctly
- No risk of wrong folder placement!

### 2.3 Supported Module Formats
- ✅ ZIP files (.zip)
- ✅ TAR files (.tar, .tar.gz, .tar.bz2)
- ✅ Individual module directories
- ✅ Maximum file size: 100MB per module

## Step 3: Configure the System

### 3.1 Update automation_config.json
```json
{
  "github": {
    "repo_url": "https://github.com/YOUR-USERNAME/YOUR-REPO-NAME.git",
    "branch": "main",
    "token": "",
    "auto_commit": true,
    "commit_message_template": "Auto-upgrade {module_name} to Odoo {version}"
  },
  "directories": {
    "base_path": "./odoo_modules",
    "original_modules": {
      "v13": "v13_original",
      "v14": "v14_original", 
      "v15": "v15_original",
      "v16": "v16_original",
      "v17": "v17_original",
      "v18": "v18_original"
    },
    "upgraded_modules": {
      "v14": "v14_upgraded",
      "v15": "v15_upgraded",
      "v16": "v16_upgraded",
      "v17": "v17_upgraded",
      "v18": "v18_upgraded"
    }
  },
  "processing": {
    "batch_size": 5,
    "quality_threshold": 85.0,
    "auto_fix_enabled": true,
    "advanced_upgrade_enabled": true
  }
}
```

### 3.2 Set GitHub Secrets
In your GitHub repository, go to Settings → Secrets and Variables → Actions:

1. **GITHUB_TOKEN** (usually auto-provided)
2. **REPO_ACCESS_TOKEN** (if using private repos)

## Step 4: Upgrade Pipeline Explanation

### 4.1 Automatic Progression
The system follows this progression path:
```
v13 → v14 → v15 → v16 → v17 → v18
```

Each upgrade step handles specific changes:

**v13 → v14:**
- Remove @api.one, @api.multi, @api.returns decorators
- Fix sudo() calls to with_user()
- Update openerp imports to odoo imports
- Method iteration fixes (for record in self:)

**v14 → v15:**
- Move assets from XML to manifest.py
- Convert qweb templates to web.assets_qweb
- Update asset bundle structure
- Fix template loading patterns

**v15 → v16:**
- Convert legacy JavaScript to OWL components
- Update Widget.extend to ES6 classes
- Convert odoo.define to @odoo-module
- Migrate to modern import/export syntax

**v16 → v17:**
- Advanced OWL component optimization
- Asset operation improvements (prepend, before, after)
- Bootstrap 5 compatibility
- UI/UX enhancements

**v17 → v18:**
- Performance optimizations (3.7x backend speed)
- Spreadsheet-based reports
- Custom property field support
- Final compatibility polish

### 4.2 Quality Assurance
- **Compatibility Scoring:** Modules must achieve ≥85% compatibility
- **Automated Fixes:** Common issues fixed automatically
- **Backup System:** Original modules preserved
- **Error Recovery:** Failed upgrades logged and retried

## Step 5: How to Use the System

### 5.1 Web Interface Method
1. Access the web application
2. Go to "Contribute" in the navigation
3. Upload your modules (any version)
4. Enable "Smart version detection"
5. Enable "Automatic upgrades"
6. System handles everything automatically!

### 5.2 Direct GitHub Method
1. Place modules in appropriate `v*_original/` folders
2. Commit and push to GitHub
3. GitHub Actions automatically triggers
4. Check `automation_logs/` for processing status
5. Find upgraded modules in `v*_upgraded/` folders

### 5.3 Manual Trigger
Run automation manually via GitHub Actions:
1. Go to Actions tab in your GitHub repo
2. Select "Odoo Module Automation v13→v18"
3. Click "Run workflow"
4. Configure options as needed

## Step 6: Monitoring and Maintenance

### 6.1 Check Processing Status
- **Web Dashboard:** Visit `/automation/` for real-time status
- **GitHub Actions:** Monitor workflow runs
- **Log Files:** Check `automation_logs/` directory
- **Email Notifications:** Configure in automation_config.json

### 6.2 Common Issues and Solutions

**Issue: Module not detected correctly**
- Solution: Use contributor upload form for automatic detection
- Fallback: Manually place in v13_original (earliest version)

**Issue: Upgrade fails**
- Solution: Check automation_logs for specific errors
- Manual fix: Download module, fix issues, re-upload

**Issue: GitHub sync fails**
- Solution: Check repository URL and permissions
- Verify GitHub token has write access

### 6.3 Quality Metrics
- **Success Rate:** Aim for >90% successful upgrades
- **Processing Time:** Average 2-5 minutes per module
- **Compatibility Score:** Target ≥85% after upgrade
- **Error Rate:** <10% requiring manual intervention

## Step 7: Advanced Configuration

### 7.1 Custom Upgrade Rules
Edit `advanced_module_upgrader.py` to add custom patterns:
```python
# Add version-specific patterns
version_patterns = {
    'custom_pattern': ['your_pattern', 'another_pattern']
}
```

### 7.2 Batch Processing Tuning
Adjust `automation_config.json`:
```json
{
  "processing": {
    "batch_size": 10,           // More modules per batch
    "delay_between_batches": 60, // Longer delay for stability
    "max_retries": 5,           // More retry attempts
    "quality_threshold": 90.0   // Higher quality requirement
  }
}
```

### 7.3 Notification Setup
Configure alerts:
```json
{
  "notifications": {
    "enabled": true,
    "webhook_url": "https://hooks.slack.com/...",
    "email_recipients": ["<EMAIL>"],
    "slack_webhook": "https://hooks.slack.com/..."
  }
}
```

## Troubleshooting

### Common Problems

**Problem:** "Working outside of application context" error
**Solution:** Restart the automation system or web application

**Problem:** Version detection returns "Unknown"
**Solution:** 
1. Use contributor upload form with manual version selection
2. Check if module has __manifest__.py or __openerp__.py
3. Ensure module structure is valid

**Problem:** Automation cycle doesn't trigger
**Solution:**
1. Check GitHub Actions permissions
2. Verify webhook configuration
3. Manual trigger via `/automation/` dashboard

**Problem:** Modules stuck in processing
**Solution:**
1. Check log files in automation_logs/
2. Restart automation system
3. Clear failed jobs via dashboard

### Getting Help

1. **Check Logs:** Always start with automation_logs/
2. **Web Dashboard:** Use `/automation/` for system status
3. **GitHub Issues:** Check repository issues tab
4. **Manual Processing:** Use individual module analysis as fallback

## Success Indicators

Your system is working correctly when:
- ✅ New modules automatically detected and processed
- ✅ Upgraded modules appear in target folders
- ✅ Compatibility scores improve after upgrade
- ✅ GitHub commits happen automatically
- ✅ Error rate stays below 10%
- ✅ Contributors can upload modules successfully

## Next Steps

Once your system is running:
1. **Monitor Daily:** Check automation dashboard
2. **Quality Review:** Verify upgraded modules periodically
3. **Community Growth:** Share contributor upload form
4. **Backup Strategy:** Regular repository backups
5. **Scale Up:** Increase batch sizes as system stabilizes

Your automated Odoo module upgrade system is now ready to handle the complete v13→v18 pipeline with intelligent version detection and quality assurance!