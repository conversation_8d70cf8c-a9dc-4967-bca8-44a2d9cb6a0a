{% extends "base.html" %}

{% block title %}Manual Intervention Queue - Odoo Module Migration Platform{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Manual Intervention Queue</h2>
                    <p class="text-muted">Review and resolve migration issues requiring human attention</p>
                </div>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary" onclick="refreshQueue()" title="Refresh Queue">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                    <button class="btn btn-outline-warning" onclick="escalateOverdue()" title="Escalate Overdue">
                        <i class="fas fa-exclamation-triangle"></i> Escalate Overdue
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Queue Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <h5 class="card-title text-danger">Critical</h5>
                    <h3 class="text-danger" id="critical-count">-</h3>
                    <small class="text-muted">Immediate attention</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h5 class="card-title text-warning">High Priority</h5>
                    <h3 class="text-warning" id="high-priority-count">-</h3>
                    <small class="text-muted">Within 8 hours</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h5 class="card-title">Pending</h5>
                    <h3 id="pending-count">-</h3>
                    <small class="text-muted">Awaiting review</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h5 class="card-title">Assigned</h5>
                    <h3 id="assigned-count">-</h3>
                    <small class="text-muted">In progress</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h5 class="card-title">Completed</h5>
                    <h3 id="completed-count">-</h3>
                    <small class="text-muted">Resolved</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-secondary">
                <div class="card-body text-center">
                    <h5 class="card-title">Avg Resolution</h5>
                    <h3 id="avg-resolution">-</h3>
                    <small class="text-muted">Hours</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Intervention Queue -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Intervention Queue</h5>
                    <div class="d-flex gap-2">
                        <select id="severity-filter" class="form-select form-select-sm" onchange="applyFilters()">
                            <option value="">All Severities</option>
                            <option value="critical">Critical</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                        <select id="status-filter" class="form-select form-select-sm" onchange="applyFilters()">
                            <option value="">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="assigned">Assigned</option>
                            <option value="in_review">In Review</option>
                            <option value="escalated">Escalated</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="interventions-table">
                            <thead>
                                <tr>
                                    <th>Priority</th>
                                    <th>Title</th>
                                    <th>Job ID</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>File</th>
                                    <th>Age</th>
                                    <th>Assigned To</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="interventions-tbody">
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Loading intervention queue...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Intervention Details Modal -->
<div class="modal fade" id="interventionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Intervention Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="intervention-details-content">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <div class="btn-group" id="intervention-actions" style="display: none;">
                    <button type="button" class="btn btn-success" onclick="resolveIntervention('approved')">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button type="button" class="btn btn-warning" onclick="resolveIntervention('modified')">
                        <i class="fas fa-edit"></i> Modify
                    </button>
                    <button type="button" class="btn btn-danger" onclick="resolveIntervention('rejected')">
                        <i class="fas fa-times"></i> Reject
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assignment Modal -->
<div class="modal fade" id="assignmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Intervention</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="assignment-form">
                    <div class="mb-3">
                        <label for="reviewer-select" class="form-label">Assign to Reviewer:</label>
                        <select class="form-select" id="reviewer-select" required>
                            <option value="">Select a reviewer...</option>
                            <option value="admin">Admin</option>
                            <option value="reviewer1">Senior Developer</option>
                            <option value="reviewer2">Migration Specialist</option>
                            <option value="reviewer3">Technical Lead</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Current Workload:</label>
                        <div id="workload-info" class="text-muted">
                            Select a reviewer to see their current workload...
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="assignIntervention()">
                    <i class="fas fa-user-plus"></i> Assign
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentInterventionId = null;
let queueData = [];

function refreshQueue() {
    loadInterventionQueue();
}

function loadInterventionQueue() {
    fetch('/api/intervention-queue')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatistics(data.statistics);
                updateQueueTable(data.queue);
                queueData = data.queue;
            } else {
                console.error('Failed to load queue:', data.error);
                showError('Failed to load intervention queue');
            }
        })
        .catch(error => {
            console.error('Error loading queue:', error);
            showError('Error loading intervention queue');
        });
}

function updateStatistics(stats) {
    try {
        document.getElementById('critical-count').textContent = stats.critical_count || 0;
        document.getElementById('high-priority-count').textContent = stats.high_priority_count || 0;
        document.getElementById('pending-count').textContent = stats.total_pending || 0;
        document.getElementById('assigned-count').textContent = stats.total_assigned || 0;
        document.getElementById('completed-count').textContent = stats.total_completed || 0;
        document.getElementById('avg-resolution').textContent = (stats.average_resolution_time || 0).toFixed(1);
    } catch (error) {
        console.error('Error updating statistics:', error);
    }
}

function updateQueueTable(queue) {
    const tbody = document.getElementById('interventions-tbody');
    
    if (queue.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No interventions in queue</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = queue.map(intervention => `
        <tr data-intervention-id="${intervention.id}" class="${getSeverityRowClass(intervention.severity)}">
            <td>
                <span class="badge ${getSeverityBadgeClass(intervention.severity)}">
                    ${intervention.severity.toUpperCase()}
                </span>
            </td>
            <td>
                <strong>${intervention.title}</strong>
                <br>
                <small class="text-muted">${intervention.description.substring(0, 80)}...</small>
            </td>
            <td>
                <a href="/migration-jobs?highlight=${intervention.job_id}" target="_blank">
                    ${intervention.job_id}
                </a>
            </td>
            <td>
                <span class="badge bg-secondary">${intervention.intervention_type}</span>
            </td>
            <td>
                <span class="badge ${getStatusBadgeClass(intervention.status)}">
                    ${intervention.status}
                </span>
            </td>
            <td>
                ${intervention.file_path ? `
                    <small>${intervention.file_path}</small>
                    ${intervention.line_number ? `<br><small class="text-muted">Line ${intervention.line_number}</small>` : ''}
                ` : '-'}
            </td>
            <td>
                <span class="${getAgeClass(intervention.hours_pending)}">
                    ${formatAge(intervention.hours_pending)}
                </span>
            </td>
            <td>
                ${intervention.resolved_by || '-'}
            </td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewIntervention(${intervention.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${intervention.status === 'pending' ? `
                        <button class="btn btn-sm btn-outline-success" onclick="showAssignmentModal(${intervention.id})" title="Assign">
                            <i class="fas fa-user-plus"></i>
                        </button>
                    ` : ''}
                    ${['assigned', 'in_review'].includes(intervention.status) ? `
                        <button class="btn btn-sm btn-outline-warning" onclick="viewIntervention(${intervention.id})" title="Review">
                            <i class="fas fa-gavel"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

function getSeverityRowClass(severity) {
    switch (severity) {
        case 'critical': return 'table-danger';
        case 'high': return 'table-warning';
        default: return '';
    }
}

function getSeverityBadgeClass(severity) {
    switch (severity) {
        case 'critical': return 'bg-danger';
        case 'high': return 'bg-warning text-dark';
        case 'medium': return 'bg-info text-dark';
        case 'low': return 'bg-secondary';
        default: return 'bg-secondary';
    }
}

function getStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'bg-secondary';
        case 'assigned': return 'bg-primary';
        case 'in_review': return 'bg-warning text-dark';
        case 'approved': return 'bg-success';
        case 'rejected': return 'bg-danger';
        case 'modified': return 'bg-info text-dark';
        case 'escalated': return 'bg-dark';
        default: return 'bg-secondary';
    }
}

function getAgeClass(hours) {
    if (hours > 24) return 'text-danger fw-bold';
    if (hours > 8) return 'text-warning fw-bold';
    return 'text-muted';
}

function formatAge(hours) {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${Math.round(hours)}h`;
    return `${Math.round(hours / 24)}d`;
}

function viewIntervention(interventionId) {
    currentInterventionId = interventionId;
    const intervention = queueData.find(i => i.id === interventionId);
    
    if (!intervention) {
        showError('Intervention not found');
        return;
    }
    
    const content = document.getElementById('intervention-details-content');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>ID:</strong></td><td>${intervention.id}</td></tr>
                    <tr><td><strong>Job ID:</strong></td><td>${intervention.job_id}</td></tr>
                    <tr><td><strong>Type:</strong></td><td>${intervention.intervention_type}</td></tr>
                    <tr><td><strong>Severity:</strong></td><td><span class="badge ${getSeverityBadgeClass(intervention.severity)}">${intervention.severity}</span></td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(intervention.status)}">${intervention.status}</span></td></tr>
                    <tr><td><strong>Created:</strong></td><td>${formatDateTime(intervention.created_at)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>File Context</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>File:</strong></td><td>${intervention.file_path || 'N/A'}</td></tr>
                    <tr><td><strong>Line:</strong></td><td>${intervention.line_number || 'N/A'}</td></tr>
                    <tr><td><strong>Age:</strong></td><td>${formatAge(intervention.hours_pending)}</td></tr>
                    <tr><td><strong>Assigned To:</strong></td><td>${intervention.resolved_by || 'Unassigned'}</td></tr>
                    <tr><td><strong>Resolved:</strong></td><td>${intervention.resolved_at ? formatDateTime(intervention.resolved_at) : 'Not resolved'}</td></tr>
                </table>
            </div>
        </div>
        <div class="mt-3">
            <h6>Description</h6>
            <div class="alert alert-info">
                ${intervention.description}
            </div>
        </div>
    `;
    
    // Show action buttons if intervention can be resolved
    const actionsDiv = document.getElementById('intervention-actions');
    if (['assigned', 'in_review'].includes(intervention.status)) {
        actionsDiv.style.display = 'block';
    } else {
        actionsDiv.style.display = 'none';
    }
    
    const modal = new bootstrap.Modal(document.getElementById('interventionModal'));
    modal.show();
}

function showAssignmentModal(interventionId) {
    currentInterventionId = interventionId;
    
    const modal = new bootstrap.Modal(document.getElementById('assignmentModal'));
    modal.show();
    
    // Load reviewer workload when reviewer is selected
    const reviewerSelect = document.getElementById('reviewer-select');
    reviewerSelect.addEventListener('change', function() {
        if (this.value) {
            loadReviewerWorkload(this.value);
        } else {
            document.getElementById('workload-info').textContent = 'Select a reviewer to see their current workload...';
        }
    });
}

function loadReviewerWorkload(reviewerId) {
    fetch(`/api/reviewer-workload/${reviewerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const workload = data.workload;
                document.getElementById('workload-info').innerHTML = `
                    <strong>Current Assignments:</strong> ${workload.assigned_count}<br>
                    <strong>Completed:</strong> ${workload.completed_count}<br>
                    <strong>Average Resolution Time:</strong> ${workload.average_resolution_time.toFixed(1)} hours<br>
                    <strong>Capacity Remaining:</strong> ${workload.current_capacity}
                `;
            }
        })
        .catch(error => {
            console.error('Error loading workload:', error);
        });
}

function assignIntervention() {
    const reviewerId = document.getElementById('reviewer-select').value;
    
    if (!reviewerId) {
        showError('Please select a reviewer');
        return;
    }
    
    fetch('/api/assign-intervention', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            intervention_id: currentInterventionId,
            reviewer_id: reviewerId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('assignmentModal')).hide();
            loadInterventionQueue(); // Refresh the queue
        } else {
            showError('Failed to assign intervention: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error assigning intervention:', error);
        showError('Error assigning intervention');
    });
}

function resolveIntervention(resolution) {
    const notes = prompt(`Enter resolution notes for ${resolution}:`);
    
    fetch('/api/resolve-intervention', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            intervention_id: currentInterventionId,
            resolution: resolution,
            notes: notes || '',
            reviewer_id: 'current_user' // In a real system, this would be the logged-in user
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('interventionModal')).hide();
            loadInterventionQueue(); // Refresh the queue
        } else {
            showError('Failed to resolve intervention: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error resolving intervention:', error);
        showError('Error resolving intervention');
    });
}

function escalateOverdue() {
    if (!confirm('This will escalate all overdue interventions. Continue?')) {
        return;
    }
    
    fetch('/api/escalate-interventions', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            loadInterventionQueue(); // Refresh the queue
        } else {
            showError('Failed to escalate interventions: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error escalating interventions:', error);
        showError('Error escalating interventions');
    });
}

function applyFilters() {
    const severityFilter = document.getElementById('severity-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    
    let filteredData = queueData;
    
    if (severityFilter) {
        filteredData = filteredData.filter(i => i.severity === severityFilter);
    }
    
    if (statusFilter) {
        filteredData = filteredData.filter(i => i.status === statusFilter);
    }
    
    updateQueueTable(filteredData);
}

function formatDateTime(isoString) {
    return new Date(isoString).toLocaleString();
}

function showError(message) {
    console.error('Manual Interventions Error:', message);
    // Create a temporary error message instead of alert
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    errorDiv.innerHTML = `
        <strong>Error:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(errorDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

function showSuccess(message) {
    console.log('Manual Interventions Success:', message);
    // Create a temporary success message instead of alert
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    successDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    successDiv.innerHTML = `
        <strong>Success:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(successDiv);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

// Auto-refresh queue every 30 seconds
setInterval(loadInterventionQueue, 30000);

// Load queue on page load
document.addEventListener('DOMContentLoaded', function() {
    loadInterventionQueue();
});
</script>
{% endblock %}