Provider DeepSeek Chat/Reasoner not available: API key DEEPSEEK_API_KEY not configured
🧪 REAL MODULE DATA TEST RUNNER
==================================================
🕒 Started at: 2025-07-13 15:24:57

🔧 Setting up test environment...
✅ Database initialized with proper Flask context
✅ Test environment configured
   Database: /tmp/test_odoo_upgrade.db
   AI Providers: Disabled
   External Services: Disabled
   ✅ Database initialized
🚀 Starting Flask application on port 5003...
   ✅ Flask application started successfully
🧪 Running real module data tests...
🧪 REAL MODULE DATA TESTING
==================================================
🎯 Testing Odoo Upgrade Engine with actual module data
🕒 Started at: 2025-07-13 15:25:03

🔄 Waiting for Flask server to start...
✅ Flask server is ready

🔍 Testing GitHub Repository Scanning...

📁 Testing repository: https://github.com/OCA/server-tools
   ✅ Scan server-tools: Found 32 modules
      - sentry: ********.0
      - auditlog: ********.0
      - jsonifier: ********.0

📁 Testing repository: https://github.com/OCA/web
   ✅ Scan web: Found 34 modules
      - web_notify: ********.0
      - web_favicon: ********.0
      - web_timeline: ********.0

📁 Testing repository: https://github.com/OCA/account-financial-tools
   ✅ Scan account-financial-tools: Found 22 modules
      - account_netting: ********.0
      - account_usability: ********.0
      - account_fiscal_year: ********.0

📥 Testing Module Pulling...
   ❌ Module Pulling: Exception: HTTPConnectionPool(host='localhost', port=5003): Read timed out. (read timeout=30)

🤖 Testing AI Analysis...
   ⚠️ AI Analysis: No migration jobs to analyze

📊 Testing Migration Status...
   ✅ Migration Status: Active: 0, Completed: 0

📈 Testing Dashboard Data...
   ✅ Dashboard Data: Modules: 0, Jobs: 0

🔄 Testing GitHub Sync Back Functionality...
   ✅ GitHub Sync Endpoint: Endpoint exists and validates input
   ✅ GitHub Authentication Check: Validates module existence
   ✅ GitHub Integration Page: Page loads with sync functionality

==================================================
📊 REAL MODULE DATA TEST SUMMARY
==================================================
🕒 Duration: 31.5 seconds
📈 Success Rate: 80.0%
✅ Passed: 8
❌ Failed: 1
⚠️  Skipped: 1

⚠️  1 tests failed. Check the detailed report.

📊 Generating summary report...

============================================================
🎯 REAL MODULE DATA TEST SUMMARY
============================================================
📊 Success Rate: 80.0%
✅ Passed: 8
❌ Failed: 1
⚠️  Skipped: 1
⏱️  Duration: 31.5s

📋 RECOMMENDATIONS:
   ⚠️ 1 tests failed. Review required before merge.

📁 Generated Files:
   - real_module_test_report.json (detailed results)
   - real_module_test_summary.json (summary)
🧹 Cleaning up...
🛑 Stopping Flask application...
   ✅ Flask application stopped
✅ Test database cleaned up
   ✅ Cleanup complete

==================================================
⚠️  REAL MODULE DATA TESTING COMPLETED WITH ISSUES
🔍 Review the test reports for details
