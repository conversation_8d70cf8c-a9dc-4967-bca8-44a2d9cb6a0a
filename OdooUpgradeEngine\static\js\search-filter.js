/**
 * Search and Filter JavaScript
 * Provides real-time search and filtering functionality
 * 
 * Features:
 * - Real-time search as you type
 * - Multi-criteria filtering
 * - Sort functionality
 * - Export filtered results
 */

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchAndFilter();
});

/**
 * Initialize search and filter functionality
 */
function initializeSearchAndFilter() {
    setupRealTimeSearch();
    setupFilterControls();
    setupSortControls();
    setupExportFunctionality();
}

// ===== REAL-TIME SEARCH =====

/**
 * Setup real-time search functionality
 */
function setupRealTimeSearch() {
    const searchInputs = document.querySelectorAll('[data-search-target]');
    
    searchInputs.forEach(input => {
        const targetSelector = input.getAttribute('data-search-target');
        const searchDelay = parseInt(input.getAttribute('data-search-delay')) || 300;
        
        let searchTimeout;
        
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performRealTimeSearch(input.value, targetSelector);
            }, searchDelay);
        });
    });
}

/**
 * Perform real-time search
 */
function performRealTimeSearch(query, targetSelector) {
    const items = document.querySelectorAll(targetSelector);
    const searchQuery = query.toLowerCase().trim();
    
    let visibleCount = 0;
    
    items.forEach(item => {
        const searchableText = getSearchableText(item);
        const isVisible = searchQuery === '' || searchableText.includes(searchQuery);
        
        item.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });
    
    updateSearchResults(visibleCount, items.length);
}

/**
 * Get searchable text from an item
 */
function getSearchableText(item) {
    const searchableElements = item.querySelectorAll('[data-searchable]');
    let text = '';
    
    if (searchableElements.length > 0) {
        searchableElements.forEach(el => {
            text += ' ' + el.textContent;
        });
    } else {
        text = item.textContent;
    }
    
    return text.toLowerCase();
}

/**
 * Update search results counter
 */
function updateSearchResults(visibleCount, totalCount) {
    const resultCounter = document.getElementById('search-results-count');
    if (resultCounter) {
        if (visibleCount === totalCount) {
            resultCounter.textContent = `Showing all ${totalCount} items`;
        } else {
            resultCounter.textContent = `Showing ${visibleCount} of ${totalCount} items`;
        }
    }
}

// ===== FILTER CONTROLS =====

/**
 * Setup filter controls
 */
function setupFilterControls() {
    const filterControls = document.querySelectorAll('[data-filter-target]');
    
    filterControls.forEach(control => {
        control.addEventListener('change', function() {
            applyFilters();
        });
    });
    
    // Setup clear filters button
    const clearFiltersBtn = document.getElementById('clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearAllFilters);
    }
}

/**
 * Apply all active filters
 */
function applyFilters() {
    const filterControls = document.querySelectorAll('[data-filter-target]');
    const items = document.querySelectorAll('[data-filterable]');
    
    let visibleCount = 0;
    
    items.forEach(item => {
        let isVisible = true;
        
        filterControls.forEach(control => {
            if (!isVisible) return;
            
            const filterType = control.getAttribute('data-filter-type');
            const filterValue = control.value;
            
            if (filterValue === '') return; // Skip empty filters
            
            isVisible = applyFilter(item, filterType, filterValue);
        });
        
        item.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });
    
    updateSearchResults(visibleCount, items.length);
    updateActiveFiltersDisplay();
}

/**
 * Apply individual filter
 */
function applyFilter(item, filterType, filterValue) {
    const filterElement = item.querySelector(`[data-filter-${filterType}]`);
    if (!filterElement) return true;
    
    const itemValue = filterElement.getAttribute(`data-filter-${filterType}`);
    
    switch (filterType) {
        case 'status':
            return itemValue === filterValue;
        case 'version':
            return itemValue === filterValue;
        case 'date':
            return isDateInRange(itemValue, filterValue);
        case 'priority':
            return itemValue === filterValue;
        default:
            return true;
    }
}

/**
 * Check if date is in range
 */
function isDateInRange(dateString, range) {
    if (!dateString || !range) return true;
    
    const itemDate = new Date(dateString);
    const now = new Date();
    
    switch (range) {
        case 'today':
            return itemDate.toDateString() === now.toDateString();
        case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            return itemDate >= weekAgo;
        case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            return itemDate >= monthAgo;
        default:
            return true;
    }
}

/**
 * Clear all filters
 */
function clearAllFilters() {
    const filterControls = document.querySelectorAll('[data-filter-target]');
    const searchInputs = document.querySelectorAll('[data-search-target]');
    
    filterControls.forEach(control => {
        control.value = '';
    });
    
    searchInputs.forEach(input => {
        input.value = '';
    });
    
    // Show all items
    const items = document.querySelectorAll('[data-filterable]');
    items.forEach(item => {
        item.style.display = '';
    });
    
    updateSearchResults(items.length, items.length);
    updateActiveFiltersDisplay();
}

/**
 * Update active filters display
 */
function updateActiveFiltersDisplay() {
    const activeFiltersContainer = document.getElementById('active-filters');
    if (!activeFiltersContainer) return;
    
    const filterControls = document.querySelectorAll('[data-filter-target]');
    const activeFilters = [];
    
    filterControls.forEach(control => {
        if (control.value !== '') {
            const label = control.previousElementSibling?.textContent || control.name;
            const value = control.options ? control.options[control.selectedIndex].text : control.value;
            activeFilters.push({ label, value, control });
        }
    });
    
    if (activeFilters.length === 0) {
        activeFiltersContainer.innerHTML = '';
        return;
    }
    
    let html = '<div class="mb-2"><small class="text-muted">Active filters:</small></div>';
    html += '<div class="d-flex flex-wrap gap-1">';
    
    activeFilters.forEach(filter => {
        html += `
            <span class="badge bg-primary">
                ${filter.label}: ${filter.value}
                <button type="button" class="btn-close btn-close-white ms-1" 
                        onclick="removeFilter('${filter.control.id}')" 
                        style="font-size: 0.7em;"></button>
            </span>
        `;
    });
    
    html += '</div>';
    activeFiltersContainer.innerHTML = html;
}

/**
 * Remove individual filter
 */
function removeFilter(controlId) {
    const control = document.getElementById(controlId);
    if (control) {
        control.value = '';
        applyFilters();
    }
}

// ===== SORT CONTROLS =====

/**
 * Setup sort controls
 */
function setupSortControls() {
    const sortControls = document.querySelectorAll('[data-sort-target]');
    
    sortControls.forEach(control => {
        control.addEventListener('change', function() {
            applySorting(control.value, control.getAttribute('data-sort-target'));
        });
    });
}

/**
 * Apply sorting
 */
function applySorting(sortBy, targetSelector) {
    const container = document.querySelector(targetSelector);
    if (!container) return;
    
    const items = Array.from(container.querySelectorAll('[data-sortable]'));
    
    items.sort((a, b) => {
        const aValue = getSortValue(a, sortBy);
        const bValue = getSortValue(b, sortBy);
        
        if (sortBy.includes('date')) {
            return new Date(bValue) - new Date(aValue); // Newest first
        } else if (sortBy.includes('name')) {
            return aValue.localeCompare(bValue);
        } else {
            return aValue.localeCompare(bValue);
        }
    });
    
    // Reorder DOM elements
    items.forEach(item => {
        container.appendChild(item);
    });
}

/**
 * Get sort value from item
 */
function getSortValue(item, sortBy) {
    const sortElement = item.querySelector(`[data-sort-${sortBy}]`);
    return sortElement ? sortElement.getAttribute(`data-sort-${sortBy}`) : '';
}

// ===== EXPORT FUNCTIONALITY =====

/**
 * Setup export functionality
 */
function setupExportFunctionality() {
    const exportBtn = document.getElementById('export-filtered');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportFilteredResults);
    }
}

/**
 * Export filtered results
 */
function exportFilteredResults() {
    const visibleItems = document.querySelectorAll('[data-filterable]:not([style*="display: none"])');
    
    if (visibleItems.length === 0) {
        alert('No items to export');
        return;
    }
    
    const data = [];
    visibleItems.forEach(item => {
        const rowData = {};
        const exportElements = item.querySelectorAll('[data-export]');
        
        exportElements.forEach(el => {
            const key = el.getAttribute('data-export');
            rowData[key] = el.textContent.trim();
        });
        
        data.push(rowData);
    });
    
    downloadCSV(data, 'filtered-results.csv');
}

/**
 * Download data as CSV
 */
function downloadCSV(data, filename) {
    if (data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// ===== UTILITY FUNCTIONS =====

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Highlight search terms
 */
function highlightSearchTerms(text, searchTerm) {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// ===== GLOBAL FUNCTIONS =====

// Make functions available globally
window.removeFilter = removeFilter;
window.clearAllFilters = clearAllFilters;
window.exportFilteredResults = exportFilteredResults;
