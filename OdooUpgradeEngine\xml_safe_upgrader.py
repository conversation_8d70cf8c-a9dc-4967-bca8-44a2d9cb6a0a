"""
XML Safe Upgrader - Professional XML Transformation

This module uses lxml for safe XML parsing and modification,
replacing dangerous regex-based XML manipulation.
"""

from lxml import etree
from pathlib import Path
import logging
from typing import Dict, List, Any, Optional
import os

class XMLSafeUpgrader:
    """
    Professional XML upgrader using lxml for safe transformations.
    
    This replaces regex-based XML manipulation with proper XML parsing.
    """
    
    def __init__(self, module_path=None, target_version=None):
        self.logger = logging.getLogger(__name__)
        self.module_path = module_path
        self.target_version = target_version
        self.changes_made = []

    def upgrade_module(self) -> Dict[str, Any]:
        """
        Upgrade all XML files in the module path provided during initialization.
        """
        if not self.module_path:
            return {"success": False, "error": "No module path provided"}

        return self.upgrade_module_xml_files(self.module_path)
    
    def upgrade_xml_file(self, file_path: str) -> Dict[str, Any]:
        """
        Safely upgrade an XML file using lxml parser.
        
        Args:
            file_path: Path to the XML file
            
        Returns:
            Dict with upgrade results
        """
        try:
            # Create backup
            backup_path = file_path + '.backup'
            
            # Parse XML safely
            parser = etree.XMLParser(remove_blank_text=False)
            tree = etree.parse(file_path, parser)
            root = tree.getroot()
            
            changes_count = 0
            
            # Apply XML transformations
            changes_count += self._upgrade_qweb_templates(root)
            changes_count += self._remove_deprecated_attributes(root)
            changes_count += self._update_view_inheritance(root)
            changes_count += self._modernize_form_elements(root)
            
            # Week 1 Missing XML Rules Implementation (Order matters!)
            changes_count += self._remove_nolabel_attributes(root)  # XML Rule 1 - Process first
            changes_count += self._convert_tout_to_tesc(root)       # XML Rule 2
            changes_count += self._modernize_view_structure(root)   # XML Rule 4 - Before deprecated attrs
            changes_count += self._remove_deprecated_view_attrs(root)  # XML Rule 3 - Process last
            
            if changes_count > 0:
                # Create backup before writing
                os.rename(file_path, backup_path)
                
                # Write the modified XML
                tree.write(file_path, encoding='utf-8', xml_declaration=True, pretty_print=True)
                
                self.logger.info(f"Upgraded XML file: {file_path} ({changes_count} changes)")
            
            return {
                'success': True,
                'changes_count': changes_count,
                'changes_made': self.changes_made.copy(),
                'backup_created': backup_path if changes_count > 0 else None
            }
            
        except etree.XMLSyntaxError as e:
            self.logger.error(f"XML syntax error in {file_path}: {str(e)}")
            return {
                'success': False,
                'error': f"XML syntax error: {str(e)}",
                'changes_count': 0
            }
        except Exception as e:
            self.logger.error(f"Failed to upgrade XML {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'changes_count': 0
            }
    
    def _upgrade_qweb_templates(self, root: etree.Element) -> int:
        """
        Upgrade QWeb templates to modern syntax.
        
        Args:
            root: XML root element
            
        Returns:
            Number of changes made
        """
        changes = 0
        
        # Convert t-out to t-esc (safer by default)
        for elem in root.xpath(".//*[@t-out]"):
            t_out_value = elem.get('t-out')
            elem.set('t-esc', t_out_value)
            elem.attrib.pop('t-out')
            self.changes_made.append(f"Converted t-out='{t_out_value}' to t-esc")
            changes += 1
        
        # Update t-raw to t-out for trusted content
        for elem in root.xpath(".//*[@t-raw]"):
            t_raw_value = elem.get('t-raw')
            elem.set('t-out', t_raw_value)
            elem.attrib.pop('t-raw')
            self.changes_made.append(f"Converted t-raw='{t_raw_value}' to t-out")
            changes += 1
        
        return changes
    
    def _remove_deprecated_attributes(self, root: etree.Element) -> int:
        """
        Remove deprecated attributes from elements.
        
        Args:
            root: XML root element
            
        Returns:
            Number of changes made
        """
        changes = 0
        
        deprecated_attrs = [
            # 'nolabel',    # Handled specifically by _remove_nolabel_attributes 
            'colspan',      # Use CSS instead
            'colors',       # Use decoration-* instead
            'fonts',        # Use decoration-* instead
        ]
        
        for attr in deprecated_attrs:
            xpath = f".//*[@{attr}]"
            for elem in root.xpath(xpath):
                old_value = elem.get(attr)
                elem.attrib.pop(attr)
                self.changes_made.append(f"Removed deprecated attribute {attr}='{old_value}'")
                changes += 1
        
        return changes
    
    def _update_view_inheritance(self, root: etree.Element) -> int:
        """
        Update view inheritance patterns to modern syntax.
        
        Args:
            root: XML root element
            
        Returns:
            Number of changes made
        """
        changes = 0
        
        # Update inherit_id patterns
        for record in root.xpath(".//record[@model='ir.ui.view']"):
            inherit_field = record.xpath(".//field[@name='inherit_id']")
            if inherit_field:
                for field in inherit_field:
                    if field.get('ref'):
                        # Modern syntax already
                        continue
                    elif field.text and '.' in field.text:
                        # Convert old module.view_id to ref="module.view_id"
                        ref_value = field.text.strip()
                        field.set('ref', ref_value)
                        field.text = None
                        self.changes_made.append(f"Modernized inherit_id: {ref_value}")
                        changes += 1
        
        return changes
    
    def _modernize_form_elements(self, root: etree.Element) -> int:
        """
        Modernize form elements and attributes.
        
        Args:
            root: XML root element
            
        Returns:
            Number of changes made
        """
        changes = 0
        
        # Update string attributes to use translation functions
        for elem in root.xpath(".//field[@string]"):
            string_value = elem.get('string')
            if string_value and not string_value.startswith('_('):
                # Add translation wrapper
                elem.set('string', f'_("{string_value}")')
                self.changes_made.append(f"Added translation to field string: {string_value}")
                changes += 1
        
        # Update button elements
        for button in root.xpath(".//button[@string]"):
            string_value = button.get('string')
            if string_value and not string_value.startswith('_('):
                button.set('string', f'_("{string_value}")')
                self.changes_made.append(f"Added translation to button string: {string_value}")
                changes += 1
        
        return changes
    
    def upgrade_module_xml_files(self, module_path: str) -> Dict[str, Any]:
        """
        Upgrade all XML files in a module.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            Comprehensive upgrade report
        """
        module_path = Path(module_path)
        results = {
            'module_name': module_path.name,
            'files_processed': 0,
            'files_upgraded': 0,
            'total_changes': 0,
            'errors': [],
            'changes_summary': []
        }
        
        # Find all XML files
        xml_files = list(module_path.glob('**/*.xml'))
        
        for xml_file in xml_files:
            results['files_processed'] += 1
            
            file_result = self.upgrade_xml_file(str(xml_file))
            
            if file_result['success']:
                if file_result['changes_count'] > 0:
                    results['files_upgraded'] += 1
                    results['total_changes'] += file_result['changes_count']
                    results['changes_summary'].extend(file_result['changes_made'])
            else:
                results['errors'].append({
                    'file': str(xml_file),
                    'error': file_result['error']
                })
        
        return results
    
    def _remove_nolabel_attributes(self, root) -> int:
        """
        XML Rule 1: Remove nolabel="1" attributes and add CSS class for styling.
        
        In modern Odoo, the nolabel attribute is deprecated in favor of CSS classes.
        """
        changes = 0
        
        # Find all elements with nolabel="1"
        for element in root.xpath(".//*[@nolabel='1']"):
            # Remove the nolabel attribute
            del element.attrib['nolabel']
            
            # Add CSS class for styling
            current_class = element.get('class', '')
            if 'o_no_label' not in current_class:
                new_class = f"{current_class} o_no_label".strip()
                element.set('class', new_class)
            
            self.changes_made.append(f"Removed nolabel='1' and added o_no_label class in {element.tag}")
            changes += 1
        
        return changes
    
    def _convert_tout_to_tesc(self, root) -> int:
        """
        XML Rule 2: Convert t-out to t-esc for QWeb template modernization.
        
        In newer Odoo versions, t-out is deprecated in favor of t-esc for security.
        """
        changes = 0
        
        # Find all elements with t-out attribute
        for element in root.xpath(".//*[@t-out]"):
            # Get the t-out value
            tout_value = element.get('t-out')
            
            # Remove t-out and add t-esc
            del element.attrib['t-out']
            element.set('t-esc', tout_value)
            
            self.changes_made.append(f"Converted t-out='{tout_value}' to t-esc in {element.tag}")
            changes += 1
        
        return changes
    
    def _remove_deprecated_view_attrs(self, root) -> int:
        """
        XML Rule 3: Remove deprecated view attributes.
        
        Remove attributes that are no longer supported in modern Odoo versions.
        Note: nolabel is handled separately in _remove_nolabel_attributes
        """
        changes = 0
        deprecated_attrs = [
            'version',  # view version attribute deprecated
            'type',     # type attribute in some contexts deprecated  
            'position', # position in wrong context
            'create',   # deprecated create attribute
            'edit',     # deprecated edit attribute
            'delete'    # deprecated delete attribute
            # Note: 'nolabel' is NOT included here - handled by _remove_nolabel_attributes
        ]
        
        for attr in deprecated_attrs:
            for element in root.xpath(f".//*[@{attr}]"):
                # Only remove if it's in deprecated context
                if self._is_deprecated_context(element, attr):
                    old_value = element.get(attr)
                    del element.attrib[attr]
                    self.changes_made.append(f"Removed deprecated attribute '{attr}={old_value}' from {element.tag}")
                    changes += 1
        
        return changes
    
    def _modernize_view_structure(self, root) -> int:
        """
        XML Rule 4: Modernize view structure for current Odoo standards.
        
        Update view structures to use modern Odoo patterns.
        """
        changes = 0
        
        # Convert old notebook to modern notebook structure
        for notebook in root.xpath(".//notebook"):
            if not notebook.get('class'):
                notebook.set('class', 'oe_notebook')
                changes += 1
                self.changes_made.append("Added modern class to notebook element")
        
        # Convert old group structures
        for group in root.xpath(".//group[@col and not(@string)]"):
            col = group.get('col')
            if col == '2':
                del group.attrib['col']
                group.set('class', 'oe_group_2')
                changes += 1
                self.changes_made.append("Modernized group structure with CSS class")
        
        # Update separator elements
        for separator in root.xpath(".//separator[@string and not(@title)]"):
            string_val = separator.get('string')
            separator.set('title', string_val)
            del separator.attrib['string']
            changes += 1
            self.changes_made.append(f"Modernized separator: string -> title")
        
        return changes
    
    def _is_deprecated_context(self, element, attr: str) -> bool:
        """
        Helper method to determine if an attribute is deprecated in current context.
        """
        tag = element.tag
        parent_tag = element.getparent().tag if element.getparent() is not None else None
        
        # Define contexts where attributes are deprecated
        deprecated_contexts = {
            'version': ['form', 'tree', 'kanban'],  # version attribute deprecated in views
            'type': ['field'],  # field type in views is deprecated
            'create': ['tree', 'form', 'kanban'],
            'edit': ['tree', 'form', 'kanban'],
            'delete': ['tree', 'form', 'kanban']
        }
        
        return tag in deprecated_contexts.get(attr, []) or parent_tag in deprecated_contexts.get(attr, [])

class XMLValidator:
    """
    XML validator to ensure files are well-formed after transformation.
    """
    
    @staticmethod
    def validate_xml_file(file_path: str) -> Dict[str, Any]:
        """
        Validate an XML file for well-formedness.
        
        Args:
            file_path: Path to XML file
            
        Returns:
            Validation result
        """
        try:
            parser = etree.XMLParser()
            etree.parse(file_path, parser)
            return {
                'valid': True,
                'errors': []
            }
        except etree.XMLSyntaxError as e:
            return {
                'valid': False,
                'errors': [str(e)]
            }
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Unexpected error: {str(e)}"]
            }
    
    @staticmethod
    def validate_module_xml(module_path: str) -> Dict[str, Any]:
        """
        Validate all XML files in a module.
        
        Args:
            module_path: Path to module directory
            
        Returns:
            Validation report for all XML files
        """
        module_path = Path(module_path)
        results = {
            'module_name': module_path.name,
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': [],
            'all_valid': True
        }
        
        xml_files = list(module_path.glob('**/*.xml'))
        results['total_files'] = len(xml_files)
        
        for xml_file in xml_files:
            validation = XMLValidator.validate_xml_file(str(xml_file))
            if validation['valid']:
                results['valid_files'] += 1
            else:
                results['all_valid'] = False
                results['invalid_files'].append({
                    'file': str(xml_file),
                    'errors': validation['errors']
                })
        
        return results