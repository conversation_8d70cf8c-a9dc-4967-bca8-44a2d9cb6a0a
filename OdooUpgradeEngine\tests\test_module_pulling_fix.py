#!/usr/bin/env python3
"""
Test Module Pulling Fix - Focused Testing

This script tests the module pulling with proper error handling and shorter timeouts
to identify the exact issue.
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5003"
TEST_REPO = "https://github.com/OCA/server-tools"

def setup_and_test():
    """Setup environment and test module pulling"""
    
    # Setup test environment
    from test_config import setup_test_environment
    setup_test_environment()
    
    print("🧪 FOCUSED MODULE PULLING TEST")
    print("=" * 50)
    
    # Start Flask app
    print("🚀 Starting Flask application...")
    import subprocess
    flask_process = subprocess.Popen([
        sys.executable, "-c",
        f"from app import create_app; app = create_app(); app.run(host='0.0.0.0', port=5003, debug=False)"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for startup
    time.sleep(5)
    
    # Test with very short timeout to see immediate response
    print("\n1️⃣ Testing with 10-second timeout...")
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/api/github/pull-modules",
            json={
                "repository_url": TEST_REPO,
                "target_version": "18.0",
                "migration_mode": "direct",  # Use direct mode instead of pipeline
                "limit": 1  # Pull only 1 module
            },
            timeout=10
        )
        elapsed = time.time() - start_time
        
        print(f"   Response received in {elapsed:.1f}s")
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            if data.get('success'):
                print(f"   Modules: {len(data.get('modules', []))}")
                print(f"   GitHub Stats: {data.get('github_stats', {})}")
            else:
                print(f"   Error: {data.get('error')}")
        else:
            print(f"   HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("   ❌ TIMEOUT after 10 seconds")
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Test 2: Direct function call (bypass HTTP)
    print("\n2️⃣ Testing direct function call...")
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            from github_module_puller import pull_modules_from_github
            
            start_time = time.time()
            result = pull_modules_from_github(TEST_REPO, ["18.0"])
            elapsed = time.time() - start_time
            
            print(f"   Direct call completed in {elapsed:.1f}s")
            print(f"   Modules detected: {result.get('modules_detected', 0)}")
            print(f"   Modules downloaded: {result.get('modules_downloaded', 0)}")
            print(f"   Modules processed: {result.get('modules_processed', 0)}")
            print(f"   Errors: {len(result.get('errors', []))}")
            
            if result.get('errors'):
                print("   First error:", result['errors'][0])
                
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Test 3: Check what's in the database
    print("\n3️⃣ Checking database contents...")
    try:
        from app import create_app, db
        from models import OdooModule, MigrationJob
        
        app = create_app()
        with app.app_context():
            module_count = OdooModule.query.count()
            job_count = MigrationJob.query.count()
            
            print(f"   Modules in database: {module_count}")
            print(f"   Migration jobs in database: {job_count}")
            
            if module_count > 0:
                recent_modules = OdooModule.query.limit(5).all()
                print("   Recent modules:")
                for module in recent_modules:
                    print(f"     - {module.name} (v{module.version})")
                    
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Cleanup
    try:
        flask_process.terminate()
        flask_process.wait(timeout=5)
    except:
        flask_process.kill()
    
    print("\n✅ Test completed")

if __name__ == "__main__":
    setup_and_test()
