"""
Unit tests for XML transformation rules - Week 1 completion.

Tests the 4 missing XML rules that were implemented:
1. nolabel="1" removal with CSS class addition
2. t-out to t-esc QWeb modernization  
3. Deprecated attribute removal
4. View structure modernization
"""

import unittest
import tempfile
import os
from pathlib import Path
from xml_safe_upgrader import XMLSafeUpgrader
from lxml import etree

class TestXMLTransformations(unittest.TestCase):
    """Test suite for XML transformation rules."""
    
    def setUp(self):
        """Set up test environment."""
        self.upgrader = XMLSafeUpgrader()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_test_xml(self, xml_content: str) -> str:
        """Create a temporary XML file for testing."""
        temp_file = os.path.join(self.temp_dir, 'test.xml')
        with open(temp_file, 'w') as f:
            f.write(xml_content)
        return temp_file
    
    def test_remove_nolabel_attributes(self):
        """Test XML Rule 1: Remove nolabel='1' and add CSS class."""
        xml_content = '''<?xml version="1.0" encoding="utf-8"?>
        <odoo>
            <data>
                <record id="test_view" model="ir.ui.view">
                    <field name="arch" type="xml">
                        <form>
                            <field name="name" nolabel="1"/>
                            <field name="description" nolabel="1" class="existing"/>
                        </form>
                    </field>
                </record>
            </data>
        </odoo>'''
        
        temp_file = self._create_test_xml(xml_content)
        result = self.upgrader.upgrade_xml_file(temp_file)
        
        self.assertTrue(result['success'])
        self.assertGreater(result['changes_count'], 0)
        
        # Verify the XML was transformed
        tree = etree.parse(temp_file)
        root = tree.getroot()
        
        # Check that nolabel attributes are removed
        nolabel_elements = root.xpath(".//*[@nolabel]")
        self.assertEqual(len(nolabel_elements), 0)
        
        # Check that CSS classes were added
        field_elements = root.xpath(".//field[@name='name']")
        self.assertEqual(len(field_elements), 1)
        self.assertIn('o_no_label', field_elements[0].get('class', ''))
        
        # Check existing class is preserved
        desc_elements = root.xpath(".//field[@name='description']")
        self.assertEqual(len(desc_elements), 1)
        class_attr = desc_elements[0].get('class', '')
        self.assertIn('existing', class_attr)
        self.assertIn('o_no_label', class_attr)
    
    def test_convert_tout_to_tesc(self):
        """Test XML Rule 2: Convert t-out to t-esc."""
        xml_content = '''<?xml version="1.0" encoding="utf-8"?>
        <templates>
            <t t-name="test_template">
                <div t-out="record.name"/>
                <span t-out="record.description"/>
            </t>
        </templates>'''
        
        temp_file = self._create_test_xml(xml_content)
        result = self.upgrader.upgrade_xml_file(temp_file)
        
        self.assertTrue(result['success'])
        self.assertGreater(result['changes_count'], 0)
        
        # Verify the XML was transformed
        tree = etree.parse(temp_file)
        root = tree.getroot()
        
        # Check that t-out attributes are removed
        tout_elements = root.xpath(".//*[@t-out]")
        self.assertEqual(len(tout_elements), 0)
        
        # Check that t-esc attributes were added
        tesc_elements = root.xpath(".//*[@t-esc]")
        self.assertEqual(len(tesc_elements), 2)
        
        # Verify correct values
        div_elements = root.xpath(".//div[@t-esc]")
        self.assertEqual(len(div_elements), 1)
        self.assertEqual(div_elements[0].get('t-esc'), 'record.name')
        
        span_elements = root.xpath(".//span[@t-esc]")
        self.assertEqual(len(span_elements), 1)
        self.assertEqual(span_elements[0].get('t-esc'), 'record.description')
    
    def test_remove_deprecated_view_attrs(self):
        """Test XML Rule 3: Remove deprecated view attributes."""
        xml_content = '''<?xml version="1.0" encoding="utf-8"?>
        <odoo>
            <data>
                <record id="test_view" model="ir.ui.view" version="1.0">
                    <field name="arch" type="xml">
                        <tree create="false" edit="false" delete="false">
                            <field name="name" type="char"/>
                        </tree>
                    </field>
                </record>
            </data>
        </odoo>'''
        
        temp_file = self._create_test_xml(xml_content)
        result = self.upgrader.upgrade_xml_file(temp_file)
        
        self.assertTrue(result['success'])
        self.assertGreater(result['changes_count'], 0)
        
        # Verify the XML was transformed
        tree = etree.parse(temp_file)
        root = tree.getroot()
        
        # Check that deprecated attributes are removed
        records = root.xpath(".//record[@version]")
        self.assertEqual(len(records), 0)
        
        trees = root.xpath(".//tree[@create or @edit or @delete]")
        self.assertEqual(len(trees), 0)
        
        # Check that non-deprecated attributes remain
        record = root.xpath(".//record[@id='test_view']")[0]
        self.assertEqual(record.get('model'), 'ir.ui.view')
    
    def test_modernize_view_structure(self):
        """Test XML Rule 4: Modernize view structure."""
        xml_content = '''<?xml version="1.0" encoding="utf-8"?>
        <odoo>
            <data>
                <record id="test_view" model="ir.ui.view">
                    <field name="arch" type="xml">
                        <form>
                            <notebook>
                                <page string="Page 1">
                                    <group col="2">
                                        <field name="name"/>
                                    </group>
                                    <separator string="Section"/>
                                </page>
                            </notebook>
                        </form>
                    </field>
                </record>
            </data>
        </odoo>'''
        
        temp_file = self._create_test_xml(xml_content)
        result = self.upgrader.upgrade_xml_file(temp_file)
        
        self.assertTrue(result['success'])
        self.assertGreater(result['changes_count'], 0)
        
        # Verify the XML was transformed
        tree = etree.parse(temp_file)
        root = tree.getroot()
        
        # Check notebook modernization
        notebooks = root.xpath(".//notebook[@class='oe_notebook']")
        self.assertEqual(len(notebooks), 1)
        
        # Check group modernization
        groups = root.xpath(".//group[@class='oe_group_2']")
        self.assertEqual(len(groups), 1)
        
        # Check separator modernization
        separators = root.xpath(".//separator[@title]")
        self.assertEqual(len(separators), 1)
        self.assertEqual(separators[0].get('title'), 'Section')
        
        # Ensure string attribute was removed
        separators_with_string = root.xpath(".//separator[@string]")
        self.assertEqual(len(separators_with_string), 0)
    
    def test_comprehensive_transformation(self):
        """Test all XML rules working together."""
        xml_content = '''<?xml version="1.0" encoding="utf-8"?>
        <odoo>
            <data>
                <record id="test_view" model="ir.ui.view" version="1.0">
                    <field name="arch" type="xml">
                        <form create="false">
                            <notebook>
                                <page string="Details">
                                    <group col="2">
                                        <field name="name" nolabel="1"/>
                                        <separator string="Description"/>
                                    </group>
                                </page>
                            </notebook>
                        </form>
                    </field>
                </record>
            </data>
            <template id="test_template">
                <div t-out="record.name" class="test"/>
            </template>
        </odoo>'''
        
        temp_file = self._create_test_xml(xml_content)
        result = self.upgrader.upgrade_xml_file(temp_file)
        
        self.assertTrue(result['success'])
        self.assertGreater(result['changes_count'], 4)  # Multiple transformations
        
        # Verify all transformations were applied
        tree = etree.parse(temp_file)
        root = tree.getroot()
        
        # Rule 1: nolabel removed, class added
        field_elements = root.xpath(".//field[@name='name']")
        self.assertEqual(len(field_elements), 1)
        self.assertIn('o_no_label', field_elements[0].get('class', ''))
        
        # Rule 2: t-out converted to t-esc
        tesc_elements = root.xpath(".//*[@t-esc='record.name']")
        self.assertEqual(len(tesc_elements), 1)
        
        # Rule 3: deprecated attributes removed
        deprecated_records = root.xpath(".//record[@version]")
        self.assertEqual(len(deprecated_records), 0)
        
        # Rule 4: modern structure
        notebooks = root.xpath(".//notebook[@class='oe_notebook']")
        self.assertEqual(len(notebooks), 1)

if __name__ == '__main__':
    unittest.main()