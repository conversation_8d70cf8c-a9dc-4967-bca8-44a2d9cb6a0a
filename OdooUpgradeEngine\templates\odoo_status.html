{% extends "base.html" %}
{% set title = "Odoo Status" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-server me-3"></i>
            Odoo Installation Status
        </h1>
        <p class="lead">Monitor and manage your Odoo installation</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-body">
                {% if installation %}
                    <!-- Installation Status -->
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3">
                            {% if installation.status == 'active' %}
                                <i class="fas fa-check-circle fa-4x text-success"></i>
                            {% elif installation.status == 'installing' %}
                                <i class="fas fa-spinner fa-spin fa-4x text-warning"></i>
                            {% elif installation.status == 'error' %}
                                <i class="fas fa-exclamation-triangle fa-4x text-danger"></i>
                            {% else %}
                                <i class="fas fa-server fa-4x text-secondary"></i>
                            {% endif %}
                        </div>
                        <div>
                            <h3>
                                Status: 
                                <span class="badge bg-{{ 'success' if installation.status == 'active' else 'warning' if installation.status == 'installing' else 'danger' if installation.status == 'error' else 'secondary' }} fs-6">
                                    {{ installation.status.title() }}
                                </span>
                            </h3>
                            <p class="text-muted mb-0">
                                Odoo {{ installation.version }} 
                                - Installed {{ installation.created_at.strftime('%Y-%m-%d %H:%M') if installation.created_at else 'Unknown' }}
                            </p>
                        </div>
                    </div>

                    <!-- Installation Details -->
                    {% if installation.status == 'active' and installation.config_data %}
                        <div class="mb-4">
                            <h5>
                                <i class="fas fa-cog me-2"></i>
                                Configuration
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Installation Path:</strong></td>
                                            <td><code>{{ installation.config_data.odoo_path }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Virtual Environment:</strong></td>
                                            <td><code>{{ installation.config_data.venv_path }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Config File:</strong></td>
                                            <td><code>{{ installation.config_data.config_path }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Addons Path:</strong></td>
                                            <td><code>{{ installation.config_data.addons_path }}</code></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Database Host:</strong></td>
                                            <td>{{ installation.config_data.database_host }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Database Port:</strong></td>
                                            <td>{{ installation.config_data.database_port }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Database User:</strong></td>
                                            <td>{{ installation.config_data.database_user }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Database Name:</strong></td>
                                            <td>{{ installation.database_name }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Error Details -->
                    {% if installation.status == 'error' and installation.error_log %}
                        <div class="mb-4">
                            <h5 class="text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Installation Error
                            </h5>
                            <div class="alert alert-danger">
                                <pre class="mb-0">{{ installation.error_log }}</pre>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Actions -->
                    <div class="mb-3">
                        <h5>
                            <i class="fas fa-tools me-2"></i>
                            Actions
                        </h5>
                        <div class="d-flex gap-2 flex-wrap">
                            {% if installation.status == 'error' or not installation.status == 'active' %}
                                <form action="{{ url_for('main.install_odoo') }}" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-download me-1"></i>
                                        {{ 'Retry Installation' if installation.status == 'error' else 'Install Odoo' }}
                                    </button>
                                </form>
                            {% endif %}
                            
                            {% if installation.status == 'active' %}
                                <button class="btn btn-success" onclick="openOdoo()">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    Open Odoo (Port 8069)
                                </button>
                                
                                <button class="btn btn-outline-warning" onclick="restartOdoo()">
                                    <i class="fas fa-redo me-1"></i>
                                    Restart Odoo
                                </button>
                            {% endif %}
                            
                            <button class="btn btn-outline-secondary" onclick="checkStatus()">
                                <i class="fas fa-sync-alt me-1"></i>
                                Refresh Status
                            </button>
                        </div>
                    </div>
                {% else %}
                    <!-- No Installation -->
                    <div class="text-center py-5">
                        <i class="fas fa-server fa-4x text-muted mb-3"></i>
                        <h3>Odoo Not Installed</h3>
                        <p class="text-muted mb-4">Install Odoo Community Edition to start analyzing modules.</p>
                        
                        <form action="{{ url_for('main.install_odoo') }}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-download me-2"></i>
                                Install Odoo
                            </button>
                        </form>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- System Requirements -->
        <div class="card mb-3">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-list-check me-2"></i>
                    System Requirements
                </h5>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Python 3.8+
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        PostgreSQL Database
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        4+ GB RAM
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        10+ GB Disk Space
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Internet Connection
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Installation Notes -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Installation Notes
                </h5>
                <div class="alert alert-info mb-3">
                    <i class="fas fa-clock me-2"></i>
                    Installation typically takes 5-10 minutes depending on your internet connection.
                </div>
                
                <h6>What gets installed:</h6>
                <ul class="small">
                    <li>Python virtual environment</li>
                    <li>Odoo Community Edition (Latest)</li>
                    <li>Required Python dependencies</li>
                    <li>System packages (via apt)</li>
                    <li>PostgreSQL database setup</li>
                    <li>Odoo configuration file</li>
                </ul>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Note:</strong> Installation requires sudo privileges for system packages.
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openOdoo() {
    window.open('http://localhost:8069', '_blank');
}

function restartOdoo() {
    alert('Restart functionality would be implemented here');
}

function checkStatus() {
    location.reload();
}

// Auto-refresh if installation is in progress
{% if installation and installation.status == 'installing' %}
setTimeout(function() {
    location.reload();
}, 30000); // Refresh every 30 seconds during installation
{% endif %}
</script>
{% endblock %}
