#!/usr/bin/env python3
"""
Implementation Validation Script
Validates that all required files and features are properly implemented
"""

import os
import json
import sys
from datetime import datetime
from typing import List, Dict, Any

class ImplementationValidator:
    def __init__(self):
        self.validation_results = []
        self.critical_files = []
        self.optional_files = []
        
    def log_validation(self, category: str, item: str, success: bool, message: str = ""):
        """Log validation result"""
        result = {
            'category': category,
            'item': item,
            'success': success,
            'message': message
        }
        self.validation_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"  {status} {item}: {message}")
    
    def validate_core_files(self):
        """Validate core application files"""
        print("📁 Validating Core Files")
        
        core_files = [
            ('app.py', 'Main Flask application'),
            ('routes.py', 'Application routes'),
            ('models.py', 'Database models'),
            ('ai_provider_manager.py', 'AI provider management'),
            ('ai_learning_system.py', 'AI learning system'),
            ('start_application.py', 'Application starter'),
            ('requirements.txt', 'Python dependencies')
        ]
        
        for file_path, description in core_files:
            if os.path.exists(file_path):
                # Check file size to ensure it's not empty
                file_size = os.path.getsize(file_path)
                if file_size > 100:  # At least 100 bytes
                    self.log_validation("Core Files", description, True, f"Present ({file_size} bytes)")
                else:
                    self.log_validation("Core Files", description, False, "File too small or empty")
            else:
                self.log_validation("Core Files", description, False, "File missing")
    
    def validate_templates(self):
        """Validate template files"""
        print("\n🎨 Validating Templates")
        
        required_templates = [
            ('templates/base.html', 'Base template'),
            ('templates/index.html', 'Dashboard'),
            ('templates/upload_modules.html', 'Upload modules'),
            ('templates/migration_jobs.html', 'Migration jobs'),
            ('templates/ai_providers.html', 'AI providers'),
            ('templates/ai_learning_dashboard.html', 'AI learning dashboard'),
            ('templates/migration_results.html', 'Migration results'),
            ('templates/review_queue.html', 'Review queue'),
            ('templates/completed_migrations.html', 'Completed migrations'),
            ('templates/success_reports.html', 'Success reports'),
            ('templates/performance_analytics.html', 'Performance analytics'),
            ('templates/migration_history.html', 'Migration history'),
            ('templates/test_results.html', 'Test results'),
            ('templates/system_settings.html', 'System settings')
        ]
        
        for template_path, description in required_templates:
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if len(content) > 500:  # Reasonable template size
                        self.log_validation("Templates", description, True, f"Present ({len(content)} chars)")
                    else:
                        self.log_validation("Templates", description, False, "Template too small")
            else:
                self.log_validation("Templates", description, False, "Template missing")
    
    def validate_static_files(self):
        """Validate static files (CSS, JS)"""
        print("\n💻 Validating Static Files")
        
        static_files = [
            ('static/js/main.js', 'Main JavaScript'),
            ('static/js/ai-integration.js', 'AI integration JavaScript'),
            ('static/js/real-time-updates.js', 'Real-time updates JavaScript'),
            ('static/js/search-filter.js', 'Search and filter JavaScript'),
            ('static/css/custom.css', 'Custom CSS (optional)')
        ]
        
        for file_path, description in static_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                if file_size > 100:
                    self.log_validation("Static Files", description, True, f"Present ({file_size} bytes)")
                else:
                    self.log_validation("Static Files", description, False, "File too small")
            else:
                # CSS file is optional
                if 'optional' in description.lower():
                    self.log_validation("Static Files", description, True, "Optional file not present")
                else:
                    self.log_validation("Static Files", description, False, "File missing")
    
    def validate_routes_implementation(self):
        """Validate that key routes are implemented"""
        print("\n🛣️  Validating Routes Implementation")
        
        try:
            with open('routes.py', 'r') as f:
                routes_content = f.read()
            
            required_routes = [
                ('@main_routes.route(\'/\')', 'Dashboard route'),
                ('@main_routes.route(\'/upload_modules\')', 'Upload modules route'),
                ('@main_routes.route(\'/migration_jobs\')', 'Migration jobs route'),
                ('@main_routes.route(\'/ai_providers\')', 'AI providers route'),
                ('@main_routes.route(\'/ai_learning_dashboard\')', 'AI learning dashboard route'),
                ('/api/dashboard-data', 'Dashboard data API'),
                ('/api/migration-status', 'Migration status API'),
                ('/api/test-ai-provider', 'AI provider testing API'),
                ('/api/ai-learning-insights', 'AI learning insights API')
            ]
            
            for route_pattern, description in required_routes:
                if route_pattern in routes_content:
                    self.log_validation("Routes", description, True, "Route implemented")
                else:
                    self.log_validation("Routes", description, False, "Route missing")
                    
        except FileNotFoundError:
            self.log_validation("Routes", "routes.py file", False, "Routes file not found")
    
    def validate_ai_integration(self):
        """Validate AI integration components"""
        print("\n🤖 Validating AI Integration")
        
        # Check AI provider manager
        if os.path.exists('ai_provider_manager.py'):
            with open('ai_provider_manager.py', 'r') as f:
                ai_content = f.read()
            
            ai_features = [
                ('class AIProviderManager', 'AI provider manager class'),
                ('def test_deepseek_connection', 'DeepSeek testing'),
                ('def test_openai_connection', 'OpenAI testing'),
                ('def test_ollama_connection', 'Ollama testing'),
                ('def generate_migration_analysis', 'Migration analysis generation')
            ]
            
            for feature, description in ai_features:
                if feature in ai_content:
                    self.log_validation("AI Integration", description, True, "Feature implemented")
                else:
                    self.log_validation("AI Integration", description, False, "Feature missing")
        else:
            self.log_validation("AI Integration", "AI provider manager", False, "File missing")
        
        # Check AI learning system
        if os.path.exists('ai_learning_system.py'):
            with open('ai_learning_system.py', 'r') as f:
                learning_content = f.read()
            
            learning_features = [
                ('class AILearningSystem', 'AI learning system class'),
                ('def record_ai_interaction', 'Interaction recording'),
                ('def record_human_feedback', 'Feedback recording'),
                ('def get_learning_insights', 'Learning insights')
            ]
            
            for feature, description in learning_features:
                if feature in learning_content:
                    self.log_validation("AI Learning", description, True, "Feature implemented")
                else:
                    self.log_validation("AI Learning", description, False, "Feature missing")
        else:
            self.log_validation("AI Learning", "AI learning system", False, "File missing")
    
    def validate_database_models(self):
        """Validate database models"""
        print("\n🗄️  Validating Database Models")
        
        if os.path.exists('models.py'):
            with open('models.py', 'r') as f:
                models_content = f.read()
            
            required_models = [
                ('class OdooModule', 'Odoo module model'),
                ('class MigrationJob', 'Migration job model'),
                ('class ModuleAnalysis', 'Module analysis model'),
                ('class HealthCheck', 'Health check model')
            ]
            
            for model, description in required_models:
                if model in models_content:
                    self.log_validation("Database Models", description, True, "Model defined")
                else:
                    self.log_validation("Database Models", description, False, "Model missing")
        else:
            self.log_validation("Database Models", "models.py file", False, "Models file not found")
    
    def validate_test_files(self):
        """Validate test files"""
        print("\n🧪 Validating Test Files")
        
        test_files = [
            ('comprehensive_system_test.py', 'Comprehensive system test'),
            ('user_workflow_test.py', 'User workflow test'),
            ('performance_test.py', 'Performance test'),
            ('run_all_tests.py', 'Master test runner')
        ]
        
        for file_path, description in test_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                if file_size > 1000:  # Test files should be substantial
                    self.log_validation("Test Files", description, True, f"Present ({file_size} bytes)")
                else:
                    self.log_validation("Test Files", description, False, "Test file too small")
            else:
                self.log_validation("Test Files", description, False, "Test file missing")
    
    def validate_documentation(self):
        """Validate documentation files"""
        print("\n📚 Validating Documentation")
        
        doc_files = [
            ('README.md', 'Main README'),
            ('ZOE_100X_ENGINEER_COMPREHENSIVE_PLAN.md', 'Implementation plan'),
            ('COMPREHENSIVE_WORKFLOW_ANALYSIS.md', 'Workflow analysis'),
            ('IMPLEMENTATION_STARTER_KIT.md', 'Implementation guide')
        ]
        
        for file_path, description in doc_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                if file_size > 500:
                    self.log_validation("Documentation", description, True, f"Present ({file_size} bytes)")
                else:
                    self.log_validation("Documentation", description, False, "Documentation too small")
            else:
                self.log_validation("Documentation", description, False, "Documentation missing")
    
    def run_validation(self):
        """Run complete validation"""
        print("🔍 IMPLEMENTATION VALIDATION")
        print("=" * 50)
        
        # Run all validation categories
        self.validate_core_files()
        self.validate_templates()
        self.validate_static_files()
        self.validate_routes_implementation()
        self.validate_ai_integration()
        self.validate_database_models()
        self.validate_test_files()
        self.validate_documentation()
        
        # Generate validation summary
        self.generate_validation_summary()
    
    def generate_validation_summary(self):
        """Generate validation summary"""
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        # Count results by category
        categories = {}
        for result in self.validation_results:
            category = result['category']
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0}
            categories[category]['total'] += 1
            if result['success']:
                categories[category]['passed'] += 1
        
        # Display category results
        overall_passed = 0
        overall_total = 0
        
        for category, stats in categories.items():
            passed = stats['passed']
            total = stats['total']
            percentage = (passed / total) * 100 if total > 0 else 0
            
            status = "✅" if percentage == 100 else "⚠️" if percentage >= 80 else "❌"
            print(f"{status} {category}: {passed}/{total} ({percentage:.1f}%)")
            
            overall_passed += passed
            overall_total += total
        
        # Overall assessment
        overall_percentage = (overall_passed / overall_total) * 100 if overall_total > 0 else 0
        
        print(f"\n🎯 Overall Implementation: {overall_passed}/{overall_total} ({overall_percentage:.1f}%)")
        
        if overall_percentage >= 95:
            print("🎉 IMPLEMENTATION STATUS: COMPLETE")
            print("✅ All critical components are properly implemented")
        elif overall_percentage >= 85:
            print("👍 IMPLEMENTATION STATUS: MOSTLY COMPLETE")
            print("🔧 Minor components missing but system is functional")
        elif overall_percentage >= 70:
            print("⚠️  IMPLEMENTATION STATUS: PARTIALLY COMPLETE")
            print("🔧 Some important components missing")
        else:
            print("🚨 IMPLEMENTATION STATUS: INCOMPLETE")
            print("❌ Major components missing")
        
        # Show failed validations
        failed_validations = [r for r in self.validation_results if not r['success']]
        if failed_validations:
            print(f"\n❌ FAILED VALIDATIONS ({len(failed_validations)}):")
            for validation in failed_validations:
                print(f"   - {validation['category']}: {validation['item']} - {validation['message']}")
        
        # Save validation report
        self.save_validation_report(overall_passed, overall_total, overall_percentage)
    
    def save_validation_report(self, passed, total, percentage):
        """Save validation report"""
        report = {
            'validation_summary': {
                'timestamp': str(datetime.now()),
                'total_validations': total,
                'passed_validations': passed,
                'success_rate': percentage,
                'status': 'COMPLETE' if percentage >= 95 else 'MOSTLY_COMPLETE' if percentage >= 85 else 'PARTIALLY_COMPLETE' if percentage >= 70 else 'INCOMPLETE'
            },
            'detailed_results': self.validation_results
        }
        
        with open('implementation_validation_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Validation report saved to: implementation_validation_report.json")

def main():
    """Main validation execution"""
    validator = ImplementationValidator()
    validator.run_validation()

if __name__ == "__main__":
    main()
