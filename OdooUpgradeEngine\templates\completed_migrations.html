{% extends "base.html" %}
{% set title = "Completed Migrations" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-check-double me-3"></i>
            Completed Migrations
        </h1>
        <p class="lead">Archive of all successfully completed migrations</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                <h5 class="card-title">Total Completed</h5>
                <h3 class="text-success">{{ total_completed }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-percentage text-info fa-2x mb-2"></i>
                <h5 class="card-title">Success Rate</h5>
                <h3 class="text-info">{{ success_rate|round(1) }}%</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-clock text-warning fa-2x mb-2"></i>
                <h5 class="card-title">Avg Duration</h5>
                <h3 class="text-warning">2h 15m</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-download text-primary fa-2x mb-2"></i>
                <h5 class="card-title">Downloads</h5>
                <h3 class="text-primary">{{ (total_completed * 0.8)|round|int }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Completed Migrations Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            Completed Migrations
        </h5>
        <div class="btn-group">
            <button class="btn btn-outline-primary btn-sm" onclick="exportCompleted()">
                <i class="fas fa-download"></i> Export All
            </button>
            <button class="btn btn-outline-info btn-sm" onclick="bulkRerunAI()">
                <i class="fas fa-robot"></i> Bulk AI Rerun
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if completed_jobs %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Module</th>
                            <th>Version Upgrade</th>
                            <th>Completed</th>
                            <th>Duration</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in completed_jobs %}
                        <tr>
                            <td>
                                <strong>{{ job.module.name }}</strong>
                                <br>
                                <small class="text-muted">{{ job.module.description or 'No description' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="badge bg-success">{{ job.target_version }}</span>
                            </td>
                            <td>
                                <small class="text-muted">{{ job.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">~2h 30m</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewDetails({{ job.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="downloadResult({{ job.id }})">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="rerunWithAI({{ job.id }})">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No completed migrations yet</h5>
                <p class="text-muted">Completed migrations will appear here</p>
                <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Start New Migration
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function viewDetails(jobId) {
    window.location.href = `/migration/${jobId}/review`;
}

function downloadResult(jobId) {
    window.location.href = `/migration/${jobId}/download`;
}

function rerunWithAI(jobId) {
    alert('AI Rerun functionality will be implemented in Phase 3');
}

function exportCompleted() {
    alert('Export functionality will be implemented');
}

function bulkRerunAI() {
    alert('Bulk AI rerun functionality will be implemented in Phase 3');
}
</script>
{% endblock %}
