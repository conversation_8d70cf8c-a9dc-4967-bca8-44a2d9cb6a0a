{% extends "base.html" %}

{% block title %}Migration Jobs - Odoo Module Analysis Platform{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Migration Jobs</h2>
                <div>
                    <button class="btn btn-outline-secondary" onclick="refreshJobs()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Migration
                    </a>
                </div>
            </div>

            <!-- Search and Filter Controls -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="job-search" class="form-label">Search Jobs</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="job-search"
                                       data-search-target="[data-filterable]" data-search-delay="300"
                                       placeholder="Search by module name, status...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="status-filter" class="form-label">Status</label>
                            <select class="form-select" id="status-filter" data-filter-target="true" data-filter-type="status">
                                <option value="">All Statuses</option>
                                <option value="QUEUED">Queued</option>
                                <option value="ANALYSIS">Analysis</option>
                                <option value="CODE_TRANSFORMATION">Code Transform</option>
                                <option value="VERSION_UPDATE">Version Update</option>
                                <option value="VISUAL_DIFF">Visual Diff</option>
                                <option value="AWAITING_APPROVAL">Awaiting Approval</option>
                                <option value="TESTING">Testing</option>
                                <option value="COMPLETED">Completed</option>
                                <option value="FAILED">Failed</option>
                                <option value="ERROR">Error</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="version-filter" class="form-label">Target Version</label>
                            <select class="form-select" id="version-filter" data-filter-target="true" data-filter-type="version">
                                <option value="">All Versions</option>
                                <option value="18.0">18.0</option>
                                <option value="17.0">17.0</option>
                                <option value="16.0">16.0</option>
                                <option value="15.0">15.0</option>
                                <option value="14.0">14.0</option>
                                <option value="13.0">13.0</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="sort-jobs" class="form-label">Sort By</label>
                            <select class="form-select" id="sort-jobs" data-sort-target="#jobs-container">
                                <option value="created_at">Newest First</option>
                                <option value="name">Module Name</option>
                                <option value="status">Status</option>
                                <option value="updated_at">Last Updated</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-secondary me-2" id="clear-filters">
                                <i class="fas fa-times"></i> Clear
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="export-filtered">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <!-- Active Filters Display -->
                    <div id="active-filters" class="mt-2"></div>

                    <!-- Results Counter -->
                    <div class="mt-2">
                        <small class="text-muted" id="search-results-count">Loading...</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Total Jobs</h6>
                            <h3 class="mb-0" id="total-jobs">-</h3>
                        </div>
                        <i class="fas fa-tasks fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Completed</h6>
                            <h3 class="mb-0" id="completed-jobs">-</h3>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">In Progress</h6>
                            <h3 class="mb-0" id="running-jobs">-</h3>
                        </div>
                        <i class="fas fa-cog fa-spin fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Failed</h6>
                            <h3 class="mb-0" id="failed-jobs">-</h3>
                        </div>
                        <i class="fas fa-exclamation-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Jobs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Migration Jobs</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="jobs-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Module</th>
                                    <th>Version</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Phase</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="jobs-tbody" data-container="jobs-container">
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Loading migration jobs...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="jobDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Job Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="job-details-content">
                <!-- Job details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="view-diff-btn" onclick="viewDiff()">
                    <i class="fas fa-eye"></i> View Diff
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentJobId = null;
let jobsData = [];

function refreshJobs() {
    loadMigrationJobs();
}

function loadMigrationJobs() {
    fetch('/api/migration-jobs')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                jobsData = data.jobs;
                updateJobsTable(data.jobs);
                updateJobStatistics(data.jobs);
            } else {
                console.error('Failed to load jobs:', data.error);
                showError('Failed to load migration jobs');
            }
        })
        .catch(error => {
            console.error('Error loading jobs:', error);
            showError('Error loading migration jobs');
        });
}

function updateJobsTable(jobs) {
    const tbody = document.getElementById('jobs-tbody');
    
    if (jobs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No migration jobs found</p>
                    <a href="{{ url_for('main.upload_modules_page') }}" class="btn btn-primary">
                        Create First Migration Job
                    </a>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = jobs.map(job => `
        <tr data-job-id="${job.id}" data-filterable="true" data-sortable="true"
            data-filter-status="${job.status}"
            data-filter-version="${job.target_version}"
            data-sort-created_at="${job.created_at}"
            data-sort-name="${job.module_name}"
            data-sort-status="${job.status}"
            data-sort-updated_at="${job.updated_at || job.created_at}">
            <td data-export="id">${job.id}</td>
            <td data-searchable="true" data-export="module_name">
                <strong>${job.module_name}</strong>
            </td>
            <td data-export="version">
                <span class="badge bg-info">${job.source_version}</span>
                <i class="fas fa-arrow-right mx-1"></i>
                <span class="badge bg-success">${job.target_version}</span>
            </td>
            <td data-searchable="true" data-export="status">
                <span class="badge ${getStatusBadgeClass(job.status)}">${job.status}</span>
            </td>
            <td data-export="progress">
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar ${getProgressBarClass(job.status)}"
                         role="progressbar"
                         style="width: ${job.progress || 0}%"
                         aria-valuenow="${job.progress || 0}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        ${job.progress || 0}%
                    </div>
                </div>
            </td>
            <td data-export="phase">${job.current_phase || '-'}</td>
            <td data-export="created_at">${formatDateTime(job.created_at)}</td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewJobDetails(${job.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewJobDiff(${job.id})" title="View Diff">
                        <i class="fas fa-code-branch"></i>
                    </button>
                    ${job.status !== 'COMPLETED' && job.status !== 'FAILED' && job.status !== 'CANCELLED' ?
                        `<button class="btn btn-sm btn-outline-warning" onclick="cancelJob(${job.id})" title="Cancel Job">
                            <i class="fas fa-stop"></i>
                        </button>` : ''
                    }
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteJob(${job.id}, '${job.module_name}')" title="Delete Job">
                        <i class="fas fa-trash"></i>
                    </button>
                    ${job.status === 'FAILED' || job.status === 'ERROR' ?
                        `<button class="btn btn-sm btn-outline-success" onclick="rerunWithAI(${job.id})" title="Rerun with AI">
                            <i class="fas fa-robot"></i>
                        </button>` : ''
                    }
                </div>

                <!-- AI Integration Buttons -->
                <div class="btn-group mt-1" role="group" aria-label="AI Actions">
                    ${job.status === 'COMPLETED' ?
                        `<button class="btn btn-sm btn-info" onclick="rerunWithAI(${job.id})" title="Rerun this migration with AI analysis for improvements">
                            <i class="fas fa-robot me-1"></i>AI Rerun
                        </button>` : ''
                    }
                    ${job.status === 'FAILED' ?
                        `<button class="btn btn-sm btn-warning" onclick="aiAnalyzeFailure(${job.id})" title="Use AI to analyze failure and suggest fixes">
                            <i class="fas fa-brain me-1"></i>AI Analyze
                        </button>` : ''
                    }
                    <!-- Always show AI suggestions -->
                    <button class="btn btn-sm btn-outline-info" onclick="showAISuggestions(${job.id})" title="View AI recommendations for this migration">
                        <i class="fas fa-lightbulb me-1"></i>AI Tips
                    </button>
                    ${job.status === 'COMPLETED' ?
                        `<button class="btn btn-sm btn-outline-success" onclick="compareWithAI(${job.id})" title="Compare results with AI analysis">
                            <i class="fas fa-balance-scale me-1"></i>AI Compare
                        </button>` : ''
                    }
                </div>
            </td>
        </tr>
    `).join('');
}

function updateJobStatistics(jobs) {
    const total = jobs.length;
    const completed = jobs.filter(j => j.status === 'COMPLETED').length;
    const running = jobs.filter(j => [
        'QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION',
        'PYTHON_TRANSFORMATION', 'XML_TRANSFORMATION', 'VISUAL_DIFF',
        'AWAITING_APPROVAL', 'AI_ANALYSIS', 'DIFF_APPROVED'
    ].includes(j.status)).length;
    const failed = jobs.filter(j => ['FAILED', 'CANCELLED'].includes(j.status)).length;
    
    document.getElementById('total-jobs').textContent = total;
    document.getElementById('completed-jobs').textContent = completed;
    document.getElementById('running-jobs').textContent = running;
    document.getElementById('failed-jobs').textContent = failed;
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'QUEUED': 'bg-secondary',
        'ANALYSIS': 'bg-info',
        'CODE_TRANSFORMATION': 'bg-warning',
        'PYTHON_TRANSFORMATION': 'bg-warning',
        'XML_TRANSFORMATION': 'bg-warning',
        'VISUAL_DIFF': 'bg-info',
        'AWAITING_APPROVAL': 'bg-primary',
        'AI_ANALYSIS': 'bg-warning',
        'DIFF_APPROVED': 'bg-info',
        'MANUAL_INTERVENTION': 'bg-warning',
        'COMPLETED': 'bg-success',
        'FAILED': 'bg-danger',
        'CANCELLED': 'bg-secondary'
    };
    return statusClasses[status] || 'bg-secondary';
}

function getProgressBarClass(status) {
    const statusClasses = {
        'QUEUED': 'bg-secondary',
        'ANALYSIS': 'bg-info',
        'CODE_TRANSFORMATION': 'bg-warning',
        'PYTHON_TRANSFORMATION': 'bg-warning',
        'XML_TRANSFORMATION': 'bg-warning',
        'VISUAL_DIFF': 'bg-info',
        'AWAITING_APPROVAL': 'bg-primary',
        'AI_ANALYSIS': 'bg-info',
        'DIFF_APPROVED': 'bg-info',
        'MANUAL_INTERVENTION': 'bg-warning',
        'COMPLETED': 'bg-success',
        'FAILED': 'bg-danger',
        'CANCELLED': 'bg-secondary'
    };
    return statusClasses[status] || 'bg-secondary';
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function viewJobDetails(jobId) {
    currentJobId = jobId;
    
    fetch(`/api/job-status/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showJobDetails(data.job);
            } else {
                showError('Failed to load job details');
            }
        })
        .catch(error => {
            console.error('Error loading job details:', error);
            showError('Error loading job details');
        });
}

function showJobDetails(job) {
    // Debug logging to help troubleshoot
    console.log('Job details data:', job);

    const content = document.getElementById('job-details-content');

    // Safely extract values with proper fallbacks
    const sourceVersion = job.source_version || 'Unknown';
    const progress = typeof job.progress === 'number' ? job.progress : (typeof job.progress_percentage === 'number' ? job.progress_percentage : 0);
    const filesCount = typeof job.files_count === 'number' ? job.files_count : 'Not calculated';
    const interventionsCount = typeof job.interventions_count === 'number' ? job.interventions_count : 0;

    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Job ID:</strong></td><td>${job.id}</td></tr>
                    <tr><td><strong>Module:</strong></td><td>${job.module_name}</td></tr>
                    <tr><td><strong>Source Version:</strong></td><td>${sourceVersion}</td></tr>
                    <tr><td><strong>Target Version:</strong></td><td>${job.target_version}</td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(job.status)}">${job.status}</span></td></tr>
                    <tr><td><strong>Progress:</strong></td><td>${progress}%</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Timeline</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>Created:</strong></td><td>${formatDateTime(job.created_at)}</td></tr>
                    <tr><td><strong>Updated:</strong></td><td>${formatDateTime(job.updated_at)}</td></tr>
                    <tr><td><strong>Current Phase:</strong></td><td>${job.current_phase || '-'}</td></tr>
                    <tr><td><strong>Files Count:</strong></td><td>${filesCount}</td></tr>
                    <tr><td><strong>Interventions:</strong></td><td>${interventionsCount}</td></tr>
                </table>
            </div>
        </div>
        
        <!-- Week 4: Semantic Analysis Results -->
        ${job.semantic_analysis_data ? `
            <div class="mt-4">
                <h6>📊 Semantic Analysis Results <span class="badge bg-info">Week 4</span></h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h6 class="card-title">Quality Metrics</h6>
                                <div class="mb-2">
                                    <small class="text-muted">Overall Quality Score</small>
                                    <div class="progress">
                                        <div class="progress-bar ${getQualityColor(job.semantic_analysis_data.overall_quality_score)}" 
                                             style="width: ${(job.semantic_analysis_data.overall_quality_score * 100).toFixed(0)}%">
                                            ${(job.semantic_analysis_data.overall_quality_score * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Code Maintainability</small>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" 
                                             style="width: ${(job.semantic_analysis_data.code_maintainability * 100).toFixed(0)}%">
                                            ${(job.semantic_analysis_data.code_maintainability * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Business Logic Integrity</small>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" 
                                             style="width: ${(job.semantic_analysis_data.business_logic_integrity * 100).toFixed(0)}%">
                                            ${(job.semantic_analysis_data.business_logic_integrity * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-secondary">
                            <div class="card-body">
                                <h6 class="card-title">Analysis Summary</h6>
                                <p class="mb-2"><strong>Confidence:</strong> 
                                    <span class="badge ${getConfidenceBadge(job.semantic_analysis_data.confidence_level)}">
                                        ${job.semantic_analysis_data.confidence_level}
                                    </span>
                                </p>
                                <p class="mb-2"><strong>Issues Found:</strong> ${job.semantic_analysis_data.semantic_issues ? job.semantic_analysis_data.semantic_issues.length : 0}</p>
                                <p class="small text-muted">${job.semantic_analysis_data.analysis_summary || 'Analysis completed successfully'}</p>
                                ${job.semantic_analysis_data.semantic_issues && job.semantic_analysis_data.semantic_issues.length > 0 ? `
                                    <hr>
                                    <h6 class="small">Top Issues:</h6>
                                    ${job.semantic_analysis_data.semantic_issues.slice(0, 3).map(issue => `
                                        <div class="alert alert-${getSeverityColor(issue.severity)} alert-sm">
                                            <small><strong>${issue.type}:</strong> ${issue.description}</small>
                                        </div>
                                    `).join('')}
                                ` : ''}
                            </div>
                        </div>
                    </div>
                                </div>
            </div>
        ` : ''}
        
        ${job.error_message ? `
            <div class="alert alert-danger mt-3">
                <h6>Error Message</h6>
                <pre>${job.error_message}</pre>
            </div>
        ` : ''}
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('jobDetailsModal'));
    modal.show();
}

function viewJobDiff(jobId) {
    fetch(`/api/visual-diff/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.open(data.diff_url, '_blank');
            } else {
                showError('Failed to generate diff report');
            }
        })
        .catch(error => {
            console.error('Error generating diff:', error);
            showError('Error generating diff report');
        });
}

function viewDiff() {
    if (currentJobId) {
        viewJobDiff(currentJobId);
    }
}

function cancelJob(jobId) {
    if (!confirm('Are you sure you want to cancel this migration job?')) {
        return;
    }
    
    fetch(`/api/cancel-job/${jobId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Job cancelled successfully');
            loadMigrationJobs(); // Refresh the table
        } else {
            showError('Failed to cancel job: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error cancelling job:', error);
        showError('Error cancelling job');
    });
}

function showError(message) {
    // Simple error display - could be enhanced with toast notifications
    alert('Error: ' + message);
}

function showSuccess(message) {
    // Simple success display - could be enhanced with toast notifications
    alert('Success: ' + message);
}

// Auto-refresh jobs every 30 seconds
setInterval(loadMigrationJobs, 30000);

// Load jobs on page load
document.addEventListener('DOMContentLoaded', function() {
    loadMigrationJobs();
    
    // Check for highlight parameter
    const urlParams = new URLSearchParams(window.location.search);
    const highlightJobId = urlParams.get('highlight');
    if (highlightJobId) {
        setTimeout(() => highlightJob(highlightJobId), 1000);
    }
});

function highlightJob(jobId) {
    const row = document.querySelector(`tr[data-job-id="${jobId}"]`);
    if (row) {
        row.classList.add('table-warning');
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Remove highlight after 3 seconds
        setTimeout(() => {
            row.classList.remove('table-warning');
        }, 3000);
    }
}

function deleteJob(jobId, moduleName) {
    if (confirm(`Delete migration job for "${moduleName}"? This action cannot be undone.`)) {
        fetch(`/api/delete-job/${jobId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Migration job for "${moduleName}" deleted successfully`);
                refreshJobs();
            } else {
                alert('Error deleting job: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error deleting job: ' + error.message);
        });
    }
}

// Week 4: Helper functions for semantic analysis display
function getQualityColor(score) {
    if (score >= 0.8) return 'bg-success';
    if (score >= 0.6) return 'bg-warning';
    return 'bg-danger';
}

function getConfidenceBadge(confidence) {
    switch (confidence?.toLowerCase()) {
        case 'high': return 'bg-success';
        case 'medium': return 'bg-warning';
        default: return 'bg-danger';
    }
}

function getSeverityColor(severity) {
    switch (severity?.toLowerCase()) {
        case 'low': return 'info';
        case 'medium': return 'warning';
        case 'high': return 'danger';
        case 'critical': return 'danger';
        default: return 'secondary';
    }
}

// ===== AI INTEGRATION FUNCTIONS =====

function rerunWithAI(jobId) {
    if (confirm('Rerun this migration with AI analysis for improvements?')) {
        fetch(`/api/ai-rerun-migration/${jobId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('AI-enhanced migration started successfully!');
                loadMigrationJobs(); // Refresh the jobs list
            } else {
                alert('Failed to start AI migration: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error starting AI migration: ' + error.message);
        });
    }
}

function aiAnalyzeFailure(jobId) {
    // Show loading indicator
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Analyzing...';
    button.disabled = true;

    fetch(`/api/ai-analyze-failure/${jobId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        button.innerHTML = originalContent;
        button.disabled = false;

        if (data.success) {
            // Show AI analysis in a modal
            showAIAnalysisModal(data.analysis);
        } else {
            alert('Failed to analyze failure: ' + data.error);
        }
    })
    .catch(error => {
        button.innerHTML = originalContent;
        button.disabled = false;
        alert('Error analyzing failure: ' + error.message);
    });
}

function showAISuggestions(jobId) {
    fetch(`/api/ai-suggestions/${jobId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAISuggestionsModal(data.suggestions);
        } else {
            alert('No AI suggestions available: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error fetching AI suggestions: ' + error.message);
    });
}

function compareWithAI(jobId) {
    fetch(`/api/ai-compare-migration/${jobId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIComparisonModal(data.comparison);
        } else {
            alert('AI comparison not available: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error comparing with AI: ' + error.message);
    });
}

// Modal display functions
function showAIAnalysisModal(analysis) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-brain text-info me-2"></i>
                        AI Failure Analysis
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-robot me-2"></i>
                        AI has analyzed the migration failure and provided the following insights:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${analysis}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="applyAISuggestions()">Apply Suggestions</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();

    // Remove modal from DOM when hidden
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function showAISuggestionsModal(suggestions) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        AI Suggestions
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-robot me-2"></i>
                        AI recommendations for improving this migration:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${suggestions}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" onclick="implementSuggestions()">Implement</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();

    // Remove modal from DOM when hidden
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function showAIComparisonModal(comparison) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-balance-scale text-success me-2"></i>
                        AI Migration Comparison
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-robot me-2"></i>
                        AI comparison between current migration and optimal approach:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${comparison}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-info" onclick="rerunWithOptimizations()">Rerun with Optimizations</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();

    // Remove modal from DOM when hidden
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

// Placeholder functions for future implementation
function applyAISuggestions() {
    alert('Apply AI suggestions functionality will be implemented in backend');
}

function implementSuggestions() {
    alert('Implement suggestions functionality will be implemented in backend');
}

function rerunWithOptimizations() {
    alert('Rerun with optimizations functionality will be implemented in backend');
}
</script>
{% endblock %}