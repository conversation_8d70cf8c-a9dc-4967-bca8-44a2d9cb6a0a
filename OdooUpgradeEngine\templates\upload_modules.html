{% extends "base.html" %}
{% set title = "Upload Modules" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-upload me-3"></i>
            Upload Modules
        </h1>
        <p class="lead">Upload Odoo module files for analysis and compatibility testing</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-file-upload me-2"></i>
                    Select Module Files
                </h5>
                
                <form action="{{ url_for('main.upload_modules') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                    <div class="mb-4">
                        <label for="files" class="form-label">Choose Files</label>
                        <input type="file" class="form-control" name="modules" id="files" multiple accept=".zip,.tar,.tar.gz,.tgz" required>
                        <div class="form-text">
                            Supported formats: ZIP, TAR, TAR.GZ, TGZ (Max size: 100MB per file)
                        </div>
                    </div>
                    
                    <div id="filePreview" class="mb-4" style="display: none;">
                        <h6>Selected Files:</h6>
                        <div id="fileList" class="list-group">
                            <!-- Files will be listed here -->
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                            <i class="fas fa-upload me-1"></i>
                            Upload Files
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFiles()">
                            <i class="fas fa-times me-1"></i>
                            Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Upload Guidelines
                </h5>
                
                <div class="mb-3">
                    <h6>Supported Formats:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-file-archive me-2 text-primary"></i>ZIP files (.zip)</li>
                        <li><i class="fas fa-file-archive me-2 text-primary"></i>TAR files (.tar)</li>
                        <li><i class="fas fa-file-archive me-2 text-primary"></i>Compressed TAR (.tar.gz, .tgz)</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>What We Analyze:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Module manifest (__manifest__.py)</li>
                        <li><i class="fas fa-check text-success me-2"></i>Python code compatibility</li>
                        <li><i class="fas fa-check text-success me-2"></i>XML view definitions</li>
                        <li><i class="fas fa-check text-success me-2"></i>Dependencies and structure</li>
                        <li><i class="fas fa-check text-success me-2"></i>Multi-version compatibility (v13-v18)</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Tip:</strong> You can upload multiple files at once. Each will be analyzed separately.
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Uploads
                </h5>
                <p class="card-text">
                    <a href="{{ url_for('main.analyze_modules') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list me-1"></i>
                        View All Modules
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('files').addEventListener('change', function(e) {
    const files = e.target.files;
    const filePreview = document.getElementById('filePreview');
    const fileList = document.getElementById('fileList');
    
    fileList.innerHTML = '';
    
    if (files.length > 0) {
        filePreview.style.display = 'block';
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
            listItem.innerHTML = `
                <div>
                    <i class="fas fa-file-archive me-2 text-primary"></i>
                    <strong>${file.name}</strong>
                    <small class="text-muted d-block">${fileSize} MB</small>
                </div>
                <span class="badge bg-secondary rounded-pill">${file.type || 'Unknown'}</span>
            `;
            
            fileList.appendChild(listItem);
        }
    } else {
        filePreview.style.display = 'none';
    }
});

function clearFiles() {
    document.getElementById('files').value = '';
    document.getElementById('filePreview').style.display = 'none';
    document.getElementById('fileList').innerHTML = '';
}

// Form submission with progress indication
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
});
</script>
{% endblock %}
