<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migration Diff Report - Job #3</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .diff-container { font-family: 'Courier New', monospace; font-size: 14px; }
        .diff-added { background-color: #d4edda; color: #155724; border-left: 4px solid #28a745; padding: 2px 8px; }
        .diff-removed { background-color: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; padding: 2px 8px; }
        .diff-unchanged { background-color: #f8f9fa; color: #6c757d; padding: 2px 8px; }
        .diff-line-number { background-color: #e9ecef; color: #495057; padding: 2px 8px; text-align: right; width: 60px; border-right: 1px solid #dee2e6; }
        .file-header { background-color: #007bff; color: white; padding: 10px; margin: 20px 0 10px 0; border-radius: 5px; }
        .stats-badge { font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-code-branch me-2"></i>Migration Diff Report</h2>
                        <p class="text-muted mb-0">Job #3: test_hr_module (17.0 → 18.0)</p>
                    </div>
                    <div>
                        <span class="badge bg-success stats-badge me-2"><i class="fas fa-plus"></i> 15 additions</span>
                        <span class="badge bg-danger stats-badge me-2"><i class="fas fa-minus"></i> 8 deletions</span>
                        <span class="badge bg-info stats-badge"><i class="fas fa-file"></i> 2 files changed</span>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header"><h5 class="mb-0">Migration Summary</h5></div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Module:</strong> test_hr_module<br>
                                <strong>Original Version:</strong> 17.0<br>
                                <strong>Target Version:</strong> 18.0
                            </div>
                            <div class="col-md-4">
                                <strong>Migration Date:</strong> July 05, 2025<br>
                                <strong>Status:</strong> <span class="badge bg-warning">CANCELLED</span><br>
                                <strong>Quality Score:</strong> 92%
                            </div>
                            <div class="col-md-4">
                                <strong>Files Modified:</strong> 2<br>
                                <strong>Lines Added:</strong> 15<br>
                                <strong>Lines Removed:</strong> 8
                            </div>
                        </div>
                    </div>
                </div>

                <div class="diff-container">
                    <div class="file-header">
                        <i class="fas fa-file-code me-2"></i>__manifest__.py
                        <span class="badge bg-light text-dark ms-2">+3 -1</span>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This is a generated diff report for demonstration. In production, this would show actual file differences from the migration process.
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.close()">
                        <i class="fas fa-times me-2"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>