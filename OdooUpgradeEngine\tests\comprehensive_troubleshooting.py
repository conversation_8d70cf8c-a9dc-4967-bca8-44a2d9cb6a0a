#!/usr/bin/env python3
"""
Comprehensive Troubleshooting - Critical Error Investigation

This script systematically tests each component of the migration pipeline
to identify exactly where the failure occurs.
"""

import os
import sys
import time
import json
import traceback
from datetime import datetime

def setup_environment():
    """Setup test environment"""
    import sys
    sys.path.append('tests')
    from test_config import setup_test_environment
    setup_test_environment()

def test_component_1_github_puller():
    """Test 1: GitHub Module Puller (Isolated)"""
    print("\n🔍 TEST 1: GitHub Module Puller (Isolated)")
    print("-" * 50)
    
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            from github_module_puller import pull_modules_from_github
            
            print("   Testing with single module from OCA/server-tools...")
            start_time = time.time()
            
            # Test with a smaller, simpler repository first
            result = pull_modules_from_github(
                "https://github.com/OCA/server-tools", 
                ["18.0"]
            )
            
            elapsed = time.time() - start_time
            
            print(f"   ⏱️  Completed in {elapsed:.1f} seconds")
            print(f"   📊 Results:")
            print(f"      - Detected: {result.get('modules_detected', 0)}")
            print(f"      - Downloaded: {result.get('modules_downloaded', 0)}")
            print(f"      - Processed: {result.get('modules_processed', 0)}")
            print(f"      - Failed: {result.get('modules_failed', 0)}")
            print(f"      - Errors: {len(result.get('errors', []))}")
            
            if result.get('errors'):
                print(f"   ❌ First error: {result['errors'][0]}")
            
            if result.get('processed_modules'):
                print(f"   ✅ First processed module: {result['processed_modules'][0]['name']}")
                return result['processed_modules'][0]  # Return for next test
            
            return None
            
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        traceback.print_exc()
        return None

def test_component_2_migration_job_creation(processed_module):
    """Test 2: Migration Job Creation"""
    print("\n🔍 TEST 2: Migration Job Creation")
    print("-" * 50)
    
    if not processed_module:
        print("   ⚠️  SKIPPED: No processed module from previous test")
        return None
    
    try:
        from app import create_app, db
        from models import MigrationJob, OdooModule
        
        app = create_app()
        with app.app_context():
            # Find the module in database
            module_record = OdooModule.query.filter_by(
                name=processed_module['name']
            ).first()
            
            if not module_record:
                print(f"   ❌ Module {processed_module['name']} not found in database")
                return None
            
            print(f"   ✅ Found module: {module_record.name} (ID: {module_record.id})")
            
            # Test direct migration job creation
            migration_job = MigrationJob(
                module_id=module_record.id,
                target_version="18.0",
                status='QUEUED',
                timestamp=datetime.now(),
                log=f"Test migration job for troubleshooting"
            )
            
            db.session.add(migration_job)
            db.session.commit()
            
            print(f"   ✅ Created migration job: ID {migration_job.id}")
            return migration_job.id
            
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        traceback.print_exc()
        return None

def test_component_3_pipeline_orchestrator(job_id):
    """Test 3: Pipeline Migration Orchestrator"""
    print("\n🔍 TEST 3: Pipeline Migration Orchestrator")
    print("-" * 50)
    
    if not job_id:
        print("   ⚠️  SKIPPED: No migration job from previous test")
        return None
    
    try:
        from app import create_app
        from pipeline_migration_orchestrator import PipelineMigrationOrchestrator
        from models import MigrationJob
        
        app = create_app()
        with app.app_context():
            # Get the migration job
            job = MigrationJob.query.get(job_id)
            if not job:
                print(f"   ❌ Migration job {job_id} not found")
                return None
            
            print(f"   ✅ Found migration job: {job.id} for module {job.module_id}")
            
            # Test pipeline orchestrator initialization
            orchestrator = PipelineMigrationOrchestrator(
                module_id=job.module_id,
                target_version="18.0",
                enable_pipeline=True
            )
            
            print(f"   ✅ Pipeline orchestrator initialized")
            
            # Test pipeline start (this is where it might fail)
            print(f"   🚀 Starting pipeline migration...")
            start_time = time.time()
            
            result = orchestrator.start_pipeline_migration()
            elapsed = time.time() - start_time
            
            print(f"   ⏱️  Pipeline completed in {elapsed:.1f} seconds")
            print(f"   📊 Result: {result}")
            
            return result
            
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        traceback.print_exc()
        return None

def test_component_4_api_endpoint():
    """Test 4: API Endpoint (Full Integration)"""
    print("\n🔍 TEST 4: API Endpoint (Full Integration)")
    print("-" * 50)
    
    try:
        import requests
        
        # Test the actual API endpoint with a single module
        print("   🌐 Testing /api/github/pull-modules endpoint...")
        
        response = requests.post(
            "http://localhost:5003/api/github/pull-modules",
            json={
                "repository_url": "https://github.com/OCA/server-tools",
                "target_version": "18.0",
                "migration_mode": "direct",  # Use direct mode for testing
                "limit": 1  # Limit to 1 module
            },
            timeout=60
        )
        
        print(f"   📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success')}")
            print(f"   📊 Modules: {len(data.get('modules', []))}")
            print(f"   🎯 Mode: {data.get('migration_mode')}")
            
            if data.get('modules'):
                module = data['modules'][0]
                print(f"   📦 Module: {module.get('name')} (Job: {module.get('migration_job_id')})")
            
            return data
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   📄 Response: {response.text[:200]}...")
            return None
            
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        traceback.print_exc()
        return None

def test_component_5_database_state():
    """Test 5: Database State Analysis"""
    print("\n🔍 TEST 5: Database State Analysis")
    print("-" * 50)
    
    try:
        from app import create_app, db
        from models import OdooModule, MigrationJob, ModuleAnalysis
        
        app = create_app()
        with app.app_context():
            # Count records
            module_count = OdooModule.query.count()
            job_count = MigrationJob.query.count()
            analysis_count = ModuleAnalysis.query.count()
            
            print(f"   📊 Database State:")
            print(f"      - Modules: {module_count}")
            print(f"      - Migration Jobs: {job_count}")
            print(f"      - Analyses: {analysis_count}")
            
            # Check recent modules
            recent_modules = OdooModule.query.order_by(OdooModule.id.desc()).limit(5).all()
            print(f"   📦 Recent Modules:")
            for module in recent_modules:
                print(f"      - {module.name} (ID: {module.id}, Version: {module.version})")
            
            # Check recent jobs
            recent_jobs = MigrationJob.query.order_by(MigrationJob.timestamp.desc()).limit(5).all()
            print(f"   🔄 Recent Jobs:")
            for job in recent_jobs:
                print(f"      - Job {job.id}: {job.status} (Module: {job.module_id})")
            
            # Check for orphaned modules (modules without jobs)
            modules_without_jobs = db.session.query(OdooModule).outerjoin(MigrationJob).filter(MigrationJob.id == None).all()
            print(f"   ⚠️  Modules without migration jobs: {len(modules_without_jobs)}")
            
            if modules_without_jobs:
                print(f"      First few:")
                for module in modules_without_jobs[:3]:
                    print(f"      - {module.name} (ID: {module.id})")
            
            return {
                'modules': module_count,
                'jobs': job_count,
                'analyses': analysis_count,
                'orphaned_modules': len(modules_without_jobs)
            }
            
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        traceback.print_exc()
        return None

def main():
    """Main troubleshooting execution"""
    print("🚨 COMPREHENSIVE TROUBLESHOOTING - CRITICAL ERROR INVESTIGATION")
    print("=" * 70)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Setup environment
    setup_environment()
    
    # Wait for Flask server
    print("\n🔄 Waiting for Flask server...")
    import requests
    for i in range(30):
        try:
            response = requests.get("http://localhost:5003/", timeout=5)
            if response.status_code == 200:
                print("✅ Flask server is ready")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("❌ Flask server not available - some tests will be skipped")
    
    # Run systematic tests
    results = {}
    
    # Test 1: GitHub Puller
    processed_module = test_component_1_github_puller()
    results['github_puller'] = processed_module is not None
    
    # Test 2: Migration Job Creation
    job_id = test_component_2_migration_job_creation(processed_module)
    results['job_creation'] = job_id is not None
    
    # Test 3: Pipeline Orchestrator
    pipeline_result = test_component_3_pipeline_orchestrator(job_id)
    results['pipeline_orchestrator'] = pipeline_result is not None
    
    # Test 4: API Endpoint
    api_result = test_component_4_api_endpoint()
    results['api_endpoint'] = api_result is not None
    
    # Test 5: Database State
    db_state = test_component_5_database_state()
    results['database_state'] = db_state is not None
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TROUBLESHOOTING SUMMARY")
    print("=" * 70)
    
    for component, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {component.replace('_', ' ').title()}")
    
    # Save results
    with open('troubleshooting_results.json', 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'results': results,
            'database_state': db_state
        }, f, indent=2)
    
    print(f"\n📁 Results saved to: troubleshooting_results.json")
    
    # Identify the failure point
    failure_point = None
    for component, success in results.items():
        if not success:
            failure_point = component
            break
    
    if failure_point:
        print(f"\n🎯 FAILURE POINT IDENTIFIED: {failure_point.replace('_', ' ').title()}")
        print("   This is where the migration pipeline breaks down.")
    else:
        print(f"\n🎉 ALL COMPONENTS WORKING - Issue may be in integration or timing")
    
    return results

if __name__ == "__main__":
    main()
