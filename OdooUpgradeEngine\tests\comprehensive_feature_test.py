#!/usr/bin/env python3
"""
Comprehensive Feature Testing Script
Tests all pages, buttons, forms, and expected functionality
"""

import requests
import json
import time
from datetime import datetime

class ComprehensiveFeatureTest:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, status, message="", details=""):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
        if details:
            print(f"   Details: {details}")
    
    def test_page_features(self, path, page_name, expected_features):
        """Test if a page has expected features"""
        try:
            response = self.session.get(f"{self.base_url}{path}", timeout=10)
            if response.status_code != 200:
                self.log_test(f"{page_name} - Access", "FAIL", f"HTTP {response.status_code}")
                return False
            
            html_content = response.text.lower()
            
            # Check for expected features
            found_features = []
            missing_features = []
            
            for feature in expected_features:
                if feature.lower() in html_content:
                    found_features.append(feature)
                else:
                    missing_features.append(feature)
            
            if len(found_features) == len(expected_features):
                self.log_test(f"{page_name} - Features", "PASS", 
                             f"All {len(expected_features)} features found")
            elif len(found_features) > len(expected_features) * 0.7:
                self.log_test(f"{page_name} - Features", "WARN", 
                             f"{len(found_features)}/{len(expected_features)} features found",
                             f"Missing: {missing_features[:3]}")
            else:
                self.log_test(f"{page_name} - Features", "FAIL", 
                             f"Only {len(found_features)}/{len(expected_features)} features found",
                             f"Missing: {missing_features}")
            
            return len(found_features) > 0
            
        except Exception as e:
            self.log_test(f"{page_name} - Features", "FAIL", f"Error: {str(e)}")
            return False
    
    def test_ai_features(self):
        """Test AI provider functionality"""
        print("\n🤖 TESTING AI FEATURES")
        print("-" * 40)
        
        # Test AI provider page
        ai_features = [
            "ai provider", "configuration", "deepseek", "openai", "claude",
            "confidence threshold", "auto-approval", "test connection",
            "save settings", "api key"
        ]
        
        self.test_page_features("/ai_providers", "AI Providers", ai_features)
        
        # Test AI provider status API
        try:
            response = self.session.get(f"{self.base_url}/api/ai-provider-status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                provider = data.get('provider_name', 'Unknown')
                available = data.get('is_available', False)
                self.log_test("AI Provider API", "PASS" if available else "WARN", 
                             f"Provider: {provider}, Available: {available}")
            else:
                self.log_test("AI Provider API", "FAIL", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("AI Provider API", "FAIL", f"Error: {str(e)}")
    
    def test_docker_features(self):
        """Test Docker environment functionality"""
        print("\n🐳 TESTING DOCKER FEATURES")
        print("-" * 40)
        
        docker_features = [
            "docker", "environment", "create environment", "refresh",
            "total environments", "running", "stopped", "odoo version",
            "container", "port", "status"
        ]
        
        self.test_page_features("/docker_environments", "Docker Environments", docker_features)
    
    def test_automation_features(self):
        """Test automation dashboard functionality"""
        print("\n🤖 TESTING AUTOMATION FEATURES")
        print("-" * 40)
        
        automation_features = [
            "automation", "dashboard", "initialize", "run cycle", "sync modules",
            "system initialized", "module count", "automation status",
            "last run", "next run"
        ]
        
        self.test_page_features("/automation/", "Automation Dashboard", automation_features)
    
    def test_migration_features(self):
        """Test migration orchestrator and jobs"""
        print("\n🔄 TESTING MIGRATION FEATURES")
        print("-" * 40)
        
        # Test Migration Orchestrator
        orchestrator_features = [
            "migration", "orchestrator", "module", "job", "status",
            "pipeline", "upgrade", "version", "progress"
        ]
        
        self.test_page_features("/migration_orchestrator", "Migration Orchestrator", orchestrator_features)
        
        # Test Migration Jobs
        jobs_features = [
            "migration", "jobs", "status", "queued", "running", "completed",
            "failed", "progress", "details"
        ]
        
        self.test_page_features("/migration_jobs", "Migration Jobs", jobs_features)
    
    def test_github_features(self):
        """Test GitHub integration functionality"""
        print("\n🐙 TESTING GITHUB FEATURES")
        print("-" * 40)
        
        github_features = [
            "github", "integration", "repository", "scan", "pull modules",
            "target version", "migration mode", "progress", "results"
        ]
        
        self.test_page_features("/github_integration", "GitHub Integration", github_features)
    
    def test_upload_features(self):
        """Test module upload functionality"""
        print("\n📤 TESTING UPLOAD FEATURES")
        print("-" * 40)
        
        upload_features = [
            "upload", "module", "file", "drag", "drop", "browse",
            "zip", "tar", "supported formats"
        ]
        
        self.test_page_features("/upload_modules", "Upload Modules", upload_features)
    
    def test_analysis_features(self):
        """Test module analysis functionality"""
        print("\n🔍 TESTING ANALYSIS FEATURES")
        print("-" * 40)
        
        analysis_features = [
            "analyze", "module", "compatibility", "version", "score",
            "analysis status", "actions", "upgrade", "fix"
        ]
        
        self.test_page_features("/analyze_modules", "Analyze Modules", analysis_features)
    
    def test_bulk_migration_features(self):
        """Test bulk migration functionality"""
        print("\n📦 TESTING BULK MIGRATION FEATURES")
        print("-" * 40)
        
        bulk_features = [
            "bulk", "migration", "batch", "select all", "target version",
            "migration type", "start migration", "progress"
        ]
        
        self.test_page_features("/bulk_migration", "Bulk Migration", bulk_features)
    
    def test_manual_interventions(self):
        """Test manual interventions functionality"""
        print("\n✋ TESTING MANUAL INTERVENTIONS")
        print("-" * 40)
        
        intervention_features = [
            "manual", "intervention", "queue", "priority", "severity",
            "resolution", "statistics", "pending", "resolved"
        ]
        
        self.test_page_features("/manual_interventions", "Manual Interventions", intervention_features)
    
    def test_health_monitoring(self):
        """Test health dashboard functionality"""
        print("\n💊 TESTING HEALTH MONITORING")
        print("-" * 40)
        
        health_features = [
            "health", "dashboard", "system status", "monitoring",
            "alerts", "performance", "metrics", "uptime"
        ]
        
        self.test_page_features("/health_dashboard", "Health Dashboard", health_features)
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🚀 STARTING COMPREHENSIVE FEATURE TESTING")
        print("=" * 80)
        
        start_time = time.time()
        
        # Test all major feature areas
        self.test_ai_features()
        self.test_docker_features()
        self.test_automation_features()
        self.test_migration_features()
        self.test_github_features()
        self.test_upload_features()
        self.test_analysis_features()
        self.test_bulk_migration_features()
        self.test_manual_interventions()
        self.test_health_monitoring()
        
        # Generate summary
        end_time = time.time()
        duration = end_time - start_time
        
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warnings = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE FEATURE TEST SUMMARY")
        print("=" * 80)
        print(f"✅ Passed: {passed}")
        print(f"⚠️  Warnings: {warnings}")
        print(f"❌ Failed: {failed}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📈 Success Rate: {(passed/(passed+failed+warnings)*100):.1f}%")
        
        return failed == 0

if __name__ == "__main__":
    tester = ComprehensiveFeatureTest()
    success = tester.run_comprehensive_tests()
    
    # Save detailed results
    with open('comprehensive_feature_test_results.json', 'w') as f:
        json.dump(tester.test_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to comprehensive_feature_test_results.json")
