{% extends "base.html" %}

{% block title %}AI Provider Configuration{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-robot me-2"></i>AI Provider Configuration</h1>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="startSetupWizard()">
                <i class="fas fa-magic me-1"></i>Setup Wizard
            </button>
            <button class="btn btn-success me-2" onclick="saveAllSettings()">
                <i class="fas fa-save me-1"></i>Save All Settings
            </button>
            <button class="btn btn-outline-info" onclick="testAIConnection()">
                <i class="fas fa-plug me-1"></i>Test Connection
            </button>
        </div>
    </div>

    <!-- AI Provider Setup Wizard -->
    <div class="card mb-4" id="setup-wizard" style="display: none;">
        <div class="card-header bg-gradient-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-magic me-2"></i>AI Provider Setup Wizard
                <button type="button" class="btn-close btn-close-white float-end" onclick="closeSetupWizard()"></button>
            </h5>
        </div>
        <div class="card-body">
            <div class="wizard-steps">
                <!-- Step 1: Choose Provider -->
                <div class="wizard-step active" id="step-1">
                    <h6 class="text-primary mb-3">
                        <span class="badge bg-primary me-2">1</span>Choose Your AI Provider
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card provider-card h-100" data-provider="deepseek" onclick="selectProvider('deepseek')">
                                <div class="card-body text-center">
                                    <i class="fas fa-brain fa-3x text-info mb-3"></i>
                                    <h6>DeepSeek</h6>
                                    <p class="small text-muted">Recommended - 90% cheaper than OpenAI</p>
                                    <div class="badge bg-success">FREE TIER</div>
                                    <div class="badge bg-info">FAST SETUP</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card provider-card h-100" data-provider="ollama" onclick="selectProvider('ollama')">
                                <div class="card-body text-center">
                                    <i class="fas fa-server fa-3x text-success mb-3"></i>
                                    <h6>Ollama (Local)</h6>
                                    <p class="small text-muted">Run AI models locally on your machine</p>
                                    <div class="badge bg-success">100% FREE</div>
                                    <div class="badge bg-warning">LOCAL INSTALL</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card provider-card h-100" data-provider="openai" onclick="selectProvider('openai')">
                                <div class="card-body text-center">
                                    <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                                    <h6>OpenAI</h6>
                                    <p class="small text-muted">Industry standard with GPT-4</p>
                                    <div class="badge bg-warning">PAID</div>
                                    <div class="badge bg-primary">PREMIUM</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Configuration -->
                <div class="wizard-step" id="step-2" style="display: none;">
                    <h6 class="text-primary mb-3">
                        <span class="badge bg-primary me-2">2</span>Configure Your Provider
                    </h6>
                    <div id="wizard-config-content">
                        <!-- Dynamic content based on selected provider -->
                    </div>
                </div>

                <!-- Step 3: Test & Complete -->
                <div class="wizard-step" id="step-3" style="display: none;">
                    <h6 class="text-primary mb-3">
                        <span class="badge bg-primary me-2">3</span>Test & Complete Setup
                    </h6>
                    <div class="text-center">
                        <div id="test-results" class="mb-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Testing connection...</span>
                            </div>
                            <p class="mt-2">Testing your AI provider connection...</p>
                        </div>
                        <div id="test-success" style="display: none;">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">Setup Complete!</h5>
                            <p>Your AI provider is configured and ready to use.</p>
                            <button class="btn btn-success" onclick="completeSetup()">
                                <i class="fas fa-check me-1"></i>Complete Setup
                            </button>
                        </div>
                        <div id="test-error" style="display: none;">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5 class="text-warning">Connection Failed</h5>
                            <p id="error-message">Please check your configuration and try again.</p>
                            <button class="btn btn-warning" onclick="retryTest()">
                                <i class="fas fa-redo me-1"></i>Retry Test
                            </button>
                            <button class="btn btn-outline-secondary ms-2" onclick="goToStep(2)">
                                <i class="fas fa-arrow-left me-1"></i>Back to Config
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="d-flex justify-content-between mt-4">
                <button class="btn btn-outline-secondary" id="prev-btn" onclick="previousStep()" disabled>
                    <i class="fas fa-arrow-left me-1"></i>Previous
                </button>
                <div class="wizard-progress">
                    <span class="badge bg-primary" id="step-indicator">Step 1 of 3</span>
                </div>
                <button class="btn btn-primary" id="next-btn" onclick="nextStep()" disabled>
                    Next<i class="fas fa-arrow-right ms-1"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Provider Performance Comparison -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-chart-bar me-2"></i>Provider Performance Comparison
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Provider</th>
                            <th>Cost</th>
                            <th>Speed</th>
                            <th>Accuracy</th>
                            <th>Setup Difficulty</th>
                            <th>Best For</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="table-success">
                            <td>
                                <i class="fas fa-brain text-info me-2"></i>
                                <strong>DeepSeek</strong>
                                <span class="badge bg-success ms-2">Recommended</span>
                            </td>
                            <td><span class="badge bg-success">90% Cheaper</span></td>
                            <td><span class="badge bg-success">Fast</span></td>
                            <td><span class="badge bg-success">High</span></td>
                            <td><span class="badge bg-success">Easy</span></td>
                            <td>Cost-effective production use</td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-server text-success me-2"></i>
                                <strong>Ollama (Local)</strong>
                            </td>
                            <td><span class="badge bg-success">Free</span></td>
                            <td><span class="badge bg-warning">Medium</span></td>
                            <td><span class="badge bg-info">Good</span></td>
                            <td><span class="badge bg-warning">Medium</span></td>
                            <td>Privacy-focused, offline use</td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-robot text-primary me-2"></i>
                                <strong>OpenAI</strong>
                            </td>
                            <td><span class="badge bg-danger">Expensive</span></td>
                            <td><span class="badge bg-success">Fast</span></td>
                            <td><span class="badge bg-success">Highest</span></td>
                            <td><span class="badge bg-success">Easy</span></td>
                            <td>Premium features, complex tasks</td>
                        </tr>
                        <tr>
                            <td>
                                <i class="fas fa-route text-warning me-2"></i>
                                <strong>OpenRouter</strong>
                            </td>
                            <td><span class="badge bg-info">Variable</span></td>
                            <td><span class="badge bg-info">Variable</span></td>
                            <td><span class="badge bg-info">Variable</span></td>
                            <td><span class="badge bg-success">Easy</span></td>
                            <td>Multiple models, experimentation</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- ===== AI CONFIGURATION FORMS START ===== -->
    <!-- AI Auto-Approval Settings -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>AI Auto-Approval Settings</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="confidenceThreshold" class="form-label">Confidence Threshold for Auto-Approval</label>
                        <div class="input-group">
                            <input type="range" class="form-range" id="confidenceThreshold" min="0" max="100" value="80" oninput="updateThresholdDisplay(this.value)">
                            <span class="input-group-text" id="thresholdDisplay">80%</span>
                        </div>
                        <div class="form-text">Migrations with AI confidence above this threshold will be auto-approved (if low risk)</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Auto-Approval Options</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableAutoApproval" checked>
                            <label class="form-check-label" for="enableAutoApproval">
                                Enable AI Auto-Approval
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="requireLowRisk" checked>
                            <label class="form-check-label" for="requireLowRisk">
                                Only auto-approve low-risk migrations
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="blockCriticalIssues" checked>
                            <label class="form-check-label" for="blockCriticalIssues">
                                Never auto-approve migrations with critical issues
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Provider Configuration Forms -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>AI Provider Configuration</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- DeepSeek Configuration -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-brain me-2"></i>DeepSeek (Recommended - 90% Cheaper)</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="deepseekApiKey" class="form-label">API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="deepseekApiKey" placeholder="Enter DeepSeek API key">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('deepseekApiKey')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Get your free API key from <a href="https://platform.deepseek.com" target="_blank">platform.deepseek.com</a>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="deepseekModel" class="form-label">Model</label>
                                <select class="form-select" id="deepseekModel">
                                    <option value="deepseek-chat">DeepSeek Chat (Recommended)</option>
                                    <option value="deepseek-coder">DeepSeek Coder</option>
                                    <option value="deepseek-reasoner">DeepSeek Reasoner</option>
                                </select>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm" onclick="setProvider('deepseek')">
                                    <i class="fas fa-check me-1"></i>Use DeepSeek
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="testProvider('deepseek')">
                                    <i class="fas fa-vial me-1"></i>Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- OpenRouter Configuration -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-route me-2"></i>OpenRouter (Free Tier Available)</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="openrouterApiKey" class="form-label">API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="openrouterApiKey" placeholder="Enter OpenRouter API key">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('openrouterApiKey')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-gift me-1"></i>
                                    Get free credits from <a href="https://openrouter.ai" target="_blank">openrouter.ai</a>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="openrouterModel" class="form-label">Model</label>
                                <select class="form-select" id="openrouterModel">
                                    <option value="deepseek/deepseek-chat">DeepSeek Chat (Free)</option>
                                    <option value="meta-llama/llama-3.1-8b-instruct:free">Llama 3.1 8B (Free)</option>
                                    <option value="openai/gpt-4o-mini">GPT-4o Mini (Paid)</option>
                                    <option value="openai/gpt-4o">GPT-4o (Paid)</option>
                                </select>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-warning btn-sm" onclick="setProvider('openrouter')">
                                    <i class="fas fa-check me-1"></i>Use OpenRouter
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="testProvider('openrouter')">
                                    <i class="fas fa-vial me-1"></i>Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- OpenAI Configuration -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-robot me-2"></i>OpenAI (Most Reliable, Paid)</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="openaiApiKey" class="form-label">API Key</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="openaiApiKey" placeholder="Enter OpenAI API key">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('openaiApiKey')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Get API key from <a href="https://platform.openai.com" target="_blank">platform.openai.com</a>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="openaiModel" class="form-label">Model</label>
                                <select class="form-select" id="openaiModel">
                                    <option value="gpt-4o">GPT-4o (Recommended)</option>
                                    <option value="gpt-4o-mini">GPT-4o Mini (Cheaper)</option>
                                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                </select>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-secondary btn-sm" onclick="setProvider('openai')">
                                    <i class="fas fa-check me-1"></i>Use OpenAI
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="testProvider('openai')">
                                    <i class="fas fa-vial me-1"></i>Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Local Ollama Configuration -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-dark text-white">
                            <h6 class="mb-0"><i class="fas fa-server me-2"></i>Local Ollama (Free, Private)</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="ollamaUrl" class="form-label">Ollama Server URL</label>
                                <input type="url" class="form-control" id="ollamaUrl" value="http://localhost:11434" placeholder="http://localhost:11434">
                                <div class="form-text">
                                    <i class="fas fa-home me-1"></i>
                                    Install Ollama from <a href="https://ollama.ai" target="_blank">ollama.ai</a>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="ollamaModel" class="form-label">Model</label>
                                <select class="form-select" id="ollamaModel">
                                    <option value="deepseek-r1:8b">DeepSeek R1 8B (🥇 Best for Migration Analysis)</option>
                                    <option value="codellama:13b-instruct">CodeLlama 13B Instruct (🥈 Best for Code Analysis)</option>
                                    <option value="qwen3:8b">Qwen3 8B (🥉 Good General Purpose)</option>
                                    <option value="starcoder2:15b">StarCoder2 15B (Code Generation)</option>
                                    <option value="gemma3:12b">Gemma3 12B (Google Model)</option>
                                    <option value="mistral:7b">Mistral 7B (Lightweight)</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-star me-1"></i>
                                    <strong>Recommendation:</strong> DeepSeek R1 8B is specifically designed for reasoning and code analysis, making it ideal for migration analysis.
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-dark btn-sm" onclick="setProvider('ollama')">
                                    <i class="fas fa-check me-1"></i>Use Ollama
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="testProvider('ollama')">
                                    <i class="fas fa-vial me-1"></i>Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Provider Status -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>Current AI Provider Status</h5>
        </div>
        <div class="card-body">
            <div id="currentProviderStatus">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading provider status...</span>
                    </div>
                    <p class="mt-2">Checking AI provider status...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Setup for Free AI -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Available AI Providers</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">
                            Choose your preferred AI provider based on cost, features, and availability. 
                            Free options are available including DeepSeek and OpenRouter.
                        </p>
                        
                        <div class="row">
                            {% for provider in providers %}
                            <div class="col-lg-6 col-xl-4 mb-3">
                                <div class="card h-100 {% if not provider.available %}border-secondary{% elif provider.free_tier %}border-success{% else %}border-primary{% endif %}">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <strong>{{ provider.name }}</strong>
                                        {% if provider.free_tier %}
                                            <span class="badge bg-success">FREE</span>
                                        {% else %}
                                            <span class="badge bg-info">PAID</span>
                                        {% endif %}
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text small">{{ provider.description }}</p>
                                        
                                        <div class="mb-2">
                                            <strong>Cost:</strong> 
                                            {% if provider.cost_per_1m_tokens == 0 %}
                                                <span class="text-success">FREE</span>
                                            {% else %}
                                                ${{ "%.3f"|format(provider.cost_per_1m_tokens) }}/1M tokens
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>Features:</strong><br>
                                            {% for feature in provider.features %}
                                                <span class="badge bg-secondary me-1 mb-1">{{ feature }}</span>
                                            {% endfor %}
                                        </div>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                Status: 
                                                {% if provider.available %}
                                                    <span class="text-success">✓ Ready</span>
                                                {% else %}
                                                    <span class="text-warning">{{ provider.status }}</span>
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        {% if provider.available %}
                                            <button class="btn btn-primary btn-sm w-100" 
                                                    onclick="setProvider('{{ provider.type }}')">
                                                <i class="fas fa-check me-1"></i>Use This Provider
                                            </button>
                                        {% else %}
                                            <small class="text-muted">
                                                {% if 'API key' in provider.status %}
                                                    <i class="fas fa-key me-1"></i>API key required
                                                {% elif 'not running' in provider.status %}
                                                    <i class="fas fa-server me-1"></i>Service not running
                                                {% else %}
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Not available
                                                {% endif %}
                                            </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Setup Instructions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Setup Instructions</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="setupAccordion">
                            <!-- DeepSeek Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#deepseekSetup">
                                        <i class="fas fa-brain me-2"></i>DeepSeek (Recommended - 90% cheaper than GPT-4)
                                    </button>
                                </h2>
                                <div id="deepseekSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Visit <a href="https://platform.deepseek.com" target="_blank">platform.deepseek.com</a></li>
                                            <li>Create account and get API key</li>
                                            <li>Add environment variable: <code>DEEPSEEK_API_KEY=your_key_here</code></li>
                                            <li>Restart the application</li>
                                        </ol>
                                        <div class="alert alert-success">
                                            <strong>Free Trial:</strong> DeepSeek offers free credits for testing
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- OpenRouter Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#openrouterSetup">
                                        <i class="fas fa-route me-2"></i>OpenRouter (Free tier available)
                                    </button>
                                </h2>
                                <div id="openrouterSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Visit <a href="https://openrouter.ai" target="_blank">openrouter.ai</a></li>
                                            <li>Create account and get API key</li>
                                            <li>Add environment variable: <code>OPENROUTER_API_KEY=your_key_here</code></li>
                                            <li>Restart the application</li>
                                        </ol>
                                        <div class="alert alert-info">
                                            <strong>Free Access:</strong> Provides free access to multiple models including DeepSeek
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- OpenAI Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#openaiSetup">
                                        <i class="fas fa-robot me-2"></i>OpenAI (Paid but most reliable)
                                    </button>
                                </h2>
                                <div id="openaiSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Visit <a href="https://platform.openai.com" target="_blank">platform.openai.com</a></li>
                                            <li>Create account and add payment method</li>
                                            <li>Generate API key</li>
                                            <li>Add environment variable: <code>OPENAI_API_KEY=your_key_here</code></li>
                                            <li>Restart the application</li>
                                        </ol>
                                        <div class="alert alert-warning">
                                            <strong>Paid Service:</strong> Requires payment for usage
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Ollama Setup -->
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#ollamaSetup">
                                        <i class="fas fa-home me-2"></i>Ollama (Completely free local models)
                                    </button>
                                </h2>
                                <div id="ollamaSetup" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Install Ollama: <code>curl -fsSL https://ollama.com/install.sh | sh</code></li>
                                            <li>Start Ollama: <code>ollama serve</code></li>
                                            <li>Download a model: <code>ollama pull llama3.2</code></li>
                                            <li>No API key required - runs completely locally</li>
                                        </ol>
                                        <div class="alert alert-success">
                                            <strong>Free & Private:</strong> No API keys, no internet required, complete privacy
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast for notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">AI Provider</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toast-body">
                <!-- Message will be inserted here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setProvider(providerType) {
            fetch('/ai_providers/set', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    provider: providerType
                })
            })
            .then(response => response.json())
            .then(data => {
                const toast = document.getElementById('notification-toast');
                const toastBody = document.getElementById('toast-body');
                
                if (data.success) {
                    toastBody.textContent = data.message;
                    toastBody.className = 'toast-body text-success';
                    
                    // Reload page after short delay to show updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    toastBody.textContent = data.message;
                    toastBody.className = 'toast-body text-danger';
                }
                
                new bootstrap.Toast(toast).show();
            })
            .catch(error => {
                console.error('Error:', error);
                const toast = document.getElementById('notification-toast');
                const toastBody = document.getElementById('toast-body');
                toastBody.textContent = 'Error setting AI provider';
                toastBody.className = 'toast-body text-danger';
                new bootstrap.Toast(toast).show();
            });
        }

        // ===== AI CONFIGURATION JAVASCRIPT START =====

        // Update threshold display
        function updateThresholdDisplay(value) {
            document.getElementById('thresholdDisplay').textContent = value + '%';
        }

        // Toggle password visibility
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = field.nextElementSibling.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // Focus on specific provider section
        function focusProvider(provider) {
            const element = document.getElementById(provider + 'ApiKey') || document.getElementById(provider + 'Url');
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                element.focus();
            }
        }

        // Test AI provider connection
        function testProvider(provider) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testing...';
            button.disabled = true;

            // Get provider configuration
            let config = {};
            if (provider === 'deepseek') {
                config = {
                    provider: 'deepseek',
                    api_key: document.getElementById('deepseekApiKey').value,
                    model: document.getElementById('deepseekModel').value
                };
            } else if (provider === 'openrouter') {
                config = {
                    provider: 'openrouter',
                    api_key: document.getElementById('openrouterApiKey').value,
                    model: document.getElementById('openrouterModel').value
                };
            } else if (provider === 'openai') {
                config = {
                    provider: 'openai',
                    api_key: document.getElementById('openaiApiKey').value,
                    model: document.getElementById('openaiModel').value
                };
            } else if (provider === 'ollama') {
                config = {
                    provider: 'ollama',
                    url: document.getElementById('ollamaUrl').value,
                    model: document.getElementById('ollamaModel').value
                };
            }

            // Test the connection
            fetch('/api/test-ai-provider', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                button.innerHTML = originalText;
                button.disabled = false;

                if (data.success) {
                    showNotification('✅ ' + provider.toUpperCase() + ' connection successful!', 'success');
                } else {
                    showNotification('❌ ' + provider.toUpperCase() + ' connection failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                button.innerHTML = originalText;
                button.disabled = false;
                showNotification('❌ Error testing ' + provider.toUpperCase() + ': ' + error.message, 'error');
            });
        }

        // Save all AI settings
        function saveAllSettings() {
            const settings = {
                confidence_threshold: document.getElementById('confidenceThreshold').value,
                enable_auto_approval: document.getElementById('enableAutoApproval').checked,
                require_low_risk: document.getElementById('requireLowRisk').checked,
                block_critical_issues: document.getElementById('blockCriticalIssues').checked,
                providers: {
                    deepseek: {
                        api_key: document.getElementById('deepseekApiKey').value,
                        model: document.getElementById('deepseekModel').value
                    },
                    openrouter: {
                        api_key: document.getElementById('openrouterApiKey').value,
                        model: document.getElementById('openrouterModel').value
                    },
                    openai: {
                        api_key: document.getElementById('openaiApiKey').value,
                        model: document.getElementById('openaiModel').value
                    },
                    ollama: {
                        url: document.getElementById('ollamaUrl').value,
                        model: document.getElementById('ollamaModel').value
                    }
                }
            };

            fetch('/api/save-ai-settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('✅ AI settings saved successfully!', 'success');
                    loadCurrentProviderStatus();
                } else {
                    showNotification('❌ Failed to save settings: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showNotification('❌ Error saving settings: ' + error.message, 'error');
            });
        }

        // Show notification
        function showNotification(message, type) {
            // Create toast notification
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            // Add to toast container or create one
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(container);
            }

            container.insertAdjacentHTML('beforeend', toastHtml);
            const toast = container.lastElementChild;
            new bootstrap.Toast(toast).show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        // Load current provider status
        function loadCurrentProviderStatus() {
            fetch('/api/ai-provider-status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('currentProviderStatus');

                if (data.success && data.provider) {
                    const provider = data.provider;
                    statusDiv.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-check-circle text-success me-2"></i>Active Provider</h6>
                                <p><strong>Provider:</strong> ${provider.name}</p>
                                <p><strong>Model:</strong> ${provider.model}</p>
                                <p><strong>Status:</strong> <span class="badge bg-success">Active</span></p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-cogs text-info me-2"></i>Settings</h6>
                                <p><strong>Auto-Approval:</strong> ${data.settings.enable_auto_approval ? 'Enabled' : 'Disabled'}</p>
                                <p><strong>Confidence Threshold:</strong> ${data.settings.confidence_threshold}%</p>
                                <p><strong>Risk Filter:</strong> ${data.settings.require_low_risk ? 'Low Risk Only' : 'All Levels'}</p>
                            </div>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>No AI Provider Configured</strong><br>
                            Please configure an AI provider above to enable intelligent migration analysis.
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('currentProviderStatus').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        <strong>Error Loading Status</strong><br>
                        ${error.message}
                    </div>
                `;
            });
        }

        // Load settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentProviderStatus();
            initializeSetupWizard();
        });

        // ===== SETUP WIZARD FUNCTIONALITY =====

        let currentWizardStep = 1;
        let selectedProvider = null;

        function initializeSetupWizard() {
            // Add CSS for wizard
            const style = document.createElement('style');
            style.textContent = `
                .provider-card {
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border: 2px solid transparent;
                }
                .provider-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }
                .provider-card.selected {
                    border-color: #0d6efd;
                    background-color: #f8f9ff;
                }
                .wizard-progress {
                    text-align: center;
                }
            `;
            document.head.appendChild(style);
        }

        function startSetupWizard() {
            document.getElementById('setup-wizard').style.display = 'block';
            currentWizardStep = 1;
            selectedProvider = null;
            updateWizardUI();
        }

        function closeSetupWizard() {
            document.getElementById('setup-wizard').style.display = 'none';
            resetWizard();
        }

        function resetWizard() {
            currentWizardStep = 1;
            selectedProvider = null;
            document.querySelectorAll('.wizard-step').forEach(step => step.style.display = 'none');
            document.getElementById('step-1').style.display = 'block';
            document.querySelectorAll('.provider-card').forEach(card => card.classList.remove('selected'));
            updateWizardUI();
        }

        function selectProvider(provider) {
            selectedProvider = provider;
            document.querySelectorAll('.provider-card').forEach(card => card.classList.remove('selected'));
            document.querySelector(`[data-provider="${provider}"]`).classList.add('selected');
            document.getElementById('next-btn').disabled = false;
        }

        function nextStep() {
            if (currentWizardStep < 3) {
                currentWizardStep++;
                showStep(currentWizardStep);

                if (currentWizardStep === 2) {
                    loadProviderConfig();
                } else if (currentWizardStep === 3) {
                    testProviderConnection();
                }
            }
        }

        function previousStep() {
            if (currentWizardStep > 1) {
                currentWizardStep--;
                showStep(currentWizardStep);
            }
        }

        function goToStep(step) {
            currentWizardStep = step;
            showStep(step);
        }

        function showStep(step) {
            document.querySelectorAll('.wizard-step').forEach(s => s.style.display = 'none');
            document.getElementById(`step-${step}`).style.display = 'block';
            updateWizardUI();
        }

        function updateWizardUI() {
            document.getElementById('step-indicator').textContent = `Step ${currentWizardStep} of 3`;
            document.getElementById('prev-btn').disabled = currentWizardStep === 1;

            if (currentWizardStep === 1) {
                document.getElementById('next-btn').disabled = !selectedProvider;
                document.getElementById('next-btn').innerHTML = 'Next<i class="fas fa-arrow-right ms-1"></i>';
            } else if (currentWizardStep === 2) {
                document.getElementById('next-btn').disabled = false;
                document.getElementById('next-btn').innerHTML = 'Test Connection<i class="fas fa-vial ms-1"></i>';
            } else {
                document.getElementById('next-btn').style.display = 'none';
            }
        }

        function loadProviderConfig() {
            const configContent = document.getElementById('wizard-config-content');

            const configs = {
                deepseek: `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>DeepSeek Setup:</strong> Get your free API key from platform.deepseek.com
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Key</label>
                        <input type="password" class="form-control" id="wizard-deepseek-key" placeholder="Enter your DeepSeek API key">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Model</label>
                        <select class="form-select" id="wizard-deepseek-model">
                            <option value="deepseek-chat">DeepSeek Chat (Recommended)</option>
                            <option value="deepseek-coder">DeepSeek Coder</option>
                        </select>
                    </div>
                `,
                ollama: `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Ollama Setup:</strong> You need to install Ollama locally first
                    </div>
                    <div class="mb-3">
                        <h6>Installation Steps:</h6>
                        <ol>
                            <li>Download Ollama from <a href="https://ollama.ai" target="_blank">ollama.ai</a></li>
                            <li>Install and run: <code>ollama serve</code></li>
                            <li>Pull a model: <code>ollama pull llama2</code></li>
                        </ol>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ollama URL</label>
                        <input type="text" class="form-control" id="wizard-ollama-url" value="http://localhost:11434">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Model</label>
                        <select class="form-select" id="wizard-ollama-model">
                            <option value="llama2">Llama 2</option>
                            <option value="codellama">Code Llama</option>
                            <option value="mistral">Mistral</option>
                        </select>
                    </div>
                `,
                openai: `
                    <div class="alert alert-warning">
                        <i class="fas fa-credit-card me-2"></i>
                        <strong>OpenAI Setup:</strong> Requires paid API key from OpenAI
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Key</label>
                        <input type="password" class="form-control" id="wizard-openai-key" placeholder="Enter your OpenAI API key">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Model</label>
                        <select class="form-select" id="wizard-openai-model">
                            <option value="gpt-4o-mini">GPT-4o Mini (Cheaper)</option>
                            <option value="gpt-4o">GPT-4o (Premium)</option>
                        </select>
                    </div>
                `
            };

            configContent.innerHTML = configs[selectedProvider] || '';
        }

        function testProviderConnection() {
            document.getElementById('test-results').style.display = 'block';
            document.getElementById('test-success').style.display = 'none';
            document.getElementById('test-error').style.display = 'none';

            // Get configuration values
            let config = {};
            if (selectedProvider === 'deepseek') {
                config = {
                    provider: 'deepseek',
                    api_key: document.getElementById('wizard-deepseek-key').value,
                    model: document.getElementById('wizard-deepseek-model').value
                };
            } else if (selectedProvider === 'ollama') {
                config = {
                    provider: 'ollama',
                    url: document.getElementById('wizard-ollama-url').value,
                    model: document.getElementById('wizard-ollama-model').value
                };
            } else if (selectedProvider === 'openai') {
                config = {
                    provider: 'openai',
                    api_key: document.getElementById('wizard-openai-key').value,
                    model: document.getElementById('wizard-openai-model').value
                };
            }

            // Test the connection
            fetch('/api/test-ai-provider', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('test-results').style.display = 'none';

                if (data.success) {
                    document.getElementById('test-success').style.display = 'block';
                } else {
                    document.getElementById('test-error').style.display = 'block';
                    document.getElementById('error-message').textContent = data.error || 'Connection failed';
                }
            })
            .catch(error => {
                document.getElementById('test-results').style.display = 'none';
                document.getElementById('test-error').style.display = 'block';
                document.getElementById('error-message').textContent = 'Network error: ' + error.message;
            });
        }

        function retryTest() {
            testProviderConnection();
        }

        function completeSetup() {
            // Save the configuration
            if (selectedProvider === 'deepseek') {
                document.getElementById('deepseekApiKey').value = document.getElementById('wizard-deepseek-key').value;
                document.getElementById('deepseekModel').value = document.getElementById('wizard-deepseek-model').value;
                setProvider('deepseek');
            } else if (selectedProvider === 'ollama') {
                document.getElementById('ollamaUrl').value = document.getElementById('wizard-ollama-url').value;
                document.getElementById('ollamaModel').value = document.getElementById('wizard-ollama-model').value;
                setProvider('ollama');
            } else if (selectedProvider === 'openai') {
                document.getElementById('openaiApiKey').value = document.getElementById('wizard-openai-key').value;
                document.getElementById('openaiModel').value = document.getElementById('wizard-openai-model').value;
                setProvider('openai');
            }

            closeSetupWizard();

            // Show success message
            showNotification('AI provider configured successfully!', 'success');
        }

        // ===== AI CONFIGURATION JAVASCRIPT END =====
    </script>

{% endblock %}