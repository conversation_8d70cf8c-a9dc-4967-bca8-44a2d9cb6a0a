"""
GitHub Module Puller - Advanced Repository Integration

This module provides comprehensive GitHub repository integration for:
1. Pulling modules from any accessible GitHub repository
2. Automatic version detection and folder organization
3. Batch processing and module cataloging
4. Integration with existing analysis and upgrade systems

Features:
- Multi-repository support with authentication
- Intelligent module detection and extraction
- Automatic version detection using pattern analysis
- Seamless integration with existing upload and analysis workflow
- Support for both public and private repositories
"""

import os
import shutil
import logging
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import zipfile
import tarfile

import git
import requests
from werkzeug.utils import secure_filename

from app import db
from models import OdooModule, ModuleAnalysis
from module_analyzer import ModuleAnalyzer

logger = logging.getLogger(__name__)

class GitHubModulePuller:
    """
    Advanced GitHub repository integration for module pulling and management.
    
    This class provides comprehensive functionality for:
    - Pulling modules from GitHub repositories
    - Automatic version detection and organization
    - Integration with existing analysis workflow
    - Support for multiple repository sources
    """
    
    def __init__(self):
        self.github_token = os.environ.get('GITHUB_TOKEN', '')
        self.temp_dir = None
        self.analyzer = ModuleAnalyzer()
        self.supported_extensions = {'.zip', '.tar', '.tar.gz', '.tgz'}
        
    def authenticate_github(self, token: str = None) -> bool:
        """
        Authenticate with GitHub using provided token.
        
        Args:
            token: GitHub personal access token
            
        Returns:
            True if authentication successful, False otherwise
        """
        if token:
            self.github_token = token
            
        if not self.github_token:
            logger.warning("No GitHub token provided - only public repositories accessible")
            return False
            
        # Test authentication
        try:
            headers = {'Authorization': f'token {self.github_token}'}
            response = requests.get('https://api.github.com/user', headers=headers)
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"Authenticated as GitHub user: {user_data.get('login')}")
                return True
            else:
                logger.error(f"GitHub authentication failed: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"GitHub authentication error: {e}")
            return False
    
    def list_repository_contents(self, repo_url: str, path: str = "") -> List[Dict[str, Any]]:
        """
        List contents of a GitHub repository.
        
        Args:
            repo_url: GitHub repository URL
            path: Path within repository to list
            
        Returns:
            List of repository contents with metadata
        """
        try:
            # Parse repository URL
            if repo_url.startswith('https://github.com/'):
                repo_path = repo_url.replace('https://github.com/', '')
            else:
                repo_path = repo_url
                
            # Remove .git suffix if present
            if repo_path.endswith('.git'):
                repo_path = repo_path[:-4]
            
            # Build API URL
            api_url = f"https://api.github.com/repos/{repo_path}/contents/{path}"
            
            # Make request with authentication if available
            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'
                
            response = requests.get(api_url, headers=headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to list repository contents: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error listing repository contents: {e}")
            return []
    
    def detect_odoo_modules(self, repo_url: str) -> List[Dict[str, Any]]:
        """
        Detect all Odoo modules in a GitHub repository using efficient GitHub Search API.

        Args:
            repo_url: GitHub repository URL

        Returns:
            List of detected modules with metadata
        """
        detected_modules = []

        # ===== ENHANCED MODULE DETECTION START =====
        # Extract owner and repo from URL
        try:
            parts = repo_url.replace('https://github.com/', '').split('/')
            owner, repo = parts[0], parts[1]
            print(f"🔍 Scanning {owner}/{repo} for Odoo modules...")
        except:
            print("⚠️  URL parsing failed, using fallback method")
            return self._detect_modules_fallback(repo_url)

        # Method 1: Use GitHub Search API to find manifest files (most efficient)
        try:
            search_url = "https://api.github.com/search/code"

            # Search for __manifest__.py files
            params = {
                'q': f'filename:__manifest__.py repo:{owner}/{repo}',
                'per_page': 100
            }

            # Build headers for API request
            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'

            response = requests.get(search_url, params=params, headers=headers)

            if response.status_code == 200:
                search_results = response.json()
                print(f"📦 Found {search_results.get('total_count', 0)} __manifest__.py files")

                for item in search_results.get('items', []):
                    # Extract module directory from file path
                    file_path = item['path']
                    module_path = '/'.join(file_path.split('/')[:-1])  # Remove __manifest__.py
                    module_name = module_path.split('/')[-1] if '/' in module_path else module_path

                    if module_name and module_name != '':  # Skip if empty
                        detected_modules.append({
                            'name': module_name,
                            'path': module_path,
                            'download_url': f"https://api.github.com/repos/{owner}/{repo}/contents/{module_path}",
                            'type': 'directory',
                            'size': 0  # Will be calculated later if needed
                        })

                # Also search for __openerp__.py (older Odoo versions)
                params['q'] = f'filename:__openerp__.py repo:{owner}/{repo}'
                response = requests.get(search_url, params=params, headers=headers)

                if response.status_code == 200:
                    search_results = response.json()
                    print(f"📦 Found {search_results.get('total_count', 0)} __openerp__.py files")

                    for item in search_results.get('items', []):
                        file_path = item['path']
                        module_path = '/'.join(file_path.split('/')[:-1])
                        module_name = module_path.split('/')[-1] if '/' in module_path else module_path

                        # Check if we already found this module with __manifest__.py
                        if module_name and module_name != '' and not any(m['name'] == module_name for m in detected_modules):
                            detected_modules.append({
                                'name': module_name,
                                'path': module_path,
                                'download_url': f"https://api.github.com/repos/{owner}/{repo}/contents/{module_path}",
                                'type': 'directory',
                                'size': 0
                            })

                print(f"✅ Found {len(detected_modules)} modules using GitHub Search API")
                return detected_modules

            elif response.status_code == 403:
                print("⚠️  GitHub API rate limit exceeded, using fallback method")
            else:
                print(f"⚠️  GitHub Search API failed ({response.status_code}), using fallback method")

        except Exception as e:
            print(f"⚠️  GitHub Search API error: {e}, using fallback method")

        # Method 2: Fallback to directory scanning (less efficient but more reliable)
        return self._detect_modules_fallback(repo_url)
        # ===== ENHANCED MODULE DETECTION END =====

    def _detect_modules_fallback(self, repo_url: str) -> List[Dict[str, Any]]:
        """
        Fallback method: scan directories for manifest files.
        More reliable but slower for large repositories.
        """
        detected_modules = []
        print("🔍 Using fallback directory scanning method...")

        def scan_directory(path: str = "", depth: int = 0):
            """Recursively scan directory for Odoo modules."""
            if depth > 2:  # Reduced depth to avoid timeout
                return

            try:
                contents = self.list_repository_contents(repo_url, path)
                print(f"📁 Scanning {path or 'root'}: {len(contents)} items")

                # Process in batches to avoid overwhelming the API
                for i, item in enumerate(contents):
                    if i > 100:  # Limit to first 100 items per directory to avoid timeout
                        print(f"⚠️  Limiting scan to first 100 items in {path or 'root'}")
                        break

                    if item['type'] == 'dir':
                        # Check if this directory contains a manifest file
                        try:
                            manifest_files = self.list_repository_contents(repo_url, item['path'])
                            has_manifest = any(f['name'] in ['__manifest__.py', '__openerp__.py']
                                             for f in manifest_files if f['type'] == 'file')

                            if has_manifest:
                                # This is an Odoo module
                                print(f"📦 Found module: {item['name']}")
                                detected_modules.append({
                                    'name': item['name'],
                                    'path': item['path'],
                                    'download_url': item['download_url'],
                                    'type': 'directory',
                                    'size': item.get('size', 0)
                                })
                            elif depth < 1:  # Only scan subdirectories for first level
                                scan_directory(item['path'], depth + 1)

                        except Exception as e:
                            print(f"⚠️  Error scanning {item['path']}: {e}")
                            continue

                    elif item['type'] == 'file':
                        # Check if this is a module archive
                        if any(item['name'].endswith(ext) for ext in self.supported_extensions):
                            detected_modules.append({
                                'name': item['name'],
                                'path': item['path'],
                                'download_url': item['download_url'],
                                'type': 'file',
                                'size': item.get('size', 0)
                            })

            except Exception as e:
                print(f"⚠️  Error scanning directory {path}: {e}")
                return

        scan_directory()
        print(f"✅ Fallback scan found {len(detected_modules)} modules")
        return detected_modules

    def download_module(self, module_info: Dict[str, Any], repo_url: str) -> Optional[str]:
        """
        Download a module from GitHub repository.
        
        Args:
            module_info: Module information from detect_odoo_modules
            repo_url: GitHub repository URL
            
        Returns:
            Path to downloaded module file or None if failed
        """
        try:
            # Create temporary directory if needed
            if not self.temp_dir:
                self.temp_dir = tempfile.mkdtemp(prefix="github_modules_")
            
            module_name = module_info['name']
            module_path = module_info['path']
            
            if module_info['type'] == 'file':
                # Direct file download
                download_url = module_info['download_url']
                
                headers = {}
                if self.github_token:
                    headers['Authorization'] = f'token {self.github_token}'
                
                response = requests.get(download_url, headers=headers)
                if response.status_code == 200:
                    # Save file
                    safe_filename = secure_filename(module_name)
                    file_path = os.path.join(self.temp_dir, safe_filename)
                    
                    with open(file_path, 'wb') as f:
                        f.write(response.content)
                    
                    return file_path
                else:
                    logger.error(f"Failed to download module file: {response.status_code}")
                    return None
                    
            elif module_info['type'] == 'directory':
                # Download entire directory as archive
                return self._download_directory_as_archive(repo_url, module_path, module_name)
                
        except Exception as e:
            logger.error(f"Error downloading module {module_name}: {e}")
            return None
    
    def _download_directory_as_archive(self, repo_url: str, module_path: str, module_name: str) -> Optional[str]:
        """
        Download a directory from GitHub as a ZIP archive.
        
        Args:
            repo_url: GitHub repository URL
            module_path: Path to module directory
            module_name: Module name
            
        Returns:
            Path to downloaded archive or None if failed
        """
        try:
            # Parse repository URL
            if repo_url.startswith('https://github.com/'):
                repo_path = repo_url.replace('https://github.com/', '')
            else:
                repo_path = repo_url
                
            if repo_path.endswith('.git'):
                repo_path = repo_path[:-4]
            
            # Clone repository to temporary location
            temp_repo_dir = tempfile.mkdtemp(prefix="github_repo_")
            
            if self.github_token:
                clone_url = f"https://{self.github_token}@github.com/{repo_path}.git"
            else:
                clone_url = f"https://github.com/{repo_path}.git"
            
            # Clone repository
            repo = git.Repo.clone_from(clone_url, temp_repo_dir)
            
            # Create archive of specific module directory
            module_dir = os.path.join(temp_repo_dir, module_path)
            if not os.path.exists(module_dir):
                logger.error(f"Module directory not found: {module_path}")
                return None
            
            # Create ZIP archive
            safe_filename = secure_filename(f"{module_name}.zip")
            archive_path = os.path.join(self.temp_dir, safe_filename)
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(module_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, module_dir)
                        zipf.write(file_path, arcname)
            
            # Cleanup temporary repository
            shutil.rmtree(temp_repo_dir)
            
            return archive_path
            
        except Exception as e:
            logger.error(f"Error creating directory archive: {e}")
            return None
    
    def pull_modules_from_repository(self, repo_url: str, target_versions: List[str] = None) -> Dict[str, Any]:
        """
        Pull all modules from a GitHub repository and process them.
        
        Args:
            repo_url: GitHub repository URL
            target_versions: List of target versions to analyze for
            
        Returns:
            Dictionary with processing results
        """
        results = {
            'repository_url': repo_url,
            'timestamp': datetime.now().isoformat(),
            'modules_detected': 0,
            'modules_downloaded': 0,
            'modules_processed': 0,
            'modules_failed': 0,
            'processed_modules': [],
            'failed_modules': [],
            'errors': []
        }
        
        try:
            # Detect modules in repository
            logger.info(f"Scanning repository: {repo_url}")
            detected_modules = self.detect_odoo_modules(repo_url)
            results['modules_detected'] = len(detected_modules)
            
            if not detected_modules:
                results['errors'].append("No Odoo modules detected in repository")
                return results
            
            logger.info(f"Found {len(detected_modules)} modules")
            
            # Process each detected module
            for module_info in detected_modules:
                try:
                    # Download module
                    module_path = self.download_module(module_info, repo_url)
                    if not module_path:
                        results['failed_modules'].append({
                            'name': module_info['name'],
                            'error': 'Download failed'
                        })
                        results['modules_failed'] += 1
                        continue
                    
                    results['modules_downloaded'] += 1
                    
                    # Process module through existing upload workflow
                    processed_module = self._process_downloaded_module(
                        module_path, module_info, repo_url, target_versions
                    )
                    
                    if processed_module:
                        results['processed_modules'].append(processed_module)
                        results['modules_processed'] += 1
                    else:
                        results['failed_modules'].append({
                            'name': module_info['name'],
                            'error': 'Processing failed'
                        })
                        results['modules_failed'] += 1
                        
                except Exception as e:
                    logger.error(f"Error processing module {module_info['name']}: {e}")
                    results['failed_modules'].append({
                        'name': module_info['name'],
                        'error': str(e)
                    })
                    results['modules_failed'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error pulling modules from repository: {e}")
            results['errors'].append(str(e))
            return results
        
        finally:
            # Cleanup temporary directory
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
    
    def _process_downloaded_module(self, module_path: str, module_info: Dict[str, Any], 
                                 repo_url: str, target_versions: List[str]) -> Optional[Dict[str, Any]]:
        """
        Process a downloaded module through the existing upload workflow.
        
        Args:
            module_path: Path to downloaded module file
            module_info: Module metadata from GitHub
            repo_url: Source repository URL
            target_versions: Target versions for analysis
            
        Returns:
            Processed module information or None if failed
        """
        try:
            # Create uploaded module record
            filename = os.path.basename(module_path)
            
            # Create uploads directory if it doesn't exist
            uploads_dir = os.path.join(os.getcwd(), 'uploads')
            os.makedirs(uploads_dir, exist_ok=True)
            
            # Move file to uploads directory
            final_path = os.path.join(uploads_dir, filename)
            shutil.move(module_path, final_path)
            
            # Create database record
            uploaded_module = OdooModule(
                filename=filename,
                original_filename=module_info['name'],
                file_path=final_path,
                file_size=os.path.getsize(final_path),
                upload_date=datetime.now(),
                analysis_status='pending',
                source_info=json.dumps({
                    'source': 'github',
                    'repository': repo_url,
                    'path': module_info['path'],
                    'type': module_info['type']
                })
            )
            
            db.session.add(uploaded_module)
            db.session.commit()
            
            # Analyze module for each target version
            analysis_results = []
            
            if not target_versions:
                target_versions = ['18.0']  # Default to latest
            
            for target_version in target_versions:
                try:
                    # Perform analysis
                    analysis_result = self.analyzer.analyze_module(final_path, target_version)
                    
                    # Create analysis record
                    analysis = ModuleAnalysis(
                        module_id=uploaded_module.id,
                        target_version=target_version,
                        analysis_date=datetime.now(),
                        compatibility_score=analysis_result.get('compatibility_score', 0),
                        compatibility_issues=analysis_result.get('compatibility_issues', []),
                        compatibility_warnings=analysis_result.get('compatibility_warnings', []),
                        analysis_data=analysis_result
                    )
                    
                    db.session.add(analysis)
                    analysis_results.append({
                        'target_version': target_version,
                        'compatibility_score': analysis_result.get('compatibility_score', 0),
                        'issues_count': len(analysis_result.get('compatibility_issues', [])),
                        'warnings_count': len(analysis_result.get('compatibility_warnings', []))
                    })
                    
                except Exception as e:
                    logger.error(f"Analysis failed for version {target_version}: {e}")
                    analysis_results.append({
                        'target_version': target_version,
                        'error': str(e)
                    })
            
            # Update module status
            uploaded_module.analysis_status = 'completed'
            db.session.commit()
            
            return {
                'module_id': uploaded_module.id,
                'filename': filename,
                'original_name': module_info['name'],
                'source_path': module_info['path'],
                'file_size': uploaded_module.file_size,
                'analysis_results': analysis_results
            }
            
        except Exception as e:
            logger.error(f"Error processing downloaded module: {e}")
            return None
    
    def get_accessible_repositories(self, username: str = None) -> List[Dict[str, Any]]:
        """
        Get list of repositories accessible to the authenticated user.
        
        Args:
            username: Specific username to get repositories for
            
        Returns:
            List of repository information
        """
        repositories = []
        
        try:
            if not self.github_token:
                logger.warning("No GitHub token available for repository listing")
                return repositories
            
            headers = {'Authorization': f'token {self.github_token}'}
            
            if username:
                # Get specific user's repositories
                url = f"https://api.github.com/users/{username}/repos"
            else:
                # Get authenticated user's repositories
                url = "https://api.github.com/user/repos"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                repos_data = response.json()
                
                for repo in repos_data:
                    repositories.append({
                        'name': repo['name'],
                        'full_name': repo['full_name'],
                        'description': repo.get('description', ''),
                        'url': repo['html_url'],
                        'clone_url': repo['clone_url'],
                        'private': repo['private'],
                        'language': repo.get('language', ''),
                        'updated_at': repo['updated_at']
                    })
            else:
                logger.error(f"Failed to get repositories: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error getting accessible repositories: {e}")
        
        return repositories

# Global instance
_github_puller = None

def get_github_puller() -> GitHubModulePuller:
    """Get the global GitHub module puller instance."""
    global _github_puller
    if _github_puller is None:
        _github_puller = GitHubModulePuller()
    return _github_puller

def pull_modules_from_github(repo_url: str, target_versions: List[str] = None) -> Dict[str, Any]:
    """
    Convenience function to pull modules from GitHub repository.
    
    Args:
        repo_url: GitHub repository URL
        target_versions: List of target versions for analysis
        
    Returns:
        Processing results dictionary
    """
    puller = get_github_puller()
    return puller.pull_modules_from_repository(repo_url, target_versions)

def get_user_repositories() -> List[Dict[str, Any]]:
    """
    Get list of repositories accessible to the authenticated user.
    
    Returns:
        List of repository information
    """
    puller = get_github_puller()
    return puller.get_accessible_repositories()