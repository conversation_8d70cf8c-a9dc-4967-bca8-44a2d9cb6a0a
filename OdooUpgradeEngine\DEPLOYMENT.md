# OdooUpgradeEngine - Complete Deployment Guide

## 🎯 Project Overview

**OdooUpgradeEngine** is a comprehensive AI-powered platform for automated Odoo module upgrades from v13 through v18, featuring enterprise-level bulk migration capabilities for production databases with 200+ modules.

### Core Architecture Components

1. **Module Analysis Engine** - Deep compatibility scanning and automated fixes
2. **Advanced Upgrader** - Complete version migration with Owl 2 conversion  
3. **Database Migration Engine** - Live PostgreSQL migration with OpenUpgrade
4. **Migration Orchestrator** - Coordinated module + database workflows
5. **Bulk Migration Manager** - Enterprise production database handling
6. **GitHub Automation** - Continuous integration pipeline

## 🚀 Quick Deployment Steps

### Option 1: GitHub Repository Setup

```bash
# 1. Clone the repository
git clone https://github.com/yerenwgventures/OdooUpgradeEngine.git
cd OdooUpgradeEngine

# 2. Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# 3. Install dependencies
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.1.1
pip install SQLAlchemy==2.0.23
pip install psycopg2-binary==2.9.9
pip install Werkzeug==2.3.7
pip install gunicorn==21.2.0
pip install email-validator==2.1.0
pip install python-dotenv==1.0.0
pip install requests==2.31.0
pip install GitPython==3.1.40
pip install chardet==5.2.0
pip install lxml==4.9.3

# 4. Set environment variables
export DATABASE_URL="postgresql://user:password@localhost:5432/odoo_upgrade"
export SESSION_SECRET="your-secure-session-secret-key-here"

# 5. Initialize database
python -c "from app import db; db.create_all()"

# 6. Start application
gunicorn --bind 0.0.0.0:5000 --reuse-port --reload main:app
```

### Option 2: Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create required directories
RUN mkdir -p uploads backups automation_logs config

# Expose port
EXPOSE 5000

# Start command
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "main:app"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=**************************************/odoo_upgrade
      - SESSION_SECRET=your-secure-session-secret
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads
      - ./backups:/app/backups
      - ./automation_logs:/app/automation_logs

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=odoo_upgrade
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### Option 3: Production Server Setup

```bash
# Server setup (Ubuntu/Debian)
sudo apt update
sudo apt install python3 python3-pip python3-venv postgresql postgresql-contrib nginx

# Create application user
sudo useradd -m -s /bin/bash odoo-upgrade
sudo su - odoo-upgrade

# Clone and setup application
git clone https://github.com/yerenwgventures/OdooUpgradeEngine.git
cd OdooUpgradeEngine
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup PostgreSQL database
sudo -u postgres createdb odoo_upgrade
sudo -u postgres createuser odoo_upgrade_user
sudo -u postgres psql -c "ALTER USER odoo_upgrade_user PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE odoo_upgrade TO odoo_upgrade_user;"

# Configure systemd service
sudo nano /etc/systemd/system/odoo-upgrade.service
```

```ini
# /etc/systemd/system/odoo-upgrade.service
[Unit]
Description=OdooUpgradeEngine
After=network.target

[Service]
Type=notify
User=odoo-upgrade
Group=odoo-upgrade
WorkingDirectory=/home/<USER>/OdooUpgradeEngine
Environment=DATABASE_URL=postgresql://odoo_upgrade_user:secure_password@localhost:5432/odoo_upgrade
Environment=SESSION_SECRET=your-very-secure-session-secret-key
ExecStart=/home/<USER>/OdooUpgradeEngine/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 main:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable odoo-upgrade
sudo systemctl start odoo-upgrade
sudo systemctl status odoo-upgrade
```

## 🔧 Configuration

### Environment Variables
```bash
# Required
DATABASE_URL=postgresql://user:password@host:port/database
SESSION_SECRET=your-flask-session-secret

# Optional GitHub Automation
GITHUB_TOKEN=your-github-personal-access-token
GITHUB_REPO=your-org/odoo-modules

# Optional Features
UPLOAD_FOLDER=/path/to/uploads
MAX_CONTENT_LENGTH=104857600  # 100MB
```

### Directory Structure Setup
```bash
# Create required directories
mkdir -p uploads/{modules,backups,temp}
mkdir -p automation_logs/{daily,manual,errors}
mkdir -p config/{automation,migration,secrets}
mkdir -p static/{css,js,images}
mkdir -p templates/{modules,migration,automation}

# Set permissions
chmod 755 uploads automation_logs config
chmod 644 static/css/* static/js/*
```

## 📊 Feature Configuration

### 1. Module Analysis Setup
```python
# In app.py or config file
ANALYSIS_CONFIG = {
    'supported_versions': ['13.0', '14.0', '15.0', '16.0', '17.0', '18.0'],
    'max_file_size': 100 * 1024 * 1024,  # 100MB
    'supported_formats': ['.zip', '.tar', '.tar.gz'],
    'analysis_timeout': 300,  # 5 minutes
}
```

### 2. Database Migration Configuration
```python
MIGRATION_CONFIG = {
    'backup_before_migration': True,
    'migration_timeout': 3600,  # 1 hour
    'rollback_on_error': True,
    'openupgrade_integration': True,
}
```

### 3. Bulk Migration Setup
```python
BULK_MIGRATION_CONFIG = {
    'max_modules_per_batch': 50,
    'phase_timeout': 1800,  # 30 minutes per phase
    'concurrent_migrations': 3,
    'complexity_thresholds': {
        'simple': 0.2,
        'medium': 0.5,
        'complex': 0.8,
        'critical': 1.0
    }
}
```

### 4. Automation Configuration
```json
{
  "github": {
    "repo_url": "https://github.com/your-org/odoo-modules.git",
    "branch": "main",
    "auto_commit": true,
    "auto_release": true
  },
  "schedule": {
    "daily_cycle": "02:00",
    "manual_triggers": true,
    "notification_email": "<EMAIL>"
  },
  "processing": {
    "version_progression": ["13.0", "14.0", "15.0", "16.0", "17.0", "18.0"],
    "skip_broken_modules": true,
    "create_backup_branches": true
  }
}
```

## 🔐 Security Setup

### 1. Database Security
```sql
-- Create dedicated database user
CREATE USER odoo_upgrade_app WITH PASSWORD 'strong_password';
GRANT CONNECT ON DATABASE odoo_upgrade TO odoo_upgrade_app;
GRANT USAGE ON SCHEMA public TO odoo_upgrade_app;
GRANT CREATE ON SCHEMA public TO odoo_upgrade_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO odoo_upgrade_app;
```

### 2. File Upload Security
```python
# Upload validation
ALLOWED_EXTENSIONS = {'zip', 'tar', 'gz'}
MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
```

### 3. Nginx Configuration
```nginx
# /etc/nginx/sites-available/odoo-upgrade
server {
    listen 80;
    server_name your-domain.com;

    client_max_body_size 100M;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /home/<USER>/OdooUpgradeEngine/static;
        expires 30d;
    }
}
```

## 📈 Monitoring & Maintenance

### 1. Health Checks
```python
# Health check endpoint
@app.route('/health')
def health_check():
    try:
        # Database connectivity
        db.session.execute('SELECT 1')
        
        # File system access
        uploads_accessible = os.access('uploads', os.W_OK)
        
        # Migration system status
        migration_ready = check_migration_components()
        
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'uploads': 'accessible' if uploads_accessible else 'error',
            'migration_system': 'ready' if migration_ready else 'error',
            'timestamp': datetime.utcnow().isoformat()
        })
    except Exception as e:
        return jsonify({'status': 'error', 'error': str(e)}), 500
```

### 2. Logging Setup
```python
import logging
from logging.handlers import RotatingFileHandler

# Configure logging
if not app.debug:
    file_handler = RotatingFileHandler('logs/odoo-upgrade.log', 
                                     maxBytes=10240000, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('OdooUpgradeEngine startup')
```

### 3. Backup Strategy
```bash
#!/bin/bash
# backup.sh - Daily backup script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/odoo-upgrade"

# Database backup
pg_dump -h localhost -U odoo_upgrade_user odoo_upgrade > \
    "$BACKUP_DIR/db_backup_$DATE.sql"

# File system backup
tar -czf "$BACKUP_DIR/files_backup_$DATE.tar.gz" \
    uploads/ automation_logs/ config/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

## 🚀 Production Deployment Checklist

### Pre-deployment
- [ ] PostgreSQL database setup and secured
- [ ] SSL certificates configured (Let's Encrypt recommended)
- [ ] Firewall rules configured (ports 80, 443, 22 only)
- [ ] System user created with limited privileges
- [ ] Required directories created with proper permissions
- [ ] Environment variables configured securely
- [ ] Backup strategy implemented

### Deployment
- [ ] Application code deployed from GitHub
- [ ] Dependencies installed in virtual environment
- [ ] Database migrations applied
- [ ] Static files served by Nginx
- [ ] Systemd service configured and enabled
- [ ] Log rotation configured
- [ ] Health checks passing

### Post-deployment
- [ ] Upload functionality tested
- [ ] Module analysis working
- [ ] Migration orchestrator functional
- [ ] Bulk migration tested with sample data
- [ ] Automation system configured (if using GitHub)
- [ ] Monitoring alerts configured
- [ ] Documentation updated with deployment details

## 📞 Support & Troubleshooting

### Common Issues

1. **Database Connection Errors**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql
sudo -u postgres psql -c "SELECT version();"

# Test connection
psql -h localhost -U odoo_upgrade_user -d odoo_upgrade -c "SELECT 1;"
```

2. **File Upload Issues**
```bash
# Check permissions
ls -la uploads/
sudo chown -R odoo-upgrade:odoo-upgrade uploads/
chmod 755 uploads/
```

3. **Migration Failures**
```bash
# Check logs
tail -f automation_logs/migration_*.log
journalctl -u odoo-upgrade -f
```

4. **Performance Issues**
```bash
# Monitor resources
htop
df -h
free -m

# Check database performance
sudo -u postgres psql odoo_upgrade -c "SELECT * FROM pg_stat_activity;"
```

### Maintenance Commands
```bash
# Restart application
sudo systemctl restart odoo-upgrade

# View logs
sudo journalctl -u odoo-upgrade -n 100

# Update application
cd /home/<USER>/OdooUpgradeEngine
git pull origin main
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart odoo-upgrade

# Database maintenance
sudo -u postgres vacuumdb odoo_upgrade
sudo -u postgres reindexdb odoo_upgrade
```

---

**Deployment completed successfully! Your OdooUpgradeEngine is ready for production use.**

For support: https://github.com/yerenwgventures/OdooUpgradeEngine/issues