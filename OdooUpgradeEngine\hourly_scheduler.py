"""
Hourly Automation Scheduler

This module runs every hour to:
1. Sync uploaded modules with GitHub automation folders
2. Trigger automation cycles for module upgrades
3. Clean up duplicate/outdated modules
4. Ensure system health and GitHub synchronization
"""

import threading
import time
import logging
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any
import os

from module_sync_manager import ModuleSync<PERSON>anager
from automation_system import OdooModuleAutomationSystem

logger = logging.getLogger(__name__)

class HourlyScheduler:
    """
    Scheduler that runs automation tasks every hour
    """

    def __init__(self):
        self.running = False
        self.thread = None
        self.sync_manager = ModuleSyncManager()
        self.automation_system = None
        self.last_run = None
        self.next_run = None

        # Initialize automation system if GitHub token is available
        if os.getenv("GITHUB_TOKEN"):
            try:
                self.automation_system = OdooModuleAutomationSystem()
                logger.info("Automation system initialized with GitHub integration")
            except Exception as e:
                logger.error(f"Could not initialize automation system: {str(e)}")

        self.setup_logging()

    def setup_logging(self):
        """Setup logging for scheduler"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('automation_logs/hourly_scheduler.log'),
                logging.StreamHandler()
            ]
        )

    def start(self):
        """Start the hourly scheduler"""
        if self.running:
            logger.warning("Scheduler is already running")
            return

        self.running = True
        self.thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.thread.start()

        # Calculate next run time
        self.next_run = datetime.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)

        logger.info(f"Hourly scheduler started. Next run: {self.next_run}")

    def stop(self):
        """Stop the hourly scheduler"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=10)
        logger.info("Hourly scheduler stopped")

    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                current_time = datetime.now()

                # Check if it's time for hourly run
                if self.next_run and current_time >= self.next_run:
                    logger.info("Starting hourly automation cycle...")
                    self._run_hourly_tasks()

                    # Schedule next run
                    self.last_run = current_time
                    self.next_run = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                    logger.info(f"Next scheduled run: {self.next_run}")

                # Sleep for 60 seconds before checking again
                time.sleep(60)

            except Exception as e:
                logger.error(f"Error in scheduler loop: {str(e)}")
                time.sleep(60)  # Wait before retrying

    def _run_hourly_tasks(self):
        """Run all hourly automation tasks"""
        task_results = {
            "timestamp": datetime.now().isoformat(),
            "sync_result": None,
            "automation_result": None,
            "cleanup_result": None,
            "errors": []
        }

        try:
            # Task 1: Sync uploaded modules with automation folders
            logger.info("Running module sync...")
            task_results["sync_result"] = self.sync_manager.sync_uploaded_modules()

            # Task 2: Run automation cycle if system is available
            if self.automation_system:
                logger.info("Running automation cycle...")
                task_results["automation_result"] = self.automation_system.run_automation_cycle()
            else:
                logger.warning("Automation system not available, skipping automation cycle")

            # Task 3: Clean up old versions
            logger.info("Running cleanup...")
            task_results["cleanup_result"] = self.sync_manager.cleanup_old_versions()

            # Save task results
            self._save_task_results(task_results)

            logger.info(f"Hourly tasks completed successfully: {task_results}")

        except Exception as e:
            error_msg = f"Error in hourly tasks: {str(e)}"
            logger.error(error_msg)
            task_results["errors"].append(error_msg)
            self._save_task_results(task_results)

    def _save_task_results(self, results: Dict[str, Any]):
        """Save task results to log file"""
        try:
            log_dir = "automation_logs"
            os.makedirs(log_dir, exist_ok=True)

            log_file = f"{log_dir}/hourly_results_{datetime.now().strftime('%Y%m%d')}.json"

            # Load existing results if file exists
            existing_results = []
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r') as f:
                        existing_results = json.load(f)
                except:
                    existing_results = []

            # Add new results
            existing_results.append(results)

            # Keep only last 24 entries (one day)
            if len(existing_results) > 24:
                existing_results = existing_results[-24:]

            # Save results
            with open(log_file, 'w') as f:
                json.dump(existing_results, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"Could not save task results: {str(e)}")

    def trigger_immediate_run(self) -> Dict[str, Any]:
        """Trigger an immediate run of all tasks"""
        logger.info("Triggering immediate hourly task run...")

        # Run tasks immediately
        self._run_hourly_tasks()

        # Reset next scheduled run to next hour
        self.next_run = datetime.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)

        return {
            "status": "completed",
            "timestamp": datetime.now().isoformat(),
            "next_scheduled_run": self.next_run.isoformat()
        }

    def get_status(self) -> Dict[str, Any]:
        """Get current scheduler status"""
        status = {
            "running": self.running,
            "last_run": self.last_run.isoformat() if self.last_run else None,
            "next_run": self.next_run.isoformat() if self.next_run else None,
            "automation_system_available": self.automation_system is not None,
            "github_token_configured": bool(os.getenv("GITHUB_TOKEN")),
            "sync_manager_status": self.sync_manager.get_sync_status()
        }

        return status

    def get_recent_results(self, hours: int = 24) -> list:
        """Get recent task results"""
        try:
            log_file = f"automation_logs/hourly_results_{datetime.now().strftime('%Y%m%d')}.json"

            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    results = json.load(f)

                # Filter by time if needed
                cutoff_time = datetime.now() - timedelta(hours=hours)
                filtered_results = []

                for result in results:
                    try:
                        result_time = datetime.fromisoformat(result["timestamp"])
                        if result_time >= cutoff_time:
                            filtered_results.append(result)
                    except:
                        # Include result if can't parse timestamp
                        filtered_results.append(result)

                return filtered_results

            return []

        except Exception as e:
            logger.error(f"Could not get recent results: {str(e)}")
            return []

# Global scheduler instance
_scheduler_instance = None

def get_scheduler() -> HourlyScheduler:
    """Get the global scheduler instance"""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = HourlyScheduler()
    return _scheduler_instance

def start_scheduler():
    """Start the hourly scheduler in a separate thread"""
    global scheduler_thread, scheduler_running

    if scheduler_running:
        logger.info("Scheduler already running")
        return

    try:
        scheduler_running = True
        scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
        scheduler_thread.start()
        logger.info("Hourly scheduler started successfully")
    except Exception as e:
        logger.error(f"Failed to start scheduler: {e}")
        scheduler_running = False

def stop_scheduler():
    """Stop the global scheduler"""
    global _scheduler_instance
    if _scheduler_instance:
        _scheduler_instance.stop()
        _scheduler_instance = None