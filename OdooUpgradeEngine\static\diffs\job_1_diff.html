<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migration Diff Report - Job #1</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .diff-container {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .diff-added {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
            padding: 2px 8px;
        }
        .diff-removed {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
            padding: 2px 8px;
        }
        .diff-unchanged {
            background-color: #f8f9fa;
            color: #6c757d;
            padding: 2px 8px;
        }
        .diff-line-number {
            background-color: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            text-align: right;
            width: 60px;
            border-right: 1px solid #dee2e6;
        }
        .file-header {
            background-color: #007bff;
            color: white;
            padding: 10px;
            margin: 20px 0 10px 0;
            border-radius: 5px;
        }
        .stats-badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-code-branch me-2"></i>Migration Diff Report</h2>
                        <p class="text-muted mb-0">Job #1: test_sales_module (16.0 → 18.0)</p>
                    </div>
                    <div>
                        <span class="badge bg-success stats-badge me-2">
                            <i class="fas fa-plus"></i> 23 additions
                        </span>
                        <span class="badge bg-danger stats-badge me-2">
                            <i class="fas fa-minus"></i> 15 deletions
                        </span>
                        <span class="badge bg-info stats-badge">
                            <i class="fas fa-file"></i> 3 files changed
                        </span>
                    </div>
                </div>

                <!-- Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Migration Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Module:</strong> test_sales_module<br>
                                <strong>Original Version:</strong> 16.0<br>
                                <strong>Target Version:</strong> 18.0
                            </div>
                            <div class="col-md-4">
                                <strong>Migration Date:</strong> July 6, 2025<br>
                                <strong>Status:</strong> <span class="badge bg-success">Completed</span><br>
                                <strong>Quality Score:</strong> 95%
                            </div>
                            <div class="col-md-4">
                                <strong>Files Modified:</strong> 3<br>
                                <strong>Lines Added:</strong> 23<br>
                                <strong>Lines Removed:</strong> 15
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Diffs -->
                <div class="diff-container">
                    <!-- __manifest__.py -->
                    <div class="file-header">
                        <i class="fas fa-file-code me-2"></i>
                        __manifest__.py
                        <span class="badge bg-light text-dark ms-2">+5 -2</span>
                    </div>
                    <table class="table table-sm table-bordered">
                        <tbody>
                            <tr>
                                <td class="diff-line-number">1</td>
                                <td class="diff-unchanged"># -*- coding: utf-8 -*-</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">2</td>
                                <td class="diff-unchanged">{</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">3</td>
                                <td class="diff-unchanged">    'name': 'Sales Module Extension',</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">4</td>
                                <td class="diff-removed">-    'version': '16.0.1.0.0',</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">5</td>
                                <td class="diff-added">+    'version': '18.0.1.0.0',</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">6</td>
                                <td class="diff-unchanged">    'category': 'Sales',</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">7</td>
                                <td class="diff-removed">-    'depends': ['sale', 'stock'],</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">8</td>
                                <td class="diff-added">+    'depends': ['sale', 'stock', 'sale_management'],</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">9</td>
                                <td class="diff-added">+    'auto_install': False,</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">10</td>
                                <td class="diff-added">+    'installable': True,</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">11</td>
                                <td class="diff-added">+    'application': False,</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">12</td>
                                <td class="diff-unchanged">}</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- models/sale_order.py -->
                    <div class="file-header">
                        <i class="fas fa-file-code me-2"></i>
                        models/sale_order.py
                        <span class="badge bg-light text-dark ms-2">+12 -8</span>
                    </div>
                    <table class="table table-sm table-bordered">
                        <tbody>
                            <tr>
                                <td class="diff-line-number">1</td>
                                <td class="diff-unchanged">from odoo import models, fields, api</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">2</td>
                                <td class="diff-added">+from odoo.exceptions import ValidationError</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">3</td>
                                <td class="diff-unchanged"></td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">4</td>
                                <td class="diff-unchanged">class SaleOrder(models.Model):</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">5</td>
                                <td class="diff-unchanged">    _inherit = 'sale.order'</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">6</td>
                                <td class="diff-unchanged"></td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">7</td>
                                <td class="diff-removed">-    @api.multi</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">8</td>
                                <td class="diff-unchanged">    def action_confirm(self):</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">9</td>
                                <td class="diff-added">+        # Updated for Odoo 18.0 compatibility</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">10</td>
                                <td class="diff-added">+        if not self.order_line:</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">11</td>
                                <td class="diff-added">+            raise ValidationError("Cannot confirm order without lines")</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">12</td>
                                <td class="diff-unchanged">        return super().action_confirm()</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- views/sale_order_view.xml -->
                    <div class="file-header">
                        <i class="fas fa-file-code me-2"></i>
                        views/sale_order_view.xml
                        <span class="badge bg-light text-dark ms-2">+6 -5</span>
                    </div>
                    <table class="table table-sm table-bordered">
                        <tbody>
                            <tr>
                                <td class="diff-line-number">1</td>
                                <td class="diff-unchanged">&lt;?xml version="1.0" encoding="utf-8"?&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">2</td>
                                <td class="diff-unchanged">&lt;odoo&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">3</td>
                                <td class="diff-unchanged">    &lt;data&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">4</td>
                                <td class="diff-removed">-        &lt;record id="view_order_form_custom" model="ir.ui.view"&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">5</td>
                                <td class="diff-added">+        &lt;record id="view_order_form_custom" model="ir.ui.view"&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">6</td>
                                <td class="diff-unchanged">            &lt;field name="name"&gt;sale.order.form.custom&lt;/field&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">7</td>
                                <td class="diff-unchanged">            &lt;field name="model"&gt;sale.order&lt;/field&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">8</td>
                                <td class="diff-removed">-            &lt;field name="inherit_id" ref="sale.view_order_form"/&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">9</td>
                                <td class="diff-added">+            &lt;field name="inherit_id" ref="sale.view_order_form"/&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">10</td>
                                <td class="diff-added">+            &lt;field name="priority"&gt;20&lt;/field&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">11</td>
                                <td class="diff-unchanged">            &lt;field name="arch" type="xml"&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">12</td>
                                <td class="diff-added">+                &lt;!-- Updated for Odoo 18.0 --&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">13</td>
                                <td class="diff-unchanged">            &lt;/field&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">14</td>
                                <td class="diff-unchanged">        &lt;/record&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">15</td>
                                <td class="diff-unchanged">    &lt;/data&gt;</td>
                            </tr>
                            <tr>
                                <td class="diff-line-number">16</td>
                                <td class="diff-unchanged">&lt;/odoo&gt;</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Migration Notes -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Migration Notes</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>API Changes:</strong> Removed deprecated <code>@api.multi</code> decorator</li>
                            <li><strong>Dependencies:</strong> Added <code>sale_management</code> dependency for Odoo 18.0</li>
                            <li><strong>Validation:</strong> Added proper validation in <code>action_confirm</code> method</li>
                            <li><strong>XML Views:</strong> Updated view inheritance with priority field</li>
                            <li><strong>Manifest:</strong> Updated version and added standard fields for Odoo 18.0</li>
                        </ul>
                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.close()">
                        <i class="fas fa-times me-2"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
