"""
Manual Intervention Queue Manager - Week 5 Implementation

This module provides comprehensive queue management for manual interventions
that require human review during the migration process. It implements
priority-based assignment, workflow management, and resolution tracking.

Features:
- Priority-based queue management
- Job assignment to human reviewers
- Approval/rejection workflow
- Queue statistics and reporting
- Automatic escalation for critical issues
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from models import db, ManualIntervention, MigrationJob, MigrationStatus

class InterventionPriority(Enum):
    """Priority levels for manual interventions"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class InterventionStatus(Enum):
    """Status values for interventions"""
    PENDING = 'pending'
    ASSIGNED = 'assigned'
    IN_REVIEW = 'in_review'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    MODIFIED = 'modified'
    ESCALATED = 'escalated'

@dataclass
class QueueStatistics:
    """Statistics for the intervention queue"""
    total_pending: int
    total_assigned: int
    total_completed: int
    critical_count: int
    high_priority_count: int
    average_resolution_time: float
    oldest_pending_hours: float

@dataclass
class ReviewerWorkload:
    """Workload information for a reviewer"""
    reviewer_id: str
    assigned_count: int
    completed_count: int
    average_resolution_time: float
    current_capacity: int

class ManualInterventionManager:
    """
    Comprehensive manager for manual intervention queue operations.
    
    Handles priority-based assignment, workflow management, and provides
    analytics for the manual review process.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.max_assignments_per_reviewer = 5
        self.critical_escalation_hours = 2
        self.high_priority_escalation_hours = 8
        self.medium_priority_escalation_hours = 24
        
    def create_intervention(self, 
                          job_id: str,
                          intervention_type: str,
                          title: str,
                          description: str,
                          severity: str = 'medium',
                          file_path: str = None,
                          line_number: int = None,
                          code_context: str = None,
                          metadata: Dict[str, Any] = None) -> ManualIntervention:
        """
        Create a new manual intervention item.
        
        Args:
            job_id: Migration job ID
            intervention_type: Type of intervention needed
            title: Short description of the issue
            description: Detailed description
            severity: Priority level (low, medium, high, critical)
            file_path: Relevant file path if applicable
            line_number: Line number in file if applicable
            code_context: Code snippet for context
            metadata: Additional metadata
            
        Returns:
            Created ManualIntervention object
        """
        try:
            intervention = ManualIntervention(
                job_id=job_id,
                intervention_type=intervention_type,
                title=title,
                description=description,
                severity=severity,
                file_path=file_path,
                line_number=line_number,
                code_context=code_context,
                status=InterventionStatus.PENDING.value,
                intervention_metadata=metadata or {}
            )
            
            db.session.add(intervention)
            db.session.commit()
            
            self.logger.info(f"Created intervention {intervention.id} for job {job_id} with severity {severity}")
            
            # Auto-assign critical interventions
            if severity == 'critical':
                self._auto_assign_critical(intervention)
            
            return intervention
            
        except Exception as e:
            self.logger.error(f"Failed to create intervention: {str(e)}")
            db.session.rollback()
            raise
    
    def get_queue_statistics(self) -> QueueStatistics:
        """Get comprehensive queue statistics"""
        try:
            # Count interventions by status
            total_pending = ManualIntervention.query.filter_by(status='pending').count()
            total_assigned = ManualIntervention.query.filter(
                ManualIntervention.status.in_(['assigned', 'in_review'])
            ).count()
            total_completed = ManualIntervention.query.filter(
                ManualIntervention.status.in_(['approved', 'rejected', 'modified'])
            ).count()
            
            # Count by priority
            critical_count = ManualIntervention.query.filter_by(
                severity='critical', status='pending'
            ).count()
            high_priority_count = ManualIntervention.query.filter_by(
                severity='high', status='pending'
            ).count()
            
            # Calculate average resolution time
            completed_interventions = ManualIntervention.query.filter(
                ManualIntervention.resolved_at.isnot(None)
            ).all()
            
            if completed_interventions:
                resolution_times = [
                    (intervention.resolved_at - intervention.created_at).total_seconds() / 3600
                    for intervention in completed_interventions
                ]
                average_resolution_time = sum(resolution_times) / len(resolution_times)
            else:
                average_resolution_time = 0.0
            
            # Find oldest pending intervention
            oldest_pending = ManualIntervention.query.filter_by(
                status='pending'
            ).order_by(ManualIntervention.created_at.asc()).first()
            
            if oldest_pending:
                oldest_pending_hours = (datetime.utcnow() - oldest_pending.created_at).total_seconds() / 3600
            else:
                oldest_pending_hours = 0.0
            
            return QueueStatistics(
                total_pending=total_pending,
                total_assigned=total_assigned,
                total_completed=total_completed,
                critical_count=critical_count,
                high_priority_count=high_priority_count,
                average_resolution_time=average_resolution_time,
                oldest_pending_hours=oldest_pending_hours
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get queue statistics: {str(e)}")
            raise
    
    def get_prioritized_queue(self, limit: int = 50) -> List[ManualIntervention]:
        """
        Get prioritized list of pending interventions.
        
        Args:
            limit: Maximum number of interventions to return
            
        Returns:
            List of interventions ordered by priority and age
        """
        try:
            # Custom ordering: critical first, then by creation time
            interventions = ManualIntervention.query.filter_by(
                status='pending'
            ).order_by(
                # Critical interventions first
                db.case(
                    (ManualIntervention.severity == 'critical', 1),
                    (ManualIntervention.severity == 'high', 2),
                    (ManualIntervention.severity == 'medium', 3),
                    (ManualIntervention.severity == 'low', 4),
                    else_=5
                ),
                # Then by age (oldest first)
                ManualIntervention.created_at.asc()
            ).limit(limit).all()
            
            return interventions
            
        except Exception as e:
            self.logger.error(f"Failed to get prioritized queue: {str(e)}")
            raise
    
    def assign_intervention(self, intervention_id: int, reviewer_id: str) -> bool:
        """
        Assign an intervention to a reviewer.
        
        Args:
            intervention_id: ID of the intervention
            reviewer_id: ID of the reviewer
            
        Returns:
            True if assignment successful
        """
        try:
            intervention = ManualIntervention.query.get(intervention_id)
            if not intervention:
                self.logger.error(f"Intervention {intervention_id} not found")
                return False
            
            if intervention.status != 'pending':
                self.logger.error(f"Intervention {intervention_id} is not pending")
                return False
            
            # Check reviewer workload
            current_assignments = ManualIntervention.query.filter(
                ManualIntervention.resolved_by == reviewer_id,
                ManualIntervention.status.in_(['assigned', 'in_review'])
            ).count()
            
            if current_assignments >= self.max_assignments_per_reviewer:
                self.logger.warning(f"Reviewer {reviewer_id} at maximum capacity")
                return False
            
            # Make assignment
            intervention.status = 'assigned'
            intervention.resolved_by = reviewer_id
            
            # Update metadata
            if not intervention.intervention_metadata:
                intervention.intervention_metadata = {}
            intervention.intervention_metadata['assigned_at'] = datetime.utcnow().isoformat()
            
            db.session.commit()
            
            self.logger.info(f"Assigned intervention {intervention_id} to reviewer {reviewer_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to assign intervention: {str(e)}")
            db.session.rollback()
            return False
    
    def resolve_intervention(self,
                           intervention_id: int,
                           resolution: str,
                           notes: str = None,
                           reviewer_id: str = None) -> bool:
        """
        Resolve an intervention with approval/rejection.
        
        Args:
            intervention_id: ID of the intervention
            resolution: 'approved', 'rejected', or 'modified'
            notes: Resolution notes
            reviewer_id: ID of the reviewer making the resolution
            
        Returns:
            True if resolution successful
        """
        try:
            intervention = ManualIntervention.query.get(intervention_id)
            if not intervention:
                self.logger.error(f"Intervention {intervention_id} not found")
                return False
            
            if intervention.status in ['approved', 'rejected', 'modified']:
                self.logger.error(f"Intervention {intervention_id} already resolved")
                return False
            
            # Update intervention
            intervention.status = resolution
            intervention.resolution_notes = notes
            intervention.resolved_at = datetime.utcnow()
            
            if reviewer_id:
                intervention.resolved_by = reviewer_id
            
            # Update metadata
            if not intervention.intervention_metadata:
                intervention.intervention_metadata = {}
            intervention.intervention_metadata['resolved_at'] = datetime.utcnow().isoformat()
            intervention.intervention_metadata['resolution'] = resolution
            
            db.session.commit()
            
            # Update associated migration job if all interventions resolved
            self._check_job_intervention_completion(intervention.job_id)
            
            self.logger.info(f"Resolved intervention {intervention_id} as {resolution}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to resolve intervention: {str(e)}")
            db.session.rollback()
            return False
    
    def escalate_overdue_interventions(self) -> List[ManualIntervention]:
        """
        Escalate interventions that have been pending too long.
        
        Returns:
            List of escalated interventions
        """
        try:
            escalated = []
            now = datetime.utcnow()
            
            # Find overdue critical interventions (2+ hours)
            critical_cutoff = now - timedelta(hours=self.critical_escalation_hours)
            critical_overdue = ManualIntervention.query.filter(
                ManualIntervention.severity == 'critical',
                ManualIntervention.status == 'pending',
                ManualIntervention.created_at <= critical_cutoff
            ).all()
            
            # Find overdue high priority interventions (8+ hours)
            high_cutoff = now - timedelta(hours=self.high_priority_escalation_hours)
            high_overdue = ManualIntervention.query.filter(
                ManualIntervention.severity == 'high',
                ManualIntervention.status == 'pending',
                ManualIntervention.created_at <= high_cutoff
            ).all()
            
            # Find overdue medium priority interventions (24+ hours)
            medium_cutoff = now - timedelta(hours=self.medium_priority_escalation_hours)
            medium_overdue = ManualIntervention.query.filter(
                ManualIntervention.severity == 'medium',
                ManualIntervention.status == 'pending',
                ManualIntervention.created_at <= medium_cutoff
            ).all()
            
            # Escalate all overdue interventions
            for intervention in critical_overdue + high_overdue + medium_overdue:
                intervention.status = 'escalated'
                if not intervention.intervention_metadata:
                    intervention.intervention_metadata = {}
                intervention.intervention_metadata['escalated_at'] = now.isoformat()
                intervention.intervention_metadata['escalation_reason'] = 'overdue'
                escalated.append(intervention)
            
            if escalated:
                db.session.commit()
                self.logger.warning(f"Escalated {len(escalated)} overdue interventions")
            
            return escalated
            
        except Exception as e:
            self.logger.error(f"Failed to escalate interventions: {str(e)}")
            db.session.rollback()
            return []
    
    def get_reviewer_workload(self, reviewer_id: str) -> ReviewerWorkload:
        """Get workload statistics for a specific reviewer"""
        try:
            # Count current assignments
            assigned_count = ManualIntervention.query.filter(
                ManualIntervention.resolved_by == reviewer_id,
                ManualIntervention.status.in_(['assigned', 'in_review'])
            ).count()
            
            # Count completed assignments
            completed_count = ManualIntervention.query.filter(
                ManualIntervention.resolved_by == reviewer_id,
                ManualIntervention.status.in_(['approved', 'rejected', 'modified'])
            ).count()
            
            # Calculate average resolution time
            completed_interventions = ManualIntervention.query.filter(
                ManualIntervention.resolved_by == reviewer_id,
                ManualIntervention.resolved_at.isnot(None)
            ).all()
            
            if completed_interventions:
                resolution_times = [
                    (intervention.resolved_at - intervention.created_at).total_seconds() / 3600
                    for intervention in completed_interventions
                ]
                average_resolution_time = sum(resolution_times) / len(resolution_times)
            else:
                average_resolution_time = 0.0
            
            current_capacity = max(0, self.max_assignments_per_reviewer - assigned_count)
            
            return ReviewerWorkload(
                reviewer_id=reviewer_id,
                assigned_count=assigned_count,
                completed_count=completed_count,
                average_resolution_time=average_resolution_time,
                current_capacity=current_capacity
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get reviewer workload: {str(e)}")
            raise
    
    def _auto_assign_critical(self, intervention: ManualIntervention):
        """Automatically assign critical interventions to available reviewers"""
        try:
            # Find reviewer with lowest current workload
            # In a real system, this would query a user/reviewer table
            # For now, we'll just mark it as needing immediate attention
            intervention.intervention_metadata = intervention.intervention_metadata or {}
            intervention.intervention_metadata['auto_assignment_attempted'] = True
            intervention.intervention_metadata['requires_immediate_attention'] = True
            
            db.session.commit()
            
            self.logger.info(f"Marked critical intervention {intervention.id} for immediate attention")
            
        except Exception as e:
            self.logger.error(f"Failed to auto-assign critical intervention: {str(e)}")
    
    def _check_job_intervention_completion(self, job_id: str):
        """Check if all interventions for a job are resolved and continue workflow"""
        try:
            pending_interventions = ManualIntervention.query.filter(
                ManualIntervention.job_id == job_id,
                ManualIntervention.status.in_(['pending', 'assigned', 'in_review', 'escalated'])
            ).count()
            
            if pending_interventions == 0:
                # All interventions resolved, update job to continue workflow
                job = MigrationJob.query.get(job_id)
                if job and job.status == MigrationStatus.MANUAL_INTERVENTION:
                    job.status = MigrationStatus.DB_MIGRATING
                    job.current_phase = "Continuing Migration"
                    db.session.commit()
                    
                    self.logger.info(f"All interventions resolved for job {job_id}, continuing migration")
            
        except Exception as e:
            self.logger.error(f"Failed to check intervention completion: {str(e)}")

def create_intervention_for_job(job_id: str, 
                              title: str, 
                              description: str, 
                              severity: str = 'medium',
                              intervention_type: str = 'manual_review',
                              **kwargs) -> ManualIntervention:
    """
    Convenience function to create interventions from other modules.
    
    Args:
        job_id: Migration job ID
        title: Intervention title
        description: Detailed description
        severity: Priority level
        intervention_type: Type of intervention
        **kwargs: Additional arguments for intervention creation
        
    Returns:
        Created ManualIntervention object
    """
    manager = ManualInterventionManager()
    return manager.create_intervention(
        job_id=job_id,
        intervention_type=intervention_type,
        title=title,
        description=description,
        severity=severity,
        **kwargs
    )

def main():
    """Test the manual intervention manager"""
    manager = ManualInterventionManager()
    
    # Test queue statistics
    stats = manager.get_queue_statistics()
    print(f"Queue Statistics: {stats}")
    
    # Test prioritized queue
    queue = manager.get_prioritized_queue(limit=10)
    print(f"Prioritized queue has {len(queue)} items")

if __name__ == "__main__":
    main()