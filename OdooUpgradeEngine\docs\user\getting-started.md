# 🚀 Getting Started with Odoo Upgrade Engine

## 📋 **Quick Start Guide**

This guide will help you get the Odoo Upgrade Engine up and running in minutes.

---

## 🔧 **Prerequisites**

### **System Requirements**
- **Python 3.10+** (recommended)
- **Git** for repository management
- **4GB RAM minimum** (8GB recommended for large modules)
- **10GB free disk space** for module storage

### **Optional Dependencies**
- **Redis** (for background processing and performance optimization)
- **Docker** (for isolated testing environments)

---

## 📦 **Installation**

### **Step 1: Clone the Repository**
```bash
git clone https://github.com/yerenwgventures/OdooUpgradeEngine.git
cd OdooUpgradeEngine
```

### **Step 2: Install Dependencies**
```bash
# Install Python dependencies
pip install -r requirements.txt

# Optional: Install Redis for background processing
# Ubuntu/Debian: sudo apt-get install redis-server
# macOS: brew install redis
# Windows: Download from https://redis.io/download
```

### **Step 3: Initialize Database**
```bash
# Initialize the application database
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### **Step 4: Start the Application**
```bash
# Start the web application
python app.py
```

The application will be available at: **http://localhost:5000**

---

## 🌐 **First Steps**

### **1. Access the Dashboard**
Open your browser and navigate to: **http://localhost:5000**

You'll see the main dashboard with:
- **Migration Orchestrator**: Main workflow interface
- **GitHub Integration**: Connect to repositories
- **AI Providers**: Configure AI services
- **System Status**: Monitor application health

### **2. Configure AI Providers (Optional)**
Navigate to: **http://localhost:5000/ai_providers**

Configure one or more AI providers:
- **OpenAI GPT-4**: For advanced code analysis
- **Anthropic Claude**: For complex transformations
- **Google Gemini**: For additional AI capabilities

### **3. Test with Sample Repository**
Navigate to: **http://localhost:5000/github_integration**

Try with a sample OCA repository:
- **Repository URL**: `https://github.com/OCA/server-tools`
- **Target Version**: `18.0`
- **Limit**: `3` (for testing)

---

## 🔄 **Basic Workflow**

### **Step 1: Connect to GitHub Repository**
1. Go to **GitHub Integration** page
2. Enter repository URL (e.g., `https://github.com/OCA/server-tools`)
3. Select target Odoo version (e.g., `18.0`)
4. Choose migration mode:
   - **Direct**: Single-step upgrade
   - **Pipeline**: Progressive multi-version upgrade

### **Step 2: Pull and Analyze Modules**
1. Click **"Pull Modules"**
2. Monitor progress in real-time
3. Review detected modules and their metadata
4. Check for any errors or warnings

### **Step 3: Start Migration**
1. Go to **Migration Orchestrator**
2. Select modules to migrate
3. Choose migration strategy:
   - **Individual**: Migrate one module at a time
   - **Batch**: Migrate multiple modules together
   - **Pipeline**: Progressive version upgrades

### **Step 4: Monitor Progress**
1. Track migration status in real-time
2. View detailed logs and progress updates
3. Handle any manual interventions if required
4. Review migration results and diffs

### **Step 5: Review and Deploy**
1. Examine generated code changes
2. Review visual diffs and migration reports
3. Test upgraded modules (if testing framework configured)
4. Download or sync back to GitHub

---

## 📊 **Understanding the Interface**

### **Main Dashboard**
- **System Overview**: Current status and statistics
- **Recent Activity**: Latest migrations and operations
- **Quick Actions**: Common tasks and shortcuts
- **Health Monitoring**: System performance metrics

### **Migration Orchestrator**
- **Module Selection**: Choose modules to migrate
- **Migration Configuration**: Set parameters and options
- **Progress Tracking**: Real-time status updates
- **Results Review**: Examine migration outcomes

### **GitHub Integration**
- **Repository Management**: Connect and manage repositories
- **Module Discovery**: Automatic Odoo module detection
- **Sync Operations**: Upload/download modules
- **Branch Management**: Handle Git branches and commits

### **AI Providers**
- **Provider Configuration**: Set up AI services
- **Usage Monitoring**: Track AI API usage
- **Performance Metrics**: AI response times and accuracy
- **Cost Tracking**: Monitor API costs and usage

---

## 🧪 **Testing Your Setup**

### **Quick Health Check**
```bash
# Test basic functionality
curl http://localhost:5000/health

# Expected response: {"status": "healthy", "timestamp": "..."}
```

### **Test GitHub Integration**
```bash
# Test repository scanning
curl -X POST http://localhost:5000/api/github/pull-modules \
  -H "Content-Type: application/json" \
  -d '{
    "repository_url": "https://github.com/OCA/server-tools",
    "target_version": "18.0",
    "limit": 1
  }'
```

### **Test Migration Pipeline**
1. Upload a sample module via the web interface
2. Start a test migration
3. Monitor progress and review results
4. Check generated diffs and reports

---

## 🔧 **Configuration Options**

### **Environment Variables**
```bash
# Database configuration
DATABASE_URL=sqlite:///instance/odoo_upgrade.db

# AI Provider API keys (optional)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key

# GitHub integration (optional)
GITHUB_TOKEN=your_github_token

# Redis configuration (optional)
REDIS_URL=redis://localhost:6379/0
```

### **Application Settings**
Access via: **http://localhost:5000/system_settings**

Configure:
- **Migration preferences**
- **Performance settings**
- **Notification preferences**
- **Security options**

---

## 🆘 **Troubleshooting**

### **Common Issues**

**Application won't start:**
```bash
# Check Python version
python --version  # Should be 3.10+

# Check dependencies
pip install -r requirements.txt

# Check database initialization
python -c "from app import create_app; print('✅ App loads successfully')"
```

**GitHub integration not working:**
- Verify repository URL is accessible
- Check GitHub token permissions (if using private repos)
- Ensure network connectivity

**Migration fails:**
- Check module format and structure
- Verify target version compatibility
- Review error logs in the interface

**Performance issues:**
- Install Redis for background processing
- Increase system memory allocation
- Check disk space availability

### **Getting Help**
- **Documentation**: Check the `docs/` directory
- **Logs**: Review application logs for detailed error information
- **Health Dashboard**: Monitor system status at `/health_dashboard`
- **GitHub Issues**: Report bugs and request features

---

## 🎯 **Next Steps**

### **For Basic Users**
1. **Explore Features**: Try different migration modes and options
2. **Test with Your Modules**: Upload and migrate your own modules
3. **Review Documentation**: Read the complete feature list in FEATURES.md

### **For Advanced Users**
1. **Configure AI Providers**: Set up multiple AI services for better results
2. **Set up Background Processing**: Install Redis for improved performance
3. **Explore API**: Use the REST API for automation and integration

### **For Developers**
1. **Review Architecture**: Understand the system design
2. **Contribute**: Check contributing guidelines
3. **Extend Functionality**: Add custom migration rules and transformations

---

**🎉 Congratulations! You're now ready to use the Odoo Upgrade Engine for your module migration needs.**

For more detailed information, see:
- **[Complete Feature List](../../FEATURES.md)**
- **[User Guide](user-guide.md)**
- **[API Documentation](../api/endpoints.md)**
- **[Troubleshooting Guide](troubleshooting.md)**
