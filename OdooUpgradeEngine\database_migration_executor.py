import subprocess
import os
import logging
from models import MigrationJob

class DatabaseMigrationExecutor:
    def __init__(self, job: MigrationJob):
        self.job = job
        self.module_name = os.path.basename(job.upgraded_module_path)
        self.target_version = job.target_version
        self.upgraded_code_path = os.path.dirname(job.upgraded_module_path)
        self.db_name = f"job_{self.job.id}_v{self.target_version.replace('.', '_')}"

    def run_migration(self):
        log_output = []
        log_output.append(f"--- Starting Database Migration for {self.module_name} to v{self.target_version} ---")
        log_output.append(f"Database Name: {self.db_name}")

        docker_command = [
            "docker", "run", "--rm",
            "-v", f"{os.path.abspath(self.upgraded_code_path)}:/mnt/extra-addons",
            "odooupgrade/odoo-openupgrade:17.0",
            "--database", self.db_name,
            "--init", self.module_name,
            f"--upgrade-path=/path/to/OpenUpgrade/{self.target_version}",
            "--stop-after-init",
            "--log-level=info",
            "--without-demo=all",
        ]

        log_output.append(f"Executing Docker command: {' '.join(docker_command)}")

        try:
            process = subprocess.Popen(
                docker_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8'
            )
            for line in iter(process.stdout.readline, ''):
                clean_line = line.strip()
                if clean_line:
                    logging.info(clean_line)
                    log_output.append(clean_line)
            process.communicate()

            if process.returncode != 0:
                log_output.append(f"\nERROR: Database migration process failed with exit code {process.returncode}")
                return False, "\n".join(log_output)

            log_output.append("\n--- Database Migration Completed Successfully ---")
            return True, "\n".join(log_output)
        except Exception as e:
            error_msg = f"\nFATAL ERROR: An unexpected exception occurred during migration: {e}"
            logging.error(error_msg, exc_info=True)
            log_output.append(error_msg)
            return False, "\n".join(log_output)