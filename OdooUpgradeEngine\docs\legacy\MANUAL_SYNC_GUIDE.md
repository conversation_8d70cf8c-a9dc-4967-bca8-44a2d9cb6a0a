# Manual GitHub Sync Guide - Critical Files

## Overview
Due to git lock file issues, these critical files need manual sync to your GitHub repository. Here's the exact content for each file:

---

## 1. automation_config.json (Priority: CRITICAL)

**File Path**: `automation_config.json`
**Action**: Replace entire file content

```json
{
  "github": {
    "repo_url": "https://github.com/yerenwgventures/OdooUpgradeEngine.git",
    "branch": "main",
    "token": "GITHUB_TOKEN",
    "auto_commit": true,
    "commit_message_template": "Auto-upgrade {module_name} to Odoo {version}"
  },
  "directories": {
    "base_path": "./odoo_modules",
    "original_modules": {
      "v13": "v13_original",
      "v14": "v14_original",
      "v15": "v15_original",
      "v16": "v16_original",
      "v17": "v17_original",
      "v18": "v18_original"
    },
    "upgraded_modules": {
      "v14": "v14_upgraded",
      "v15": "v15_upgraded",
      "v16": "v16_upgraded",
      "v17": "v17_upgraded",
      "v18": "v18_upgraded"
    },
    "backup": "backups",
    "logs": "automation_logs"
  },
  "processing": {
    "batch_size": 5,
    "delay_between_batches": 30,
    "max_retries": 3,
    "quality_threshold": 85.0,
    "auto_fix_enabled": true,
    "advanced_upgrade_enabled": true
  },
  "filters": {
    "excluded_modules": [
      "test_module",
      "deprecated_module"
    ],
    "included_modules": [],
    "min_module_size": 1024,
    "max_module_size": 104857600
  },
  "scheduling": {
    "enabled": true,
    "cron_schedule": "0 2 * * *",
    "max_concurrent_jobs": 3
  },
  "notifications": {
    "enabled": true,
    "webhook_url": "",
    "email_recipients": [],
    "slack_webhook": ""
  }
}
```

---

## 2. github_sync.py (Priority: CRITICAL - NEW FILE)

**File Path**: `github_sync.py`
**Action**: Create new file with this content

```python
#!/usr/bin/env python3
"""
GitHub Sync Script for OdooUpgradeEngine
========================================

This script provides manual GitHub repository synchronization capabilities
for the OdooUpgradeEngine project, useful when automated git operations
are blocked or need manual intervention.
"""

import os
import subprocess
import json
import sys
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GitHubSync:
    """Manual GitHub synchronization utility for OdooUpgradeEngine"""
    
    def __init__(self, config_path: str = "automation_config.json"):
        """Initialize GitHub sync with configuration"""
        self.config = self._load_config(config_path)
        self.repo_url = self.config.get("github", {}).get("repo_url", "")
        self.branch = self.config.get("github", {}).get("branch", "main")
        self.token = os.environ.get("GITHUB_TOKEN", "")
        
        if not self.token:
            logger.warning("GITHUB_TOKEN environment variable not set")
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file {config_path} not found")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return {}
    
    def check_git_status(self) -> Tuple[bool, str]:
        """Check current git status and identify issues"""
        try:
            # Check if we're in a git repository
            result = subprocess.run(
                ["git", "rev-parse", "--is-inside-work-tree"],
                capture_output=True,
                text=True,
                check=True
            )
            
            # Check for uncommitted changes
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True,
                text=True,
                check=True
            )
            
            if result.stdout.strip():
                return True, f"Uncommitted changes detected:\n{result.stdout}"
            else:
                return True, "Repository is clean"
                
        except subprocess.CalledProcessError as e:
            return False, f"Git error: {e.stderr}"
    
    def get_modified_files(self) -> List[str]:
        """Get list of modified files"""
        try:
            result = subprocess.run(
                ["git", "diff", "--name-only", "HEAD"],
                capture_output=True,
                text=True,
                check=True
            )
            
            modified_files = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            # Also check untracked files
            result = subprocess.run(
                ["git", "ls-files", "--others", "--exclude-standard"],
                capture_output=True,
                text=True,
                check=True
            )
            
            untracked_files = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            return modified_files + untracked_files
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error getting modified files: {e}")
            return []
    
    def sync_specific_files(self, file_paths: List[str]) -> bool:
        """Sync specific files to GitHub"""
        if not file_paths:
            logger.info("No files to sync")
            return True
        
        try:
            # Add files to staging
            subprocess.run(["git", "add"] + file_paths, check=True)
            
            # Commit changes
            commit_message = f"Sync critical files: {', '.join(file_paths[:3])}"
            if len(file_paths) > 3:
                commit_message += f" and {len(file_paths) - 3} more files"
            
            subprocess.run(["git", "commit", "-m", commit_message], check=True)
            
            # Push to remote
            if self.token:
                auth_url = self.repo_url.replace("https://", f"https://{self.token}@")
                subprocess.run(["git", "push", auth_url, self.branch], check=True)
            else:
                subprocess.run(["git", "push", "origin", self.branch], check=True)
            
            logger.info(f"Successfully synced {len(file_paths)} files to GitHub")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error syncing files: {e}")
            return False
    
    def force_sync_all(self) -> bool:
        """Force sync all modified files"""
        modified_files = self.get_modified_files()
        
        if not modified_files:
            logger.info("No files to sync")
            return True
        
        logger.info(f"Found {len(modified_files)} modified files")
        for file in modified_files:
            logger.info(f"  - {file}")
        
        return self.sync_specific_files(modified_files)
    
    def reset_git_lock(self) -> bool:
        """Attempt to reset git lock file"""
        lock_file = Path(".git/index.lock")
        
        if lock_file.exists():
            try:
                lock_file.unlink()
                logger.info("Git lock file removed")
                return True
            except Exception as e:
                logger.error(f"Could not remove git lock file: {e}")
                return False
        else:
            logger.info("No git lock file found")
            return True

def main():
    """Main function for manual GitHub sync"""
    sync = GitHubSync()
    
    # Check git status
    is_git_repo, status_msg = sync.check_git_status()
    print(f"Git Status: {status_msg}")
    
    if not is_git_repo:
        print("Not in a git repository or git error occurred")
        sys.exit(1)
    
    # Get modified files
    modified_files = sync.get_modified_files()
    
    if not modified_files:
        print("No files to sync")
        sys.exit(0)
    
    print(f"\nFound {len(modified_files)} modified files:")
    for file in modified_files:
        print(f"  - {file}")
    
    # Ask user for confirmation
    response = input("\nWould you like to sync these files? (y/n): ")
    
    if response.lower() == 'y':
        # Attempt to reset git lock first
        sync.reset_git_lock()
        
        # Sync files
        if sync.force_sync_all():
            print("✅ Successfully synced all files to GitHub")
        else:
            print("❌ Failed to sync files")
            sys.exit(1)
    else:
        print("Sync cancelled")

if __name__ == "__main__":
    main()
```

---

## Quick Sync Instructions

1. **Save automation_config.json**: Copy the JSON content above and replace your repository's automation_config.json
2. **Create github_sync.py**: Copy the Python script above and create a new file github_sync.py in your repository
3. **Run manual sync**: After adding these files, you can run `python github_sync.py` to sync remaining files

These two files are the most critical for GitHub integration functionality.