{% extends "base.html" %}

{% block title %}Review Migration for {{ job.module.name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- ===== AI RECOMMENDATIONS PANEL START ===== -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-robot me-2"></i>AI Analysis & Recommendations</h5>
                </div>
                <div class="card-body" id="ai-analysis-panel">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading AI analysis...</span>
                        </div>
                        <p class="mt-2">Loading AI recommendations...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning">
                    <h5 class="mb-0"><i class="fas fa-code-branch me-2"></i>Migration Review: <strong>{{ job.module.name }}</strong> to v{{ job.target_version }}</h5>
                </div>
                <div class="card-body">
                    <p>
                        The automated code transformation is complete. Please review the AI analysis and visual changes below. Approving this will
                        initiate the **irreversible** database migration and testing phases.
                    </p>
                    <ul>
                        <li><strong>Job ID:</strong> {{ job.id }}</li>
                        <li><strong>Module:</strong> {{ job.module.name }} ({{ job.module.version }})</li>
                        <li><strong>Target Version:</strong> {{ job.target_version }}</li>
                    </ul>
                    <div class="d-flex gap-2 mt-3">
                        <form action="{{ url_for('main.approve_migration', job_id=job.id) }}" method="POST" onsubmit="return confirm('Are you sure you want to approve these changes and proceed with the database migration? This cannot be undone.');">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check-circle me-2"></i>Approve & Continue
                            </button>
                        </form>
                        <a href="{{ url_for('main.module_details', module_id=job.module_id) }}" class="btn btn-danger">
                            <i class="fas fa-times-circle me-2"></i>Reject & Go Back
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- ===== AI RECOMMENDATIONS PANEL END ===== -->

    <div class="card">
        <div class="card-header bg-dark text-white">
            <h3 class="h5 mb-0">Visual Difference Report</h3>
        </div>
        <div class="card-body p-0">
            <div style="width: 100%; height: 80vh; border: 0;">
                <iframe srcdoc="{{ diff_report.content|e }}" style="width: 100%; height: 100%; border: 0;"></iframe>
            </div>
        </div>
    </div>
</div>

<!-- ===== AI ANALYSIS JAVASCRIPT START ===== -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load AI analysis for this migration job
    loadAIAnalysis({{ job.id }});
});

function loadAIAnalysis(jobId) {
    fetch(`/api/ai-analysis/${jobId}`)
        .then(response => response.json())
        .then(data => {
            const panel = document.getElementById('ai-analysis-panel');

            if (data.success && data.ai_analysis) {
                const analysis = data.ai_analysis;
                panel.innerHTML = generateAIAnalysisHTML(analysis);
            } else {
                panel.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>AI Analysis Unavailable</strong><br>
                        ${data.message || 'AI analysis could not be performed for this migration.'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading AI analysis:', error);
            document.getElementById('ai-analysis-panel').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Error Loading AI Analysis</strong><br>
                    Please refresh the page to try again.
                </div>
            `;
        });
}

function generateAIAnalysisHTML(analysis) {
    const confidenceColor = analysis.confidence_score >= 0.8 ? 'success' :
                           analysis.confidence_score >= 0.6 ? 'warning' : 'danger';

    const riskColor = analysis.risk_level === 'low' ? 'success' :
                     analysis.risk_level === 'medium' ? 'warning' : 'danger';

    let html = `
        <div class="mb-3">
            <h6><i class="fas fa-chart-line me-2"></i>Confidence Score</h6>
            <div class="progress mb-2">
                <div class="progress-bar bg-${confidenceColor}" style="width: ${analysis.confidence_score * 100}%"></div>
            </div>
            <small class="text-muted">${(analysis.confidence_score * 100).toFixed(1)}% confidence</small>
        </div>

        <div class="mb-3">
            <h6><i class="fas fa-shield-alt me-2"></i>Risk Level</h6>
            <span class="badge bg-${riskColor}">${analysis.risk_level.toUpperCase()}</span>
        </div>

        <div class="mb-3">
            <h6><i class="fas fa-cogs me-2"></i>Strategy</h6>
            <small class="text-muted">${analysis.migration_strategy}</small>
        </div>
    `;

    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += `
            <div class="mb-3">
                <h6><i class="fas fa-lightbulb me-2"></i>Recommendations</h6>
                <div class="list-group list-group-flush">
        `;
        analysis.recommendations.forEach(rec => {
            const priorityColor = rec.priority === 'high' ? 'danger' :
                                 rec.priority === 'medium' ? 'warning' : 'info';
            html += `
                <div class="list-group-item p-2">
                    <small class="badge bg-${priorityColor} me-2">${rec.priority}</small>
                    <small>${rec.description}</small>
                </div>
            `;
        });
        html += `</div></div>`;
    }

    if (analysis.potential_issues && analysis.potential_issues.length > 0) {
        html += `
            <div class="mb-3">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Potential Issues</h6>
                <div class="list-group list-group-flush">
        `;
        analysis.potential_issues.forEach(issue => {
            const impactColor = issue.impact === 'critical' ? 'danger' :
                               issue.impact === 'medium' ? 'warning' : 'info';
            html += `
                <div class="list-group-item p-2">
                    <small class="badge bg-${impactColor} me-2">${issue.impact}</small>
                    <small><strong>${issue.issue}:</strong> ${issue.mitigation}</small>
                </div>
            `;
        });
        html += `</div></div>`;
    }

    return html;
}
</script>
<!-- ===== AI ANALYSIS JAVASCRIPT END ===== -->

{% endblock %}