#!/usr/bin/env python3
"""
Test script to verify all 4 XML transformation rules are working correctly.
"""

import tempfile
import os
from xml_safe_upgrader import XMLSafeUpgrader

def test_xml_rules():
    """Test all 4 XML transformation rules"""
    
    # Test XML content with all patterns that should be transformed
    test_xml = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Test for Rule 1: nolabel removal -->
        <record model="ir.ui.view" id="test_view_1">
            <field name="name">Test View</field>
            <field name="model">test.model</field>
            <field name="arch" type="xml">
                <form>
                    <field name="test_field" nolabel="1"/>
                    <field name="another_field" nolabel="1" class="existing_class"/>
                </form>
            </field>
        </record>
        
        <!-- Test for Rule 2: t-out to t-esc conversion -->
        <template id="test_template">
            <div>
                <span t-out="user.name"/>
                <p t-out="product.description"/>
            </div>
        </template>
        
        <!-- Test for Rule 3: Deprecated attributes -->
        <record model="ir.ui.view" id="test_view_2">
            <field name="name">Test View 2</field>
            <field name="model">test.model</field>
            <field name="arch" type="xml">
                <form version="1.0">
                    <field name="test_field"/>
                </form>
            </field>
        </record>
        
        <!-- Test for Rule 4: View structure modernization -->
        <record model="ir.ui.view" id="test_view_3">
            <field name="name">Test View 3</field>
            <field name="model">test.model</field>
            <field name="arch" type="xml">
                <form>
                    <notebook>
                        <page string="Page 1">
                            <group col="2">
                                <field name="field1"/>
                                <field name="field2"/>
                            </group>
                            <separator string="Section"/>
                        </page>
                    </notebook>
                </form>
            </field>
        </record>
    </data>
</odoo>'''
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
        f.write(test_xml)
        temp_file = f.name
    
    try:
        # Run the XML upgrader
        upgrader = XMLSafeUpgrader()
        result = upgrader.upgrade_xml_file(temp_file)
        
        print("=== XML Rules Test Results ===")
        print(f"Success: {result['success']}")
        print(f"Changes made: {result['changes_count']}")
        
        if result['success']:
            print("\n=== Changes Applied ===")
            for change in result['changes_made']:
                print(f"- {change}")
            
            # Read the transformed file
            with open(temp_file, 'r') as f:
                transformed_content = f.read()
            
            print("\n=== Transformed XML ===")
            print(transformed_content)
            
            # Verify specific transformations
            print("\n=== Verification ===")
            
            # Rule 1: nolabel should be removed and o_no_label class added
            if 'nolabel="1"' not in transformed_content:
                print("✓ Rule 1: nolabel attributes removed")
            else:
                print("✗ Rule 1: nolabel attributes still present")
            
            if 'o_no_label' in transformed_content:
                print("✓ Rule 1: o_no_label class added")
            else:
                print("✗ Rule 1: o_no_label class not added")
            
            # Rule 2: t-out should be converted to t-esc
            if 't-out=' not in transformed_content:
                print("✓ Rule 2: t-out attributes removed")
            else:
                print("✗ Rule 2: t-out attributes still present")
            
            if 't-esc=' in transformed_content:
                print("✓ Rule 2: t-esc attributes added")
            else:
                print("✗ Rule 2: t-esc attributes not added")
            
            # Rule 3: version attribute should be removed
            if 'version="1.0"' not in transformed_content:
                print("✓ Rule 3: deprecated version attribute removed")
            else:
                print("✗ Rule 3: deprecated version attribute still present")
            
            # Rule 4: notebook should get class, group col should be modernized
            if 'oe_notebook' in transformed_content:
                print("✓ Rule 4: notebook class added")
            else:
                print("✗ Rule 4: notebook class not added")
            
            if 'col="2"' not in transformed_content:
                print("✓ Rule 4: group col attribute removed")
            else:
                print("✗ Rule 4: group col attribute still present")
                
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    finally:
        # Clean up
        if os.path.exists(temp_file):
            os.unlink(temp_file)
        backup_file = temp_file + '.backup'
        if os.path.exists(backup_file):
            os.unlink(backup_file)

if __name__ == '__main__':
    test_xml_rules()