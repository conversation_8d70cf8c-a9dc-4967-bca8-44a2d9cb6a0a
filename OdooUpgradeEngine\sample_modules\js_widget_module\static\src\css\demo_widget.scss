// Modern SCSS styling for Odoo 18 compatibility testing
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

// SCSS variables
$primary-color: #714B67;
$secondary-color: #875A7B;
$background-light: #f8f9fa;
$border-radius: 8px;
$transition-duration: 0.3s;

// Mixins for reusable styles
@mixin button-style($bg-color: $primary-color) {
  background-color: $bg-color;
  border: none;
  border-radius: $border-radius;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all $transition-duration ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// Component styles
.demo-widget {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: $background-light;
  border: 1px solid #dee2e6;
  border-radius: $border-radius;
  padding: 1.5rem;
  margin: 1rem 0;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    h3 {
      color: $primary-color;
      margin: 0;
      font-weight: 600;
    }
  }
  
  &__counter {
    background: white;
    border: 2px solid $secondary-color;
    border-radius: $border-radius;
    padding: 1rem;
    text-align: center;
    margin: 1rem 0;
    
    .count-display {
      font-size: 2rem;
      font-weight: 600;
      color: $secondary-color;
      margin-bottom: 0.5rem;
    }
    
    .message {
      color: #666;
      font-style: italic;
    }
  }
  
  &__controls {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    
    .btn-increment {
      @include button-style($primary-color);
    }
    
    .btn-reset {
      @include button-style(#dc3545);
    }
  }
  
  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;
    
    &__controls {
      flex-direction: column;
      
      button {
        width: 100%;
      }
    }
  }
}

// Animation for counter updates
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.demo-widget__counter.updated {
  animation: pulse 0.3s ease-in-out;
}

// Dark theme support (Odoo 18 feature)
@media (prefers-color-scheme: dark) {
  .demo-widget {
    background: #2d3436;
    border-color: #636e72;
    color: #ddd;
    
    &__counter {
      background: #34495e;
      border-color: $secondary-color;
    }
    
    .message {
      color: #b2bec3;
    }
  }
}

// Legacy CSS patterns that might need updates
.legacy-styles {
  // Old vendor prefixes
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  
  // Deprecated CSS properties
  filter: alpha(opacity=50); // IE specific
  zoom: 1; // IE hasLayout trigger
  
  // Old flexbox syntax
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
}