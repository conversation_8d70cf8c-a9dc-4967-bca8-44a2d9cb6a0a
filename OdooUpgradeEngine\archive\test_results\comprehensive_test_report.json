{"test_summary": {"start_time": "2025-07-13T14:15:56.157364", "end_time": "2025-07-13T14:15:56.433206", "total_tests": 38, "passed_tests": 33, "failed_tests": 5}, "detailed_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server is responding", "details": null, "timestamp": "2025-07-13T14:15:56.166122"}, {"test_name": "Page Load: Dashboard", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.172907"}, {"test_name": "Page Load: Upload Mo<PERSON><PERSON>", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.177492"}, {"test_name": "Page Load: GitHub Integration", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.185642"}, {"test_name": "Page Load: <PERSON><PERSON>s", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.194651"}, {"test_name": "Page Load: Migration Orchestrator", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.211356"}, {"test_name": "Page Load: Manual Interventions", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.217071"}, {"test_name": "Page Load: <PERSON><PERSON><PERSON>", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.240762"}, {"test_name": "Page Load: Migration Results", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.252064"}, {"test_name": "Page Load: Review Queue", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.261117"}, {"test_name": "Page Load: Completed Migrations", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.269273"}, {"test_name": "Page Load: Success Reports", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.277423"}, {"test_name": "Page Load: Performance Analytics", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.286447"}, {"test_name": "Page Load: Migration History", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.303652"}, {"test_name": "Page Load: Testing Dashboard", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.316914"}, {"test_name": "Page Load: Docker Environments", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.321743"}, {"test_name": "Page Load: Test Results", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.333801"}, {"test_name": "Page Load: AI Providers", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.347668"}, {"test_name": "Page Load: AI Learning Dashboard", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.352674"}, {"test_name": "Page Load: Health Monitor", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.367597"}, {"test_name": "Page Load: System Settings", "success": true, "message": "Page loads correctly", "details": null, "timestamp": "2025-07-13T14:15:56.373068"}, {"test_name": "All Pages Load", "success": true, "message": "20/20 pages loaded successfully", "details": null, "timestamp": "2025-07-13T14:15:56.373078"}, {"test_name": "API: Dashboard Data API", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-07-13T14:15:56.378251"}, {"test_name": "API: Migration Status API", "success": true, "message": "API responds correctly", "details": null, "timestamp": "2025-07-13T14:15:56.382990"}, {"test_name": "API: Migration Jobs API", "success": true, "message": "API responds correctly", "details": null, "timestamp": "2025-07-13T14:15:56.391889"}, {"test_name": "API: AI Learning Insights API", "success": true, "message": "API responds correctly", "details": null, "timestamp": "2025-07-13T14:15:56.395437"}, {"test_name": "All APIs Work", "success": false, "message": "3/4 APIs working correctly", "details": null, "timestamp": "2025-07-13T14:15:56.395446"}, {"test_name": "AI Provider Test: deepseek", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-07-13T14:15:56.397639"}, {"test_name": "AI Provider Test: o<PERSON><PERSON>", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-07-13T14:15:56.399727"}, {"test_name": "AI Provider Testing", "success": false, "message": "0/2 provider tests working", "details": null, "timestamp": "2025-07-13T14:15:56.399735"}, {"test_name": "JavaScript: /static/js/main.js", "success": true, "message": "File accessible", "details": null, "timestamp": "2025-07-13T14:15:56.405470"}, {"test_name": "JavaScript: /static/js/ai-integration.js", "success": true, "message": "File accessible", "details": null, "timestamp": "2025-07-13T14:15:56.407759"}, {"test_name": "JavaScript: /static/js/real-time-updates.js", "success": true, "message": "File accessible", "details": null, "timestamp": "2025-07-13T14:15:56.409861"}, {"test_name": "JavaScript: /static/js/search-filter.js", "success": true, "message": "File accessible", "details": null, "timestamp": "2025-07-13T14:15:56.412246"}, {"test_name": "JavaScript Files", "success": true, "message": "4/4 JS files accessible", "details": null, "timestamp": "2025-07-13T14:15:56.412257"}, {"test_name": "Database Connectivity", "success": true, "message": "Database operations working", "details": null, "timestamp": "2025-07-13T14:15:56.419556"}, {"test_name": "Menu Navigation", "success": true, "message": "Menu structure present (6/6 sections)", "details": null, "timestamp": "2025-07-13T14:15:56.426061"}, {"test_name": "Responsive Design", "success": true, "message": "Bootstrap elements present (7/7)", "details": null, "timestamp": "2025-07-13T14:15:56.433157"}]}