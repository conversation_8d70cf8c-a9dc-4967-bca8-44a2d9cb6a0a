"""
AI Migration Assistant - Advanced OpenAI Integration for True Migration System

This module provides AI-powered analysis and assistance for Odoo module migrations.
It integrates with OpenAI to provide intelligent recommendations, error analysis,
and automated fix suggestions throughout the migration workflow.

Features:
- Intelligent migration analysis using OpenAI GPT-4o
- Automated error detection and fix suggestions  
- Risk assessment and migration planning
- Context-aware code transformation recommendations
- Integration with migration orchestrator workflow
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# Import AI provider manager for multi-provider support
try:
    from ai_provider_manager import get_ai_provider_manager, analyze_with_ai, AIProviderType
    AI_PROVIDER_AVAILABLE = True
except ImportError:
    AI_PROVIDER_AVAILABLE = False

# Fallback to OpenAI if provider manager not available
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MigrationContext:
    """Context information for AI analysis"""
    source_version: str
    target_version: str
    module_name: str
    module_files: List[str]
    detected_issues: List[Dict[str, Any]]
    transformation_results: Dict[str, Any]
    error_logs: List[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class AIAnalysisResult:
    """Result of AI analysis"""
    confidence_score: float  # 0.0 to 1.0
    risk_level: str  # low, medium, high, critical
    recommendations: List[Dict[str, Any]]
    suggested_fixes: List[Dict[str, Any]]
    migration_strategy: str
    potential_issues: List[Dict[str, Any]]
    analysis_summary: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class AIMigrationAssistant:
    """
    AI-powered migration assistant using OpenAI for intelligent analysis.
    
    This assistant provides comprehensive AI analysis throughout the migration
    workflow, offering intelligent recommendations and automated fixes.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ai_available = False
        self.provider_manager = None
        self.openai_client = None  # Fallback OpenAI client
        
        # Try to use AI provider manager first (supports multiple providers)
        if AI_PROVIDER_AVAILABLE:
            try:
                self.provider_manager = get_ai_provider_manager()
                self.ai_available = True
                provider_stats = self.provider_manager.get_provider_stats()
                if 'active_provider' in provider_stats:
                    self.logger.info(f"AI Migration Assistant initialized with {provider_stats['active_provider']}")
                else:
                    self.logger.info("AI Migration Assistant initialized with multi-provider support")
            except Exception as e:
                self.logger.error(f"Failed to initialize AI provider manager: {e}")
                self.ai_available = False
        
        # Fallback to OpenAI if provider manager not available
        if not self.ai_available and OPENAI_AVAILABLE and os.environ.get("OPENAI_API_KEY"):
            try:
                self.openai_client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
                self.ai_available = True
                self.logger.info("AI Migration Assistant initialized with OpenAI fallback")
            except Exception as e:
                self.logger.warning(f"Failed to initialize OpenAI client: {str(e)}")
                self.ai_available = False
        
        if not self.ai_available:
            self.logger.info("AI Migration Assistant initialized in fallback mode (no AI providers available)")
    
    def analyze_migration_context(self, context: MigrationContext) -> AIAnalysisResult:
        """
        Perform comprehensive AI analysis of migration context.
        
        Args:
            context: Migration context with module and transformation information
            
        Returns:
            Detailed AI analysis with recommendations and fixes
        """
        if not self.openai_available:
            return self._fallback_analysis(context)
        
        try:
            # Prepare context for AI analysis
            analysis_prompt = self._build_analysis_prompt(context)
            
            # Get AI analysis using multi-provider system
            if self.provider_manager:
                response = self.provider_manager.generate_response(
                    prompt=analysis_prompt + "\n\nRespond with JSON format only.",
                    system_prompt=self._get_system_prompt(),
                    response_format={"type": "json_object"},
                    temperature=0.1,
                    max_tokens=2000
                )
                
                if response['success']:
                    ai_response = json.loads(response['content'])
                else:
                    raise Exception("AI provider response failed")
            
            # Fallback to OpenAI client
            elif self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
                    messages=[
                        {"role": "system", "content": self._get_system_prompt()},
                        {"role": "user", "content": analysis_prompt + "\n\nRespond with JSON format only."}
                    ],
                    response_format={"type": "json_object"},
                    temperature=0.1,
                    max_tokens=2000
                )
                ai_response = json.loads(response.choices[0].message.content)
            
            else:
                return self._fallback_analysis(context)
            
            # Convert to structured result
            result = AIAnalysisResult(
                confidence_score=ai_response.get("confidence_score", 0.8),
                risk_level=ai_response.get("risk_level", "medium"),
                recommendations=ai_response.get("recommendations", []),
                suggested_fixes=ai_response.get("suggested_fixes", []),
                migration_strategy=ai_response.get("migration_strategy", "standard"),
                potential_issues=ai_response.get("potential_issues", []),
                analysis_summary=ai_response.get("analysis_summary", "AI analysis completed")
            )
            
            self.logger.info(f"AI analysis completed for {context.module_name} ({context.source_version} → {context.target_version})")
            return result
            
        except Exception as e:
            self.logger.error(f"AI analysis failed: {str(e)}")
            return self._fallback_analysis(context)
    
    def analyze_migration_errors(self, error_logs: List[str], context: MigrationContext) -> Dict[str, Any]:
        """
        Analyze migration errors and provide AI-powered fix suggestions.
        
        Args:
            error_logs: List of error messages and logs
            context: Migration context for additional information
            
        Returns:
            Dictionary with error analysis and fix suggestions
        """
        if not self.openai_available:
            return self._fallback_error_analysis(error_logs)
        
        try:
            # Prepare error analysis prompt
            error_prompt = self._build_error_analysis_prompt(error_logs, context)
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert Odoo migration specialist. Analyze errors and provide specific, actionable fix suggestions. Respond with JSON containing error_analysis, root_causes, and fix_suggestions."
                    },
                    {
                        "role": "user",
                        "content": error_prompt
                    }
                ],
                response_format={"type": "json_object"},
                temperature=0.1,
                max_tokens=1500
            )
            
            error_analysis = json.loads(response.choices[0].message.content)
            self.logger.info(f"AI error analysis completed for {len(error_logs)} errors")
            
            return error_analysis
            
        except Exception as e:
            self.logger.error(f"AI error analysis failed: {str(e)}")
            return self._fallback_error_analysis(error_logs)
    
    def suggest_migration_improvements(self, migration_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze migration results and suggest improvements for future migrations.
        
        Args:
            migration_results: Results from completed migration
            
        Returns:
            Dictionary with improvement suggestions and lessons learned
        """
        if not self.openai_available:
            return {"suggestions": ["Enable OpenAI integration for AI-powered improvement suggestions"]}
        
        try:
            improvement_prompt = self._build_improvement_prompt(migration_results)
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert Odoo migration consultant. Analyze migration results and provide specific improvement suggestions for future migrations. Focus on practical, actionable recommendations."
                    },
                    {
                        "role": "user",
                        "content": improvement_prompt
                    }
                ],
                response_format={"type": "json_object"},
                temperature=0.2,
                max_tokens=1000
            )
            
            improvements = json.loads(response.choices[0].message.content)
            self.logger.info("AI improvement suggestions generated")
            
            return improvements
            
        except Exception as e:
            self.logger.error(f"AI improvement analysis failed: {str(e)}")
            return {"suggestions": [f"AI analysis unavailable: {str(e)}"]}
    
    def _build_analysis_prompt(self, context: MigrationContext) -> str:
        """Build comprehensive analysis prompt for AI"""
        return f"""
Analyze this Odoo module migration from version {context.source_version} to {context.target_version}:

Module: {context.module_name}
Files: {', '.join(context.module_files[:10])}{'...' if len(context.module_files) > 10 else ''}
Detected Issues: {json.dumps(context.detected_issues[:5], indent=2) if context.detected_issues else 'None'}
Transformation Results: {json.dumps(context.transformation_results, indent=2)}

Please provide a comprehensive analysis including:
1. Confidence score (0.0-1.0) for migration success
2. Risk level (low/medium/high/critical)
3. Specific recommendations for this migration
4. Suggested fixes for any issues
5. Migration strategy recommendation
6. Potential issues to watch for
7. Analysis summary

Respond with JSON in this exact format:
{{
    "confidence_score": 0.85,
    "risk_level": "medium",
    "recommendations": [
        {{"type": "code_review", "description": "Review API changes", "priority": "high"}},
        {{"type": "testing", "description": "Test core functionality", "priority": "medium"}}
    ],
    "suggested_fixes": [
        {{"issue": "API deprecation", "fix": "Update to new API", "code_example": "example code"}}
    ],
    "migration_strategy": "incremental",
    "potential_issues": [
        {{"issue": "Database changes", "impact": "medium", "mitigation": "Backup database"}}
    ],
    "analysis_summary": "Migration appears feasible with moderate complexity..."
}}
"""
    
    def _build_error_analysis_prompt(self, error_logs: List[str], context: MigrationContext) -> str:
        """Build error analysis prompt"""
        errors_text = "\n".join(error_logs[:10])  # Limit to first 10 errors
        
        return f"""
Analyze these Odoo migration errors for module {context.module_name} ({context.source_version} → {context.target_version}):

ERRORS:
{errors_text}

Provide detailed analysis including:
1. Root cause analysis for each error type
2. Specific fix suggestions with code examples where applicable
3. Priority ranking of fixes
4. Dependencies between fixes

Respond with JSON format:
{{
    "error_analysis": "Overall analysis of error patterns...",
    "root_causes": ["cause1", "cause2"],
    "fix_suggestions": [
        {{"error": "specific error", "fix": "specific solution", "priority": "high", "code": "example"}}
    ]
}}
"""
    
    def _build_improvement_prompt(self, migration_results: Dict[str, Any]) -> str:
        """Build improvement suggestions prompt"""
        return f"""
Analyze these migration results and suggest improvements:

{json.dumps(migration_results, indent=2)}

Provide suggestions for:
1. Migration process improvements
2. Error prevention strategies
3. Performance optimizations
4. Best practices adoption

Respond with JSON:
{{
    "suggestions": ["suggestion1", "suggestion2"],
    "best_practices": ["practice1", "practice2"],
    "preventive_measures": ["measure1", "measure2"]
}}
"""
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for AI migration analysis"""
        return """You are an expert Odoo migration specialist with deep knowledge of all Odoo versions from 13.0 to 18.0. 

You understand:
- API changes between Odoo versions
- Database schema evolution 
- OWL framework migration (v16+)
- OpenUpgrade migration scripts
- Common migration pitfalls and solutions
- Performance optimization strategies
- Security considerations

Provide precise, actionable analysis that helps ensure successful migrations.
Always respond with valid JSON in the requested format."""
    
    def _fallback_analysis(self, context: MigrationContext) -> AIAnalysisResult:
        """Provide fallback analysis when AI is not available"""
        # Determine risk level based on version gap
        source_major = int(context.source_version.split('.')[0])
        target_major = int(context.target_version.split('.')[0])
        version_gap = target_major - source_major
        
        if version_gap <= 1:
            risk_level = "low"
            confidence = 0.8
        elif version_gap <= 2:
            risk_level = "medium"
            confidence = 0.6
        else:
            risk_level = "high"
            confidence = 0.4
        
        return AIAnalysisResult(
            confidence_score=confidence,
            risk_level=risk_level,
            recommendations=[
                {"type": "manual_review", "description": "Manual code review recommended", "priority": "high"},
                {"type": "testing", "description": "Comprehensive testing required", "priority": "high"}
            ],
            suggested_fixes=[
                {"issue": "AI unavailable", "fix": "Enable OpenAI integration for detailed analysis"}
            ],
            migration_strategy="cautious",
            potential_issues=[
                {"issue": "Unknown compatibility issues", "impact": "medium", "mitigation": "Thorough testing"}
            ],
            analysis_summary=f"Fallback analysis: {version_gap}-version migration requires careful attention"
        )
    
    def _fallback_error_analysis(self, error_logs: List[str]) -> Dict[str, Any]:
        """Provide fallback error analysis when AI is not available"""
        return {
            "error_analysis": f"Detected {len(error_logs)} errors requiring manual analysis",
            "root_causes": ["AI analysis unavailable"],
            "fix_suggestions": [
                {"error": "Various migration errors", "fix": "Enable OpenAI integration for detailed error analysis", "priority": "high"}
            ]
        }

def main():
    """Test the AI Migration Assistant"""
    assistant = AIMigrationAssistant()
    
    # Test context
    context = MigrationContext(
        source_version="15.0",
        target_version="16.0",
        module_name="test_module",
        module_files=["__manifest__.py", "models/test_model.py"],
        detected_issues=[{"type": "api_deprecation", "file": "models/test_model.py"}],
        transformation_results={"python_transformations": 3, "xml_changes": 2}
    )
    
    # Test analysis
    result = assistant.analyze_migration_context(context)
    print(f"AI Analysis Result: {result.analysis_summary}")
    print(f"Risk Level: {result.risk_level}")
    print(f"Confidence: {result.confidence_score}")

if __name__ == "__main__":
    main()