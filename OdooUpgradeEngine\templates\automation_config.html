{% extends "base.html" %}
{% set active_page = "automation-config" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Automation Configuration</h2>
                    <p class="text-muted mb-0">Configure automation system settings and parameters</p>
                </div>
                <div>
                    <a href="{{ url_for('automation.automation_dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form id="automation-config-form" onsubmit="saveConfiguration(event)">
        <div class="row">
            <!-- General Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            General Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="automation_enabled" class="form-label">Enable Automation</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="automation_enabled" 
                                       {% if config.automation_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="automation_enabled">
                                    Automatic migration processing
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="max_concurrent_jobs" class="form-label">Max Concurrent Jobs</label>
                            <input type="number" class="form-control" id="max_concurrent_jobs" 
                                   value="{{ config.max_concurrent_jobs or 3 }}" min="1" max="10">
                            <div class="form-text">Maximum number of migrations to run simultaneously</div>
                        </div>

                        <div class="mb-3">
                            <label for="retry_attempts" class="form-label">Retry Attempts</label>
                            <input type="number" class="form-control" id="retry_attempts" 
                                   value="{{ config.retry_attempts or 2 }}" min="0" max="5">
                            <div class="form-text">Number of times to retry failed migrations</div>
                        </div>

                        <div class="mb-3">
                            <label for="timeout_minutes" class="form-label">Job Timeout (minutes)</label>
                            <input type="number" class="form-control" id="timeout_minutes" 
                                   value="{{ config.timeout_minutes or 30 }}" min="5" max="120">
                            <div class="form-text">Maximum time allowed for a single migration</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quality Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Quality Control
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="quality_threshold" class="form-label">Quality Threshold (%)</label>
                            <input type="range" class="form-range" id="quality_threshold" 
                                   value="{{ config.quality_threshold or 85 }}" min="50" max="100" 
                                   oninput="updateQualityDisplay(this.value)">
                            <div class="d-flex justify-content-between">
                                <small>50%</small>
                                <small id="quality_display">{{ config.quality_threshold or 85 }}%</small>
                                <small>100%</small>
                            </div>
                            <div class="form-text">Minimum quality score required for automatic approval</div>
                        </div>

                        <div class="mb-3">
                            <label for="auto_approve" class="form-label">Auto-approve High Quality</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto_approve" 
                                       {% if config.auto_approve %}checked{% endif %}>
                                <label class="form-check-label" for="auto_approve">
                                    Automatically approve migrations above threshold
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="require_manual_review" class="form-label">Manual Review Required</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="require_manual_review" 
                                       {% if config.require_manual_review %}checked{% endif %}>
                                <label class="form-check-label" for="require_manual_review">
                                    Require manual review for all migrations
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="backup_before_migration" class="form-label">Create Backups</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="backup_before_migration" 
                                       {% if config.backup_before_migration %}checked{% endif %}>
                                <label class="form-check-label" for="backup_before_migration">
                                    Create backup before starting migration
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            Notifications
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="email_notifications" class="form-label">Email Notifications</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_notifications" 
                                       {% if config.email_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="email_notifications">
                                    Send email notifications for job status
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notification_email" class="form-label">Notification Email</label>
                            <input type="email" class="form-control" id="notification_email" 
                                   value="{{ config.notification_email or '' }}" 
                                   placeholder="<EMAIL>">
                            <div class="form-text">Email address to receive notifications</div>
                        </div>

                        <div class="mb-3">
                            <label for="slack_webhook" class="form-label">Slack Webhook URL</label>
                            <input type="url" class="form-control" id="slack_webhook" 
                                   value="{{ config.slack_webhook or '' }}" 
                                   placeholder="https://hooks.slack.com/services/...">
                            <div class="form-text">Optional Slack integration for notifications</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Advanced Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="log_level" class="form-label">Log Level</label>
                            <select class="form-select" id="log_level">
                                <option value="DEBUG" {% if config.log_level == 'DEBUG' %}selected{% endif %}>DEBUG</option>
                                <option value="INFO" {% if config.log_level == 'INFO' or not config.log_level %}selected{% endif %}>INFO</option>
                                <option value="WARNING" {% if config.log_level == 'WARNING' %}selected{% endif %}>WARNING</option>
                                <option value="ERROR" {% if config.log_level == 'ERROR' %}selected{% endif %}>ERROR</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="cleanup_old_jobs" class="form-label">Cleanup Old Jobs</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="cleanup_old_jobs" 
                                       {% if config.cleanup_old_jobs %}checked{% endif %}>
                                <label class="form-check-label" for="cleanup_old_jobs">
                                    Automatically cleanup completed jobs older than 30 days
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="api_rate_limit" class="form-label">API Rate Limit (requests/minute)</label>
                            <input type="number" class="form-control" id="api_rate_limit" 
                                   value="{{ config.api_rate_limit or 60 }}" min="10" max="1000">
                            <div class="form-text">Rate limit for external API calls</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            Save Configuration
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg ms-3" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-2"></i>
                            Reset to Defaults
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
function updateQualityDisplay(value) {
    document.getElementById('quality_display').textContent = value + '%';
}

function saveConfiguration(event) {
    event.preventDefault();
    
    const config = {
        automation_enabled: document.getElementById('automation_enabled').checked,
        max_concurrent_jobs: parseInt(document.getElementById('max_concurrent_jobs').value),
        retry_attempts: parseInt(document.getElementById('retry_attempts').value),
        timeout_minutes: parseInt(document.getElementById('timeout_minutes').value),
        quality_threshold: parseInt(document.getElementById('quality_threshold').value),
        auto_approve: document.getElementById('auto_approve').checked,
        require_manual_review: document.getElementById('require_manual_review').checked,
        backup_before_migration: document.getElementById('backup_before_migration').checked,
        email_notifications: document.getElementById('email_notifications').checked,
        notification_email: document.getElementById('notification_email').value,
        slack_webhook: document.getElementById('slack_webhook').value,
        log_level: document.getElementById('log_level').value,
        cleanup_old_jobs: document.getElementById('cleanup_old_jobs').checked,
        api_rate_limit: parseInt(document.getElementById('api_rate_limit').value)
    };
    
    fetch('{{ url_for("automation.update_automation_config") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Configuration saved successfully!');
        } else {
            showAlert('error', 'Failed to save configuration: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Failed to save configuration');
    });
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        document.getElementById('automation_enabled').checked = true;
        document.getElementById('max_concurrent_jobs').value = 3;
        document.getElementById('retry_attempts').value = 2;
        document.getElementById('timeout_minutes').value = 30;
        document.getElementById('quality_threshold').value = 85;
        updateQualityDisplay(85);
        document.getElementById('auto_approve').checked = false;
        document.getElementById('require_manual_review').checked = false;
        document.getElementById('backup_before_migration').checked = true;
        document.getElementById('email_notifications').checked = false;
        document.getElementById('notification_email').value = '';
        document.getElementById('slack_webhook').value = '';
        document.getElementById('log_level').value = 'INFO';
        document.getElementById('cleanup_old_jobs').checked = true;
        document.getElementById('api_rate_limit').value = 60;
        
        showAlert('info', 'Settings reset to defaults');
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
