# pipeline_migration_orchestrator.py
"""
Pipeline Migration Orchestrator - Handles sequential version upgrades through multiple Odoo versions

This orchestrator manages progressive upgrades like v13→v14→v15→v16→v17→v18
by creating and managing multiple TrueMigrationOrchestrator jobs in sequence.
"""

from extensions import db
from models import MigrationJob, OdooModule
from true_migration_orchestrator import TrueMigrationOrchestrator
import logging
import os
import shutil
from typing import List, Dict, Any, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class PipelineMigrationOrchestrator:
    """
    Orchestrates sequential version upgrades through multiple Odoo versions.
    
    Uses TrueMigrationOrchestrator for each individual step in the pipeline.
    """
    
    def __init__(self, module_id: int, target_version: str, enable_pipeline: bool = True):
        """
        Initialize pipeline migration orchestrator.
        
        Args:
            module_id: ID of the module to migrate
            target_version: Final target version (e.g., '18.0')
            enable_pipeline: If True, use progressive upgrades; if False, direct upgrade
        """
        self.module = OdooModule.query.get(module_id)
        if not self.module:
            raise ValueError(f"Module with ID {module_id} not found.")
        
        self.target_version = target_version
        self.enable_pipeline = enable_pipeline
        self.logger = logging.getLogger(__name__)
        
        # Define version progression chain
        self.version_chain = ['13.0', '14.0', '15.0', '16.0', '17.0', '18.0']
        
        # Track pipeline jobs
        self.pipeline_jobs = []
        self.current_step = 0
        self.pipeline_status = 'INITIALIZED'
        
    def start_pipeline_migration(self) -> Dict[str, Any]:
        """
        Start the pipeline migration process.
        
        Returns:
            Dictionary with pipeline information and first job details
        """
        try:
            # Determine source version
            source_version = self._detect_source_version()
            
            # Plan migration path
            migration_path = self._plan_migration_path(source_version, self.target_version)
            
            if not self.enable_pipeline or len(migration_path) == 1:
                # Direct migration - use single TrueMigrationOrchestrator job
                return self._start_direct_migration(source_version, self.target_version)
            else:
                # Pipeline migration - create sequential jobs
                return self._start_pipeline_migration(migration_path)
                
        except Exception as e:
            self.logger.error(f"Failed to start pipeline migration: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'pipeline_enabled': self.enable_pipeline
            }
    
    def _detect_source_version(self) -> str:
        """
        Detect the source version of the module.
        
        Returns:
            Detected source version (e.g., '15.0')
        """
        # Use module's recorded version if available
        if self.module.version:
            version_parts = self.module.version.split('.')
            if len(version_parts) >= 2:
                major_minor = f"{version_parts[0]}.{version_parts[1]}"
                if major_minor in self.version_chain:
                    return major_minor
        
        # Fallback: analyze module files for version detection
        # This would use the same logic as the contributor upload system
        # For now, default to 15.0 as a safe assumption
        return '15.0'
    
    def _plan_migration_path(self, source_version: str, target_version: str) -> List[tuple]:
        """
        Plan the migration path from source to target version.
        
        Args:
            source_version: Starting version (e.g., '13.0')
            target_version: Target version (e.g., '18.0')
            
        Returns:
            List of (source, target) tuples for each migration step
        """
        try:
            source_idx = self.version_chain.index(source_version)
            target_idx = self.version_chain.index(target_version)
        except ValueError as e:
            raise ValueError(f"Invalid version in migration path: {str(e)}")
        
        if source_idx >= target_idx:
            raise ValueError(f"Source version {source_version} must be lower than target version {target_version}")
        
        # Create sequential migration steps
        migration_path = []
        for i in range(source_idx, target_idx):
            step_source = self.version_chain[i]
            step_target = self.version_chain[i + 1]
            migration_path.append((step_source, step_target))
        
        return migration_path
    
    def _start_direct_migration(self, source_version: str, target_version: str) -> Dict[str, Any]:
        """
        Start a direct migration using single TrueMigrationOrchestrator job.
        
        Args:
            source_version: Source version
            target_version: Target version
            
        Returns:
            Migration job information
        """
        # Create single migration job
        migration_job = MigrationJob(
            module_id=self.module.id,
            target_version=target_version,
            status='QUEUED'
        )
        db.session.add(migration_job)
        db.session.commit()
        
        self.logger.info(f"Created direct migration job {migration_job.id}: {source_version} → {target_version}")
        
        return {
            'success': True,
            'migration_type': 'direct',
            'job_id': migration_job.id,
            'source_version': source_version,
            'target_version': target_version,
            'pipeline_enabled': False
        }
    
    def _start_pipeline_migration(self, migration_path: List[tuple]) -> Dict[str, Any]:
        """
        Start a pipeline migration with sequential jobs.
        
        Args:
            migration_path: List of (source, target) version tuples
            
        Returns:
            Pipeline information and first job details
        """
        self.pipeline_status = 'STARTING'
        
        # Create all pipeline jobs
        for i, (step_source, step_target) in enumerate(migration_path):
            migration_job = MigrationJob(
                module_id=self.module.id,
                target_version=step_target,
                status='PIPELINE_QUEUED' if i > 0 else 'QUEUED'  # Only first job starts immediately
            )
            db.session.add(migration_job)
            db.session.flush()  # Get job ID
            
            self.pipeline_jobs.append({
                'job_id': migration_job.id,
                'step': i + 1,
                'source_version': step_source,
                'target_version': step_target,
                'status': migration_job.status
            })
        
        db.session.commit()
        
        self.pipeline_status = 'RUNNING'
        first_job = self.pipeline_jobs[0]
        
        self.logger.info(f"Created pipeline migration with {len(migration_path)} steps")
        self.logger.info(f"Starting first job {first_job['job_id']}: {first_job['source_version']} → {first_job['target_version']}")
        
        return {
            'success': True,
            'migration_type': 'pipeline',
            'pipeline_jobs': self.pipeline_jobs,
            'total_steps': len(migration_path),
            'current_step': 1,
            'first_job_id': first_job['job_id'],
            'migration_path': [f"{s} → {t}" for s, t in migration_path],
            'pipeline_enabled': True
        }
    
    def continue_pipeline_after_step(self, completed_job_id: int) -> Dict[str, Any]:
        """
        Continue pipeline migration after a step completes.
        
        Args:
            completed_job_id: ID of the job that just completed
            
        Returns:
            Information about next step or completion
        """
        try:
            # Find the completed job in our pipeline
            completed_step = None
            for i, job_info in enumerate(self.pipeline_jobs):
                if job_info['job_id'] == completed_job_id:
                    completed_step = i
                    break
            
            if completed_step is None:
                return {'success': False, 'error': 'Job not found in pipeline'}
            
            # Check if this was the last step
            if completed_step == len(self.pipeline_jobs) - 1:
                self.pipeline_status = 'COMPLETED'
                return {
                    'success': True,
                    'pipeline_completed': True,
                    'message': f'Pipeline migration completed successfully! Module upgraded to {self.target_version}'
                }
            
            # Start next step
            next_step = completed_step + 1
            next_job_info = self.pipeline_jobs[next_step]
            
            # Update next job status to start it
            next_job = MigrationJob.query.get(next_job_info['job_id'])
            if next_job:
                next_job.status = 'QUEUED'
                db.session.commit()
                
                self.current_step = next_step + 1
                
                self.logger.info(f"Starting pipeline step {self.current_step}: {next_job_info['source_version']} → {next_job_info['target_version']}")
                
                return {
                    'success': True,
                    'pipeline_completed': False,
                    'next_job_id': next_job_info['job_id'],
                    'current_step': self.current_step,
                    'total_steps': len(self.pipeline_jobs),
                    'next_migration': f"{next_job_info['source_version']} → {next_job_info['target_version']}"
                }
            else:
                return {'success': False, 'error': 'Next job not found'}
                
        except Exception as e:
            self.logger.error(f"Failed to continue pipeline: {str(e)}")
            return {'success': False, 'error': str(e)}

    @staticmethod
    def is_pipeline_job(job_id: int) -> bool:
        """
        Check if a job is part of a pipeline migration.
        
        Args:
            job_id: Migration job ID
            
        Returns:
            True if job is part of a pipeline
        """
        job = MigrationJob.query.get(job_id)
        if not job:
            return False
        
        # Check if there are other jobs for the same module with sequential target versions
        module_jobs = MigrationJob.query.filter_by(module_id=job.module_id).all()
        return len(module_jobs) > 1
    
    @staticmethod
    def get_pipeline_status(module_id: int) -> Dict[str, Any]:
        """
        Get the current status of a pipeline migration for a module.
        
        Args:
            module_id: Module ID
            
        Returns:
            Pipeline status information
        """
        jobs = MigrationJob.query.filter_by(module_id=module_id).order_by(MigrationJob.id).all()
        
        if len(jobs) <= 1:
            return {'is_pipeline': False}
        
        pipeline_info = {
            'is_pipeline': True,
            'total_steps': len(jobs),
            'completed_steps': len([j for j in jobs if j.status == 'COMPLETED']),
            'current_step': None,
            'overall_status': 'RUNNING'
        }
        
        # Find current step
        for i, job in enumerate(jobs):
            if job.status in ['QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION', 'VISUAL_DIFF', 'AWAITING_APPROVAL']:
                pipeline_info['current_step'] = i + 1
                break
        
        # Check if completed
        if pipeline_info['completed_steps'] == pipeline_info['total_steps']:
            pipeline_info['overall_status'] = 'COMPLETED'
        elif any(job.status == 'FAILED' for job in jobs):
            pipeline_info['overall_status'] = 'FAILED'
        
        return pipeline_info
