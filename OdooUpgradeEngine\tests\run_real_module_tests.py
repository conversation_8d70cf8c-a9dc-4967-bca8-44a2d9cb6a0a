#!/usr/bin/env python3
"""
Real Module Testing Runner

This script:
1. Starts the Flask application
2. Initializes the database
3. Runs comprehensive real module data tests
4. Generates detailed reports
5. Cleans up after testing
"""

import os
import sys
import time
import signal
import subprocess
import json
from datetime import datetime

class TestRunner:
    def __init__(self):
        self.flask_process = None
        self.test_port = 5003  # Use different port
        
    def setup_environment(self):
        """Setup test environment"""
        print("🔧 Setting up test environment...")

        # Setup test configuration
        try:
            from test_config import setup_test_environment
            setup_test_environment()
        except Exception as e:
            print(f"   ⚠️  Could not setup test config: {e}")

        # Initialize database
        try:
            from app import create_app, db
            app = create_app()
            with app.app_context():
                db.create_all()
            print("   ✅ Database initialized")
        except Exception as e:
            print(f"   ❌ Database initialization failed: {e}")
            return False

        return True
    
    def start_flask_app(self):
        """Start Flask application for testing"""
        print(f"🚀 Starting Flask application on port {self.test_port}...")
        
        try:
            # Start Flask app in background
            cmd = [
                sys.executable, "-c",
                f"from app import create_app; app = create_app(); app.run(host='0.0.0.0', port={self.test_port}, debug=False)"
            ]
            
            self.flask_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid if os.name != 'nt' else None
            )
            
            # Wait for startup
            time.sleep(5)
            
            # Check if process is still running
            if self.flask_process.poll() is None:
                print("   ✅ Flask application started successfully")
                return True
            else:
                stdout, stderr = self.flask_process.communicate()
                print(f"   ❌ Flask application failed to start")
                print(f"   Error: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"   ❌ Failed to start Flask application: {e}")
            return False
    
    def run_tests(self):
        """Run the real module data tests"""
        print("🧪 Running real module data tests...")
        
        try:
            # Run the test script
            result = subprocess.run(
                [sys.executable, "test_real_module_data.py"],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            print(result.stdout)
            
            if result.stderr:
                print("⚠️  Test warnings/errors:")
                print(result.stderr)
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ Tests timed out after 5 minutes")
            return False
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            return False
    
    def stop_flask_app(self):
        """Stop Flask application"""
        if self.flask_process:
            print("🛑 Stopping Flask application...")
            try:
                if os.name != 'nt':
                    # Unix-like systems
                    os.killpg(os.getpgid(self.flask_process.pid), signal.SIGTERM)
                else:
                    # Windows
                    self.flask_process.terminate()
                
                self.flask_process.wait(timeout=10)
                print("   ✅ Flask application stopped")
            except Exception as e:
                print(f"   ⚠️  Error stopping Flask application: {e}")
                try:
                    self.flask_process.kill()
                except:
                    pass
    
    def generate_summary_report(self):
        """Generate summary report from test results"""
        print("📊 Generating summary report...")
        
        try:
            # Load test report if it exists
            if os.path.exists('real_module_test_report.json'):
                with open('real_module_test_report.json', 'r') as f:
                    test_report = json.load(f)
                
                # Create summary
                summary = {
                    'test_execution': {
                        'timestamp': datetime.now().isoformat(),
                        'test_type': 'Real Module Data Validation',
                        'environment': 'Test Branch',
                        'port': self.test_port
                    },
                    'results': test_report.get('test_summary', {}),
                    'detailed_results': test_report.get('test_results', []),
                    'recommendations': self.generate_recommendations(test_report)
                }
                
                # Save summary
                with open('real_module_test_summary.json', 'w') as f:
                    json.dump(summary, f, indent=2)
                
                # Print summary
                self.print_summary(summary)
                
                return summary
            else:
                print("   ⚠️  No test report found")
                return None
                
        except Exception as e:
            print(f"   ❌ Failed to generate summary: {e}")
            return None
    
    def generate_recommendations(self, test_report):
        """Generate recommendations based on test results"""
        recommendations = []
        
        test_summary = test_report.get('test_summary', {})
        failed_tests = [r for r in test_report.get('test_results', []) if r['status'] == 'FAIL']
        
        if test_summary.get('failed', 0) == 0:
            recommendations.append({
                'type': 'SUCCESS',
                'message': 'All tests passed! The system is ready for production with real module data.',
                'action': 'APPROVE_MERGE'
            })
        else:
            recommendations.append({
                'type': 'WARNING',
                'message': f"{test_summary.get('failed', 0)} tests failed. Review required before merge.",
                'action': 'REVIEW_FAILURES'
            })
            
            # Specific recommendations based on failed tests
            for failed_test in failed_tests:
                if 'GitHub' in failed_test['test']:
                    recommendations.append({
                        'type': 'FIX',
                        'message': 'GitHub integration issues detected. Check API connectivity and authentication.',
                        'action': 'CHECK_GITHUB_CONFIG'
                    })
                elif 'AI' in failed_test['test']:
                    recommendations.append({
                        'type': 'FIX',
                        'message': 'AI analysis issues detected. Check AI provider configuration.',
                        'action': 'CHECK_AI_CONFIG'
                    })
        
        return recommendations
    
    def print_summary(self, summary):
        """Print formatted summary"""
        print("\n" + "=" * 60)
        print("🎯 REAL MODULE DATA TEST SUMMARY")
        print("=" * 60)
        
        results = summary.get('results', {})
        print(f"📊 Success Rate: {results.get('success_rate', 'N/A')}")
        print(f"✅ Passed: {results.get('passed', 0)}")
        print(f"❌ Failed: {results.get('failed', 0)}")
        print(f"⚠️  Skipped: {results.get('skipped', 0)}")
        print(f"⏱️  Duration: {results.get('duration_seconds', 0):.1f}s")
        
        print("\n📋 RECOMMENDATIONS:")
        for rec in summary.get('recommendations', []):
            emoji = "🎉" if rec['type'] == 'SUCCESS' else "⚠️" if rec['type'] == 'WARNING' else "🔧"
            print(f"   {emoji} {rec['message']}")
        
        print("\n📁 Generated Files:")
        print("   - real_module_test_report.json (detailed results)")
        print("   - real_module_test_summary.json (summary)")
    
    def cleanup(self):
        """Cleanup test environment"""
        print("🧹 Cleaning up...")
        self.stop_flask_app()

        # Cleanup test environment
        try:
            from test_config import cleanup_test_environment
            cleanup_test_environment()
        except Exception as e:
            print(f"   ⚠️  Could not cleanup test environment: {e}")

        print("   ✅ Cleanup complete")

def main():
    """Main test runner"""
    print("🧪 REAL MODULE DATA TEST RUNNER")
    print("=" * 50)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    runner = TestRunner()
    success = False
    
    try:
        # Setup and run tests
        if runner.setup_environment():
            if runner.start_flask_app():
                success = runner.run_tests()
                runner.generate_summary_report()
            else:
                print("❌ Failed to start Flask application")
        else:
            print("❌ Failed to setup test environment")
    
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        runner.cleanup()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 REAL MODULE DATA TESTING COMPLETED SUCCESSFULLY")
        print("✅ System validated with actual Odoo module data")
    else:
        print("⚠️  REAL MODULE DATA TESTING COMPLETED WITH ISSUES")
        print("🔍 Review the test reports for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
