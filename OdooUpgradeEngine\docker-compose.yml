version: '3.8'

services:
  # The PostgreSQL Database Service
  db:
    image: postgres:13-alpine
    container_name: oue_db
    environment:
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_DB=odoo_upgrade
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    restart: unless-stopped

  # The Redis Message Broker Service for Celery
  redis:
    image: redis:6-alpine
    container_name: oue_redis
    restart: unless-stopped

  # The Flask Web Application (UI)
  app:
    build: .
    container_name: oue_app
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=******************************/odoo_upgrade
      - REDIS_URL=redis://redis:6379/0
      - SESSION_SECRET=${SESSION_SECRET} # Reads from your .env file
      - GITHUB_TOKEN=${GITHUB_TOKEN}   # Reads from your .env file
    depends_on:
      - db
      - redis
    restart: unless-stopped

  # The Celery Worker (Background Tasks)
  worker:
    build: .
    container_name: oue_worker
    command: celery -A main.celery worker --loglevel=info
    environment:
      - DATABASE_URL=******************************/odoo_upgrade
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped

volumes:
  postgres_data: