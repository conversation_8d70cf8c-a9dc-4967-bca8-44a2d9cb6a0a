"""
Testing Integration Module

Integrates the advanced testing engine with the main application,
providing web routes and automation hooks for comprehensive module testing.
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, send_file
from werkzeug.utils import secure_filename

# Import our modules (with error handling for missing dependencies)
try:
    from module_testing_engine import ModuleTestingEngine, TestStatus, TestResult
    TESTING_ENGINE_AVAILABLE = True
except ImportError as e:
    TESTING_ENGINE_AVAILABLE = False
    # Create placeholder classes for type hints
    class TestResult:
        pass
    class TestStatus:
        pass
    class ModuleTestingEngine:
        pass
    logging.warning(f"Testing engine not available: {e}")

# Create blueprint
testing_bp = Blueprint('testing', __name__, url_prefix='/testing')

class TestingIntegration:
    """
    Integration class for testing functionality with the main application
    """

    def __init__(self, app=None):
        self.app = app
        self.testing_engine = None
        self.logger = logging.getLogger(__name__)

        if TESTING_ENGINE_AVAILABLE:
            try:
                self.testing_engine = ModuleTestingEngine()
                self.logger.info("Testing engine initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize testing engine: {e}")

        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize testing integration with Flask app"""
        self.app = app
        app.register_blueprint(testing_bp)

        # Add testing context to all templates
        @app.context_processor
        def inject_testing_status():
            return {
                'testing_available': TESTING_ENGINE_AVAILABLE and self.testing_engine is not None,
                'testing_config': self._get_testing_config()
            }

    def _get_testing_config(self) -> Dict[str, Any]:
        """Get current testing configuration"""
        if not self.testing_engine:
            return {"enabled": False}

        return {
            "enabled": True,
            "docker_available": self.testing_engine.docker_client is not None,
            "ai_available": self.testing_engine.ai_client is not None,
            "runbot_configured": self.testing_engine.config.get("runbot", {}).get("api_key") is not None
        }

    def test_module_async(self, module_path: str, versions: List[str] = None) -> str:
        """Start asynchronous module testing"""
        if not self.testing_engine:
            raise RuntimeError("Testing engine not available")

        # This would typically be run in a background task
        # For now, we'll simulate with a simple implementation
        test_id = f"test_{int(datetime.now().timestamp())}"

        try:
            # Start testing in background (simplified)
            results = self.testing_engine.test_module_comprehensive(module_path, versions)

            # Store results for later retrieval
            self._store_test_results(test_id, results)

            return test_id
        except Exception as e:
            self.logger.error(f"Async testing failed: {e}")
            raise

    def _store_test_results(self, test_id: str, results: Dict[str, TestResult]):
        """Store test results for later retrieval"""
        results_dir = Path("testing/results")
        results_dir.mkdir(parents=True, exist_ok=True)

        # Convert results to serializable format
        serializable_results = {}
        for key, result in results.items():
            serializable_results[key] = {
                "module_name": result.module_name,
                "test_id": result.test_id,
                "status": result.status.value,
                "odoo_version": result.odoo_version,
                "test_type": result.test_type,
                "start_time": result.start_time.isoformat(),
                "end_time": result.end_time.isoformat() if result.end_time else None,
                "logs": result.logs,
                "errors": result.errors,
                "warnings": result.warnings,
                "success_rate": result.success_rate,
                "install_time": result.install_time,
                "ai_analysis": result.ai_analysis,
                "suggested_fixes": result.suggested_fixes
            }

        result_file = results_dir / f"{test_id}.json"
        with open(result_file, 'w') as f:
            json.dump(serializable_results, f, indent=2)

    def get_test_results(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve test results by ID"""
        result_file = Path("testing/results") / f"{test_id}.json"

        if result_file.exists():
            with open(result_file, 'r') as f:
                return json.load(f)
        return None

# Initialize integration instance
testing_integration = TestingIntegration()

# Route handlers
@testing_bp.route('/')
def testing_dashboard():
    """Testing dashboard"""
    if not TESTING_ENGINE_AVAILABLE:
        flash("Testing engine is not available. Install required dependencies.", "warning")

    # Get recent test results
    recent_tests = []
    results_dir = Path("testing/results")

    if results_dir.exists():
        result_files = sorted(results_dir.glob("*.json"), key=lambda x: x.stat().st_mtime, reverse=True)

        for result_file in result_files[:10]:  # Last 10 tests
            try:
                with open(result_file, 'r') as f:
                    test_data = json.load(f)

                    # Calculate summary
                    summary = {
                        "test_id": result_file.stem,
                        "timestamp": datetime.fromtimestamp(result_file.stat().st_mtime),
                        "total_tests": len(test_data),
                        "passed": sum(1 for r in test_data.values() if r["status"] == "passed"),
                        "failed": sum(1 for r in test_data.values() if r["status"] == "failed"),
                        "success_rate": 0
                    }

                    if summary["total_tests"] > 0:
                        summary["success_rate"] = (summary["passed"] / summary["total_tests"]) * 100

                    recent_tests.append(summary)

            except Exception as e:
                logging.error(f"Error loading test result {result_file}: {e}")

    # Get testing configuration
    testing_config = {
        'docker_available': TESTING_ENGINE_AVAILABLE,
        'ai_analysis_enabled': os.environ.get('OPENAI_API_KEY') is not None,
        'runbot_enabled': False  # Placeholder for future Runbot integration
    }
    
    return render_template('testing/dashboard.html', 
                         recent_tests=recent_tests,
                         testing_available=TESTING_ENGINE_AVAILABLE,
                         testing_config=testing_config)

@testing_bp.route('/test-module', methods=['GET', 'POST'])
def test_module():
    """Test a specific module"""
    if request.method == 'POST':
        if not TESTING_ENGINE_AVAILABLE:
            flash("Testing engine not available", "error")
            return redirect(url_for('testing.testing_dashboard'))

        module_id = request.form.get('module_id')
        versions = request.form.getlist('versions')
        test_types = request.form.getlist('test_types')

        if not module_id:
            flash("Please select a module to test", "error")
            return redirect(url_for('testing.test_module'))

        try:
            # Get module path from database
            from models import UploadedModule
            from app import db

            module = db.session.get(UploadedModule, module_id)
            if not module:
                flash("Module not found", "error")
                return redirect(url_for('testing.test_module'))

            # Start testing
            test_id = testing_integration.test_module_async(
                module.file_path, 
                versions or ["17.0", "18.0"]
            )

            flash(f"Testing started. Test ID: {test_id}", "success")
            return redirect(url_for('testing.test_results', test_id=test_id))

        except Exception as e:
            flash(f"Testing failed: {str(e)}", "error")
            return redirect(url_for('testing.test_module'))

    # GET request - show test form
    from models import UploadedModule
    from app import db

    modules = db.session.query(UploadedModule).all()

    available_versions = ["13.0", "14.0", "15.0", "16.0", "17.0", "18.0"]
    test_types = ["docker", "runbot", "local"]

    return render_template('testing/test_module.html', 
                         modules=modules,
                         available_versions=available_versions,
                         test_types=test_types)

@testing_bp.route('/results/<test_id>')
def test_results(test_id):
    """View test results"""
    results = testing_integration.get_test_results(test_id)

    if not results:
        flash("Test results not found", "error")
        return redirect(url_for('testing.testing_dashboard'))

    return render_template('testing/results.html', 
                         test_id=test_id,
                         results=results)

@testing_bp.route('/api/test-status/<test_id>')
def api_test_status(test_id):
    """API endpoint for test status"""
    results = testing_integration.get_test_results(test_id)

    if not results:
        return jsonify({"error": "Test not found"}), 404

    # Calculate summary
    summary = {
        "test_id": test_id,
        "status": "completed",  # Simplified for now
        "total_tests": len(results),
        "passed": sum(1 for r in results.values() if r["status"] == "passed"),
        "failed": sum(1 for r in results.values() if r["status"] == "failed"),
        "errors": sum(1 for r in results.values() if r["status"] == "error"),
        "success_rate": 0
    }

    if summary["total_tests"] > 0:
        summary["success_rate"] = (summary["passed"] / summary["total_tests"]) * 100

    return jsonify(summary)

@testing_bp.route('/config', methods=['GET', 'POST'])
def testing_config():
    """Testing configuration"""
    config_path = Path("config/testing_config.json")

    if request.method == 'POST':
        try:
            config_data = request.get_json() or {}

            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)

            flash("Configuration updated successfully", "success")
            return jsonify({"status": "success"})

        except Exception as e:
            flash(f"Configuration update failed: {str(e)}", "error")
            return jsonify({"status": "error", "message": str(e)}), 400

    # GET request - show current config
    current_config = {}
    if config_path.exists():
        with open(config_path, 'r') as f:
            current_config = json.load(f)

    return render_template('testing/config.html', config=current_config)

@testing_bp.route('/download-report/<test_id>')
def download_test_report(test_id):
    """Download test report"""
    report_path = Path("testing/results") / f"{test_id}.json"

    if not report_path.exists():
        flash("Report not found", "error")
        return redirect(url_for('testing.testing_dashboard'))

    return send_file(report_path, as_attachment=True, 
                    download_name=f"test_report_{test_id}.json")

def init_testing_integration(app):
    """Initialize testing integration with Flask app"""
    try:
        # Initialize testing engine
        testing_engine = ModuleTestingEngine()

        # Store in app context
        app.testing_engine = testing_engine

        # Register testing blueprint
        app.register_blueprint(testing_bp)

        logging.getLogger(__name__).info("Testing engine initialized successfully")
        return True

    except Exception as e:
        logging.getLogger(__name__).error(f"Failed to initialize testing integration: {e}")
        # Create a minimal mock engine for graceful degradation
        app.testing_engine = None
        app.register_blueprint(testing_bp)
        logging.getLogger(__name__).warning("Testing integration running in fallback mode")
        return False