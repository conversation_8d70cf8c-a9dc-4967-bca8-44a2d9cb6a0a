"""
Semantic Analyzer Service - Week 4 Implementation
AI-Powered Post-Transformation Code Analysis

This module provides intelligent semantic analysis of transformed code,
building upon the existing AI migration assistant to provide deeper
code understanding and quality assessment.

Features:
- Deep semantic analysis of transformed code
- Code quality assessment and recommendations
- Business logic validation
- Integration quality checks
- Risk assessment and confidence scoring
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Import existing AI infrastructure
try:
    from ai_provider_manager import get_ai_provider_manager, analyze_with_ai
    from ai_migration_assistant import AIMigrationAssistant, MigrationContext, AIAnalysisResult
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SemanticAnalysisResult:
    """Result of semantic analysis"""
    overall_quality_score: float  # 0.0 to 1.0
    code_maintainability: float  # 0.0 to 1.0
    business_logic_integrity: float  # 0.0 to 1.0
    integration_quality: float  # 0.0 to 1.0
    semantic_issues: List[Dict[str, Any]]
    improvement_suggestions: List[Dict[str, Any]]
    confidence_level: str  # high, medium, low
    analysis_summary: str
    detailed_findings: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class CodeFragment:
    """Represents a code fragment for analysis"""
    file_path: str
    original_code: str
    transformed_code: str
    transformation_type: str
    line_range: Tuple[int, int]
    
class SemanticAnalyzer:
    """
    AI-powered semantic analyzer for post-transformation code quality assessment.
    
    This analyzer performs deep semantic analysis of transformed code to ensure:
    - Functional correctness is maintained
    - Code quality standards are met
    - Business logic integrity is preserved
    - Integration patterns remain valid
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ai_available = AI_AVAILABLE
        self.analysis_cache = {}
        
        if not self.ai_available:
            self.logger.warning("AI provider not available - semantic analysis will use fallback methods")
    
    def analyze_transformation_quality(self, 
                                     original_files: Dict[str, str],
                                     transformed_files: Dict[str, str],
                                     transformation_context: Dict[str, Any]) -> SemanticAnalysisResult:
        """
        Perform comprehensive semantic analysis of code transformations.
        
        Args:
            original_files: Dictionary of file paths to original content
            transformed_files: Dictionary of file paths to transformed content
            transformation_context: Context about the transformation performed
            
        Returns:
            Comprehensive semantic analysis result
        """
        try:
            # Identify code fragments that have been transformed
            code_fragments = self._identify_transformed_fragments(
                original_files, transformed_files
            )
            
            if not code_fragments:
                return self._create_default_result("No significant transformations detected")
            
            # Perform AI-powered semantic analysis
            if self.ai_available:
                analysis_result = self._perform_ai_semantic_analysis(
                    code_fragments, transformation_context
                )
            else:
                analysis_result = self._perform_fallback_analysis(
                    code_fragments, transformation_context
                )
            
            # Enhance with static analysis
            analysis_result = self._enhance_with_static_analysis(
                analysis_result, code_fragments
            )
            
            self.logger.info(f"Semantic analysis completed with quality score: {analysis_result.overall_quality_score:.2f}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Semantic analysis failed: {str(e)}")
            return self._create_error_result(str(e))
    
    def _identify_transformed_fragments(self, 
                                      original_files: Dict[str, str],
                                      transformed_files: Dict[str, str]) -> List[CodeFragment]:
        """Identify specific code fragments that have been transformed"""
        fragments = []
        
        for file_path in transformed_files:
            if file_path not in original_files:
                # New file created
                fragments.append(CodeFragment(
                    file_path=file_path,
                    original_code="",
                    transformed_code=transformed_files[file_path],
                    transformation_type="new_file",
                    line_range=(1, len(transformed_files[file_path].split('\n')))
                ))
                continue
            
            original = original_files[file_path]
            transformed = transformed_files[file_path]
            
            if original != transformed:
                # File has been modified
                fragments.append(CodeFragment(
                    file_path=file_path,
                    original_code=original,
                    transformed_code=transformed,
                    transformation_type="modified_file",
                    line_range=(1, len(transformed.split('\n')))
                ))
        
        return fragments
    
    def _perform_ai_semantic_analysis(self, 
                                    code_fragments: List[CodeFragment],
                                    context: Dict[str, Any]) -> SemanticAnalysisResult:
        """Perform AI-powered semantic analysis using the multi-provider system"""
        
        # Build comprehensive analysis prompt
        analysis_prompt = self._build_semantic_analysis_prompt(code_fragments, context)
        system_prompt = self._build_semantic_system_prompt()
        
        try:
            # Use the multi-provider AI system
            ai_response = analyze_with_ai(analysis_prompt, system_prompt)
            
            if ai_response.get('success'):
                return self._parse_ai_analysis_response(ai_response['content'])
            else:
                self.logger.warning("AI analysis failed, falling back to static analysis")
                return self._perform_fallback_analysis(code_fragments, context)
                
        except Exception as e:
            self.logger.error(f"AI semantic analysis error: {str(e)}")
            return self._perform_fallback_analysis(code_fragments, context)
    
    def _build_semantic_analysis_prompt(self, 
                                      code_fragments: List[CodeFragment],
                                      context: Dict[str, Any]) -> str:
        """Build comprehensive prompt for AI semantic analysis"""
        
        prompt = f"""
SEMANTIC CODE ANALYSIS REQUEST

Context:
- Source Odoo Version: {context.get('source_version', 'Unknown')}
- Target Odoo Version: {context.get('target_version', 'Unknown')}
- Module Name: {context.get('module_name', 'Unknown')}
- Transformation Type: {context.get('transformation_type', 'General')}

Please analyze the following code transformations for:
1. Functional correctness and equivalence
2. Code quality and maintainability
3. Business logic integrity
4. Odoo framework integration quality
5. Potential runtime issues or bugs

Code Transformations to Analyze:
"""
        
        for i, fragment in enumerate(code_fragments[:5]):  # Limit to 5 fragments for prompt size
            prompt += f"""

TRANSFORMATION {i+1}: {fragment.file_path}
Type: {fragment.transformation_type}

ORIGINAL CODE:
```
{fragment.original_code[:2000]}  # Truncate for prompt size
```

TRANSFORMED CODE:
```
{fragment.transformed_code[:2000]}  # Truncate for prompt size
```
"""
        
        prompt += """

ANALYSIS REQUIREMENTS:
Provide a JSON response with:
{
  "overall_quality_score": 0.85,
  "code_maintainability": 0.90,
  "business_logic_integrity": 0.95,
  "integration_quality": 0.80,
  "semantic_issues": [
    {
      "severity": "high|medium|low",
      "type": "functional|quality|integration|style",
      "description": "Issue description",
      "file": "file_path",
      "suggestion": "How to fix"
    }
  ],
  "improvement_suggestions": [
    {
      "priority": "high|medium|low",
      "category": "performance|security|maintainability|style",
      "description": "Suggestion description",
      "implementation": "How to implement"
    }
  ],
  "confidence_level": "high|medium|low",
  "analysis_summary": "Overall assessment summary",
  "detailed_findings": {
    "functional_correctness": "Assessment of functional equivalence",
    "code_quality": "Assessment of code quality improvements/regressions",
    "integration_concerns": "Any Odoo-specific integration issues",
    "recommendations": "Specific recommendations for improvement"
  }
}

Focus on:
- Are the transformations functionally equivalent?
- Do they maintain Odoo best practices?
- Are there any potential runtime issues?
- Is the code more maintainable after transformation?
"""
        
        return prompt
    
    def _build_semantic_system_prompt(self) -> str:
        """Build system prompt for semantic analysis"""
        return """You are a senior Odoo developer and code quality expert with 10+ years of experience. 

Your expertise includes:
- Deep knowledge of Odoo framework architecture (v13-v18)
- Best practices for Odoo module development
- Code quality assessment and improvement
- Migration patterns and anti-patterns
- Performance optimization
- Security considerations

Analyze the provided code transformations with the precision of a senior developer conducting a code review. Focus on functional correctness, code quality, and Odoo framework compliance.

Provide specific, actionable feedback that helps ensure the transformed code meets enterprise-quality standards."""
    
    def _parse_ai_analysis_response(self, ai_content: str) -> SemanticAnalysisResult:
        """Parse AI response into structured analysis result"""
        try:
            # Try to extract JSON from the response
            if '```json' in ai_content:
                json_start = ai_content.find('```json') + 7
                json_end = ai_content.find('```', json_start)
                json_content = ai_content[json_start:json_end].strip()
            elif '{' in ai_content and '}' in ai_content:
                json_start = ai_content.find('{')
                json_end = ai_content.rfind('}') + 1
                json_content = ai_content[json_start:json_end]
            else:
                raise ValueError("No JSON found in AI response")
            
            analysis_data = json.loads(json_content)
            
            return SemanticAnalysisResult(
                overall_quality_score=analysis_data.get('overall_quality_score', 0.7),
                code_maintainability=analysis_data.get('code_maintainability', 0.7),
                business_logic_integrity=analysis_data.get('business_logic_integrity', 0.8),
                integration_quality=analysis_data.get('integration_quality', 0.7),
                semantic_issues=analysis_data.get('semantic_issues', []),
                improvement_suggestions=analysis_data.get('improvement_suggestions', []),
                confidence_level=analysis_data.get('confidence_level', 'medium'),
                analysis_summary=analysis_data.get('analysis_summary', 'AI analysis completed'),
                detailed_findings=analysis_data.get('detailed_findings', {})
            )
            
        except Exception as e:
            self.logger.error(f"Failed to parse AI response: {str(e)}")
            return SemanticAnalysisResult(
                overall_quality_score=0.5,
                code_maintainability=0.5,
                business_logic_integrity=0.5,
                integration_quality=0.5,
                semantic_issues=[],
                improvement_suggestions=[],
                confidence_level='low',
                analysis_summary=f'AI response parsing failed: {str(e)}',
                detailed_findings={'error': str(e)}
            )
    
    def _perform_fallback_analysis(self, 
                                 code_fragments: List[CodeFragment],
                                 context: Dict[str, Any]) -> SemanticAnalysisResult:
        """Perform basic static analysis when AI is not available"""
        issues = []
        suggestions = []
        
        for fragment in code_fragments:
            # Basic syntax and pattern checks
            if fragment.transformed_code:
                # Check for common issues
                if 'TODO' in fragment.transformed_code:
                    issues.append({
                        'severity': 'medium',
                        'type': 'quality',
                        'description': 'TODO comments found in transformed code',
                        'file': fragment.file_path,
                        'suggestion': 'Complete TODO items before deployment'
                    })
                
                if 'FIXME' in fragment.transformed_code:
                    issues.append({
                        'severity': 'high',
                        'type': 'functional',
                        'description': 'FIXME comments indicate potential issues',
                        'file': fragment.file_path,
                        'suggestion': 'Resolve FIXME items immediately'
                    })
        
        return SemanticAnalysisResult(
            overall_quality_score=0.7,
            code_maintainability=0.7,
            business_logic_integrity=0.8,
            integration_quality=0.6,
            semantic_issues=issues,
            improvement_suggestions=suggestions,
            confidence_level='low',
            analysis_summary='Basic static analysis completed (AI not available)',
            detailed_findings={
                'analysis_method': 'static_fallback',
                'limitations': 'Limited analysis without AI semantic understanding'
            }
        )
    
    def _enhance_with_static_analysis(self, 
                                    result: SemanticAnalysisResult,
                                    fragments: List[CodeFragment]) -> SemanticAnalysisResult:
        """Enhance AI analysis with additional static analysis checks"""
        
        # Add static checks for Odoo-specific patterns
        for fragment in fragments:
            if fragment.file_path.endswith('.py'):
                # Check for Odoo API usage patterns
                if '@api.model' in fragment.transformed_code and 'self.env' not in fragment.transformed_code:
                    result.improvement_suggestions.append({
                        'priority': 'medium',
                        'category': 'integration',
                        'description': 'Consider using self.env for environment access in @api.model methods',
                        'implementation': 'Use self.env instead of direct environment access'
                    })
                
                # Check for potential security issues
                if 'sudo()' in fragment.transformed_code:
                    result.semantic_issues.append({
                        'severity': 'medium',
                        'type': 'security',
                        'description': 'sudo() usage detected - verify necessity',
                        'file': fragment.file_path,
                        'suggestion': 'Ensure sudo() is necessary and properly scoped'
                    })
        
        return result
    
    def _create_default_result(self, message: str) -> SemanticAnalysisResult:
        """Create a default analysis result"""
        return SemanticAnalysisResult(
            overall_quality_score=0.8,
            code_maintainability=0.8,
            business_logic_integrity=0.9,
            integration_quality=0.8,
            semantic_issues=[],
            improvement_suggestions=[],
            confidence_level='medium',
            analysis_summary=message,
            detailed_findings={}
        )
    
    def _create_error_result(self, error_message: str) -> SemanticAnalysisResult:
        """Create an error analysis result"""
        return SemanticAnalysisResult(
            overall_quality_score=0.0,
            code_maintainability=0.0,
            business_logic_integrity=0.0,
            integration_quality=0.0,
            semantic_issues=[{
                'severity': 'high',
                'type': 'functional',
                'description': f'Analysis failed: {error_message}',
                'file': 'unknown',
                'suggestion': 'Manual review required due to analysis failure'
            }],
            improvement_suggestions=[],
            confidence_level='low',
            analysis_summary=f'Semantic analysis failed: {error_message}',
            detailed_findings={'error': error_message}
        )

# Integration function for migration orchestrator
def analyze_transformation_semantics(original_files: Dict[str, str],
                                   transformed_files: Dict[str, str],
                                   context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main integration point for semantic analysis in migration workflow.
    
    This function provides a clean interface for the migration orchestrator
    to request semantic analysis of code transformations.
    """
    analyzer = SemanticAnalyzer()
    result = analyzer.analyze_transformation_quality(
        original_files, transformed_files, context
    )
    
    return result.to_dict()

def main():
    """Test the semantic analyzer"""
    # Example usage
    original_files = {
        'models/test.py': '''
class TestModel(models.Model):
    _name = 'test.model'
    
    @api.one
    def old_method(self):
        return self.name
'''
    }
    
    transformed_files = {
        'models/test.py': '''
class TestModel(models.Model):
    _name = 'test.model'
    
    def old_method(self):
        for record in self:
            return record.name
'''
    }
    
    context = {
        'source_version': '15.0',
        'target_version': '16.0',
        'module_name': 'test_module',
        'transformation_type': 'api_one_removal'
    }
    
    result = analyze_transformation_semantics(original_files, transformed_files, context)
    print("Semantic Analysis Result:")
    print(json.dumps(result, indent=2))

if __name__ == '__main__':
    main()