"""
Security Scanner - Mandatory Security Analysis

This module provides security scanning for uploaded Odoo modules,
using bandit and custom security rules to identify vulnerabilities.
"""

import subprocess
import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import tempfile
import shutil

class SecurityScanner:
    """
    Comprehensive security scanner for Odoo modules.
    
    This is a mandatory, non-skippable security check that must pass
    before any module enters the upgrade pipeline.
    """
    
    def __init__(self, module_path=None):
        self.logger = logging.getLogger(__name__)
        self.module_path = module_path
        self.critical_issues = []
        self.high_issues = []
        self.medium_issues = []
        self.low_issues = []

    def run_scan(self) -> List[str]:
        """
        Run security scan on the module path provided during initialization.
        Returns a list of security issues found.
        """
        if not self.module_path:
            return ["No module path provided for security scan"]

        try:
            results = self.scan_module(self.module_path)
            issues = []

            # Extract issues from results
            if not results.get('scan_passed', False):
                issues.extend(results.get('blocking_issues', []))

            # Add critical and high issues
            for issue in results.get('issues', []):
                if issue.get('severity') in ['CRITICAL', 'HIGH']:
                    issues.append(f"{issue.get('severity')}: {issue.get('description', 'Unknown issue')}")

            return issues
        except Exception as e:
            self.logger.error(f"Security scan failed: {e}")
            return [f"Security scan failed: {str(e)}"]
    
    def scan_module(self, module_path: str) -> Dict[str, Any]:
        """
        Perform comprehensive security scan on a module.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            Security scan results with blocking status
        """
        module_path = Path(module_path)
        
        results = {
            'module_name': module_path.name,
            'scan_passed': False,
            'blocking_issues': [],
            'bandit_results': {},
            'custom_security_issues': [],
            'total_issues': 0,
            'critical_count': 0,
            'high_count': 0,
            'medium_count': 0,
            'low_count': 0,
            'scan_summary': ''
        }
        
        try:
            # Run bandit security scanner
            bandit_results = self._run_bandit_scan(str(module_path))
            results['bandit_results'] = bandit_results
            
            # Run custom Odoo-specific security checks
            custom_results = self._run_custom_security_checks(str(module_path))
            results['custom_security_issues'] = custom_results
            
            # Analyze results and determine if module should be blocked
            results = self._analyze_security_results(results, bandit_results, custom_results)
            
            # Generate summary
            results['scan_summary'] = self._generate_scan_summary(results)
            
            self.logger.info(f"Security scan completed for {module_path.name}: "
                           f"{'PASSED' if results['scan_passed'] else 'BLOCKED'}")
            
        except Exception as e:
            self.logger.error(f"Security scan failed for {module_path}: {str(e)}")
            results['scan_passed'] = False
            results['blocking_issues'].append(f"Scan failed: {str(e)}")
        
        return results
    
    def _run_bandit_scan(self, module_path: str) -> Dict[str, Any]:
        """
        Run bandit security scanner on Python files.
        
        Args:
            module_path: Path to module directory
            
        Returns:
            Bandit scan results
        """
        try:
            # Create temporary file for bandit output
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as tmp_file:
                tmp_path = tmp_file.name
            
            # Run bandit with JSON output
            cmd = [
                'bandit', 
                '-r', module_path,
                '-f', 'json',
                '-o', tmp_path,
                '--skip', 'B101',  # Skip assert_used (common in tests)
            ]
            
            process = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True,
                timeout=60  # 1 minute timeout
            )
            
            # Read results
            with open(tmp_path, 'r') as f:
                bandit_output = json.load(f)
            
            # Clean up
            os.unlink(tmp_path)
            
            return {
                'success': True,
                'results': bandit_output,
                'exit_code': process.returncode
            }
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"Bandit scan timed out for {module_path}")
            return {'success': False, 'error': 'Scan timeout'}
        except FileNotFoundError:
            self.logger.error("Bandit not found - please install: pip install bandit")
            return {'success': False, 'error': 'Bandit not installed'}
        except Exception as e:
            self.logger.error(f"Bandit scan failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _run_custom_security_checks(self, module_path: str) -> List[Dict[str, Any]]:
        """
        Run custom Odoo-specific security checks.
        
        Args:
            module_path: Path to module directory
            
        Returns:
            List of custom security issues found
        """
        issues = []
        module_path = Path(module_path)
        
        # Check for dangerous file patterns
        issues.extend(self._check_dangerous_files(module_path))
        
        # Check for SQL injection patterns
        issues.extend(self._check_sql_injection_patterns(module_path))
        
        # Check for unsafe eval/exec usage
        issues.extend(self._check_unsafe_eval_patterns(module_path))
        
        # Check for hardcoded secrets
        issues.extend(self._check_hardcoded_secrets(module_path))
        
        # Check for unsafe file operations
        issues.extend(self._check_unsafe_file_operations(module_path))
        
        return issues
    
    def _check_dangerous_files(self, module_path: Path) -> List[Dict[str, Any]]:
        """Check for dangerous file types or names."""
        issues = []
        
        dangerous_patterns = [
            '*.exe', '*.bat', '*.cmd', '*.sh',
            '*.php', '*.jsp', '*.asp',
            '.htaccess', '.env'
        ]
        
        for pattern in dangerous_patterns:
            files = list(module_path.glob(f"**/{pattern}"))
            for file in files:
                issues.append({
                    'type': 'dangerous_file',
                    'severity': 'CRITICAL',
                    'file': str(file),
                    'message': f"Dangerous file type detected: {file.name}",
                    'recommendation': 'Remove executable and server configuration files'
                })
        
        return issues
    
    def _check_sql_injection_patterns(self, module_path: Path) -> List[Dict[str, Any]]:
        """Check for potential SQL injection vulnerabilities."""
        issues = []
        
        dangerous_patterns = [
            r'cr\.execute\s*\(\s*["\'].*%.*["\']',  # String formatting in SQL
            r'\.format\s*\(',                        # .format() in SQL context
            r'f["\'].*\{.*\}.*["\']',                # f-strings in SQL
        ]
        
        python_files = list(module_path.glob('**/*.py'))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for i, line in enumerate(content.split('\n'), 1):
                    for pattern in dangerous_patterns:
                        import re
                        if re.search(pattern, line):
                            issues.append({
                                'type': 'sql_injection',
                                'severity': 'HIGH',
                                'file': str(py_file),
                                'line': i,
                                'code': line.strip(),
                                'message': 'Potential SQL injection vulnerability',
                                'recommendation': 'Use parameterized queries or psycopg2.sql module'
                            })
            except Exception:
                continue  # Skip files that can't be read
        
        return issues
    
    def _check_unsafe_eval_patterns(self, module_path: Path) -> List[Dict[str, Any]]:
        """Check for unsafe eval/exec usage."""
        issues = []
        
        dangerous_functions = ['eval', 'exec', 'compile', '__import__']
        python_files = list(module_path.glob('**/*.py'))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for i, line in enumerate(content.split('\n'), 1):
                    for func in dangerous_functions:
                        if f'{func}(' in line and not line.strip().startswith('#'):
                            issues.append({
                                'type': 'unsafe_eval',
                                'severity': 'CRITICAL',
                                'file': str(py_file),
                                'line': i,
                                'code': line.strip(),
                                'message': f'Unsafe {func}() usage detected',
                                'recommendation': f'Avoid {func}() or use safe_eval from odoo.tools'
                            })
            except Exception:
                continue
        
        return issues
    
    def _check_hardcoded_secrets(self, module_path: Path) -> List[Dict[str, Any]]:
        """Check for hardcoded passwords, API keys, etc."""
        issues = []
        
        secret_patterns = [
            (r'password\s*=\s*["\'][^"\']{8,}["\']', 'hardcoded_password'),
            (r'api_key\s*=\s*["\'][^"\']{16,}["\']', 'hardcoded_api_key'),
            (r'secret\s*=\s*["\'][^"\']{16,}["\']', 'hardcoded_secret'),
            (r'token\s*=\s*["\'][^"\']{20,}["\']', 'hardcoded_token'),
        ]
        
        python_files = list(module_path.glob('**/*.py'))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for i, line in enumerate(content.split('\n'), 1):
                    for pattern, issue_type in secret_patterns:
                        import re
                        if re.search(pattern, line, re.IGNORECASE):
                            issues.append({
                                'type': issue_type,
                                'severity': 'HIGH',
                                'file': str(py_file),
                                'line': i,
                                'code': line.strip(),
                                'message': f'Potential {issue_type.replace("_", " ")} detected',
                                'recommendation': 'Use environment variables or Odoo config for secrets'
                            })
            except Exception:
                continue
        
        return issues
    
    def _check_unsafe_file_operations(self, module_path: Path) -> List[Dict[str, Any]]:
        """Check for unsafe file operations."""
        issues = []
        
        dangerous_patterns = [
            (r'open\s*\(\s*["\']\/.*["\']', 'absolute_path_access'),
            (r'os\.system\s*\(', 'os_system_call'),
            (r'subprocess\.call\s*\(', 'subprocess_call'),
            (r'\.\.\/.*', 'path_traversal'),
        ]
        
        python_files = list(module_path.glob('**/*.py'))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for i, line in enumerate(content.split('\n'), 1):
                    for pattern, issue_type in dangerous_patterns:
                        import re
                        if re.search(pattern, line):
                            issues.append({
                                'type': issue_type,
                                'severity': 'MEDIUM',
                                'file': str(py_file),
                                'line': i,
                                'code': line.strip(),
                                'message': f'Unsafe file operation: {issue_type.replace("_", " ")}',
                                'recommendation': 'Use secure file handling and validate paths'
                            })
            except Exception:
                continue
        
        return issues
    
    def _analyze_security_results(self, results: Dict[str, Any], 
                                 bandit_results: Dict[str, Any], 
                                 custom_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze security results and determine if module should be blocked.
        
        Args:
            results: Current results dict
            bandit_results: Bandit scan results
            custom_results: Custom security check results
            
        Returns:
            Updated results with blocking decision
        """
        blocking_issues = []
        
        # Process bandit results
        if bandit_results.get('success') and bandit_results.get('results'):
            bandit_data = bandit_results['results']
            for issue in bandit_data.get('results', []):
                severity = issue.get('issue_severity', 'LOW')
                
                if severity == 'HIGH':
                    results['high_count'] += 1
                    blocking_issues.append(f"Bandit HIGH: {issue.get('test_name', 'Unknown')}")
                elif severity == 'MEDIUM':
                    results['medium_count'] += 1
                elif severity == 'LOW':
                    results['low_count'] += 1
        
        # Process custom results
        for issue in custom_results:
            severity = issue.get('severity', 'LOW')
            
            if severity == 'CRITICAL':
                results['critical_count'] += 1
                blocking_issues.append(f"CRITICAL: {issue.get('message', 'Security issue')}")
            elif severity == 'HIGH':
                results['high_count'] += 1
                blocking_issues.append(f"HIGH: {issue.get('message', 'Security issue')}")
            elif severity == 'MEDIUM':
                results['medium_count'] += 1
            elif severity == 'LOW':
                results['low_count'] += 1
        
        # Calculate totals
        results['total_issues'] = (results['critical_count'] + results['high_count'] + 
                                 results['medium_count'] + results['low_count'])
        
        # Blocking criteria: ANY critical or more than 3 high severity issues
        results['blocking_issues'] = blocking_issues
        results['scan_passed'] = (results['critical_count'] == 0 and results['high_count'] <= 3)
        
        return results
    
    def _generate_scan_summary(self, results: Dict[str, Any]) -> str:
        """Generate human-readable scan summary."""
        if results['scan_passed']:
            return (f"✅ Security scan PASSED - "
                   f"{results['total_issues']} issues found "
                   f"(Critical: {results['critical_count']}, "
                   f"High: {results['high_count']}, "
                   f"Medium: {results['medium_count']}, "
                   f"Low: {results['low_count']})")
        else:
            return (f"🚫 Security scan BLOCKED - "
                   f"{len(results['blocking_issues'])} blocking issues found. "
                   f"Fix critical and high severity issues before proceeding.")

def main():
    """Test the security scanner"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python security_scanner.py <module_path>")
        sys.exit(1)
    
    module_path = sys.argv[1]
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create scanner and run
    scanner = SecurityScanner()
    results = scanner.scan_module(module_path)
    
    print(f"Security Scan Results for {results['module_name']}:")
    print(f"Status: {'PASSED' if results['scan_passed'] else 'BLOCKED'}")
    print(f"Total Issues: {results['total_issues']}")
    print(f"Critical: {results['critical_count']}")
    print(f"High: {results['high_count']}")
    print(f"Medium: {results['medium_count']}")
    print(f"Low: {results['low_count']}")
    print(f"\nSummary: {results['scan_summary']}")
    
    if results['blocking_issues']:
        print("\nBlocking Issues:")
        for issue in results['blocking_issues']:
            print(f"  - {issue}")

if __name__ == "__main__":
    main()