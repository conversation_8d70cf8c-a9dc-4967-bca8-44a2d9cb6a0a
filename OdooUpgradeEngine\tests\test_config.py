#!/usr/bin/env python3
"""
Test Configuration for Real Module Data Testing

This module provides configuration overrides for testing without requiring
external API keys or services.
"""

import os
import tempfile

def setup_test_environment():
    """Setup test environment variables"""

    # Use SQLite for testing (no PostgreSQL required)
    test_db_path = os.path.join(tempfile.gettempdir(), 'test_odoo_upgrade.db')
    os.environ['DATABASE_URL'] = f'sqlite:///{test_db_path}'

    # Disable AI providers for basic testing
    os.environ['DISABLE_AI_PROVIDERS'] = 'true'

    # Set test mode
    os.environ['TESTING'] = 'true'
    os.environ['FLASK_ENV'] = 'testing'

    # Disable external services
    os.environ['DISABLE_CELERY'] = 'true'
    os.environ['DISABLE_REDIS'] = 'true'

    # Initialize database with proper context
    try:
        from app import create_app, db
        app = create_app()
        with app.app_context():
            db.create_all()
        print("✅ Database initialized with proper Flask context")
    except Exception as e:
        print(f"⚠️  Database initialization warning: {e}")

    print("✅ Test environment configured")
    print(f"   Database: {test_db_path}")
    print("   AI Providers: Disabled")
    print("   External Services: Disabled")

def cleanup_test_environment():
    """Cleanup test environment"""
    
    # Remove test database
    test_db_path = os.path.join(tempfile.gettempdir(), 'test_odoo_upgrade.db')
    if os.path.exists(test_db_path):
        try:
            os.remove(test_db_path)
            print("✅ Test database cleaned up")
        except Exception as e:
            print(f"⚠️  Could not remove test database: {e}")

if __name__ == "__main__":
    setup_test_environment()
