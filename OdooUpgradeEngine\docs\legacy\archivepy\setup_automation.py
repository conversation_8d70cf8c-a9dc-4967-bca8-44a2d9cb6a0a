#!/usr/bin/env python3
"""
Setup Script for Odoo Module Automation System
===============================================

This script initializes the complete directory structure and creates example
module configurations for the automated upgrade system.
"""

import os
import json
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_directory_structure():
    """Create the required directory structure."""
    print("Creating directory structure...")
    
    directories = [
        "odoo_modules/v15_original",
        "odoo_modules/v16_original", 
        "odoo_modules/v17_original",
        "odoo_modules/v18_original",
        "odoo_modules/v16_upgraded",
        "odoo_modules/v17_upgraded",
        "odoo_modules/v18_upgraded",
        "odoo_modules/backups",
        "odoo_modules/automation_logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✓ Created: {directory}")
    
    # Create README files for each directory
    readme_contents = {
        "v15_original": "Original Odoo 15.0 modules - Place your source modules here for upgrading",
        "v16_original": "Original Odoo 16.0 modules - These can be upgraded to v17/v18", 
        "v17_original": "Original Odoo 17.0 modules - These can be upgraded to v18",
        "v18_original": "Original Odoo 18.0 modules - Latest version, no upgrades needed",
        "v16_upgraded": "Auto-upgraded modules for Odoo 16.0",
        "v17_upgraded": "Auto-upgraded modules for Odoo 17.0",
        "v18_upgraded": "Auto-upgraded modules for Odoo 18.0",
        "backups": "Backup copies of original modules before processing",
        "automation_logs": "Logs and reports from automation runs"
    }
    
    for folder, content in readme_contents.items():
        readme_path = Path(f"odoo_modules/{folder}/README.md")
        with open(readme_path, 'w') as f:
            f.write(f"# {folder.title().replace('_', ' ')}\n\n{content}\n")

def create_example_module(name: str, version: str, output_dir: str):
    """Create an example Odoo module for testing."""
    module_dir = Path(output_dir) / name
    module_dir.mkdir(exist_ok=True)
    
    # Create __manifest__.py
    manifest_content = f'''{{
    'name': '{name.title().replace("_", " ")}',
    'version': '{version}',
    'category': 'Tools',
    'summary': 'Example module for automation testing',
    'description': """
Example Odoo module created for testing the automated upgrade system.
This module demonstrates typical patterns that need upgrading between versions.
""",
    'author': 'Automation System',
    'website': 'https://github.com/your-org/odoo-modules',
    'depends': ['base', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'views/example_views.xml',
        'data/example_data.xml',
    ],
    'assets': {{
        'web.assets_backend': [
            'static/src/js/example_widget.js',
            'static/src/css/example_style.css',
        ],
    }},
    'installable': True,
    'application': False,
    'auto_install': False,
}}'''
    
    with open(module_dir / "__manifest__.py", 'w') as f:
        f.write(manifest_content)
    
    # Create models directory and files
    models_dir = module_dir / "models"
    models_dir.mkdir(exist_ok=True)
    
    with open(models_dir / "__init__.py", 'w') as f:
        f.write("from . import example_model\n")
    
    # Create example model with version-specific patterns
    model_content = '''from odoo import models, fields, api
from odoo.exceptions import UserError

class ExampleModel(models.Model):
    _name = 'example.model'
    _description = 'Example Model'
    
    name = fields.Char('Name', required=True)
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)
    
    # This will need upgrading in newer versions
    @api.one
    def legacy_method(self):
        return self.name
    
    @api.multi
    def another_legacy_method(self):
        return self.mapped('name')
    
    # Deprecated sudo usage that needs fixing
    def check_permissions(self):
        if not self.sudo().user_has_groups('base.group_user'):
            raise UserError('Access denied')
'''
    
    with open(models_dir / "example_model.py", 'w') as f:
        f.write(model_content)
    
    # Create views directory and XML files
    views_dir = module_dir / "views"
    views_dir.mkdir(exist_ok=True)
    
    view_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="example_model_form" model="ir.ui.view">
            <field name="name">example.model.form</field>
            <field name="model">example.model</field>
            <field name="arch" type="xml">
                <form string="Example Model">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="description"/>
                            <field name="active"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <record id="example_model_tree" model="ir.ui.view">
            <field name="name">example.model.tree</field>
            <field name="model">example.model</field>
            <field name="arch" type="xml">
                <tree string="Example Models">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>'''
    
    with open(views_dir / "example_views.xml", 'w') as f:
        f.write(view_content)
    
    # Create static assets
    static_dir = module_dir / "static" / "src"
    static_dir.mkdir(parents=True, exist_ok=True)
    
    js_dir = static_dir / "js"
    css_dir = static_dir / "css"
    js_dir.mkdir(exist_ok=True)
    css_dir.mkdir(exist_ok=True)
    
    # Legacy JavaScript that needs Owl 2 conversion
    js_content = '''odoo.define('example_module.ExampleWidget', function (require) {
"use strict";

var Widget = require('web.Widget');
var core = require('web.core');

var ExampleWidget = Widget.extend({
    template: 'ExampleTemplate',
    
    init: function() {
        this._super.apply(this, arguments);
        this.data = {};
    },
    
    start: function() {
        var self = this;
        return this._super().then(function() {
            self._bindEvents();
        });
    },
    
    _bindEvents: function() {
        this.$('.example-button').on('click', this._onButtonClick.bind(this));
    },
    
    _onButtonClick: function() {
        console.log('Button clicked');
    }
});

return ExampleWidget;
});'''
    
    with open(js_dir / "example_widget.js", 'w') as f:
        f.write(js_content)
    
    css_content = '''.example-widget {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.example-button {
    background-color: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.example-button:hover {
    background-color: #005a87;
}'''
    
    with open(css_dir / "example_style.css", 'w') as f:
        f.write(css_content)
    
    # Create security directory
    security_dir = module_dir / "security"
    security_dir.mkdir(exist_ok=True)
    
    security_content = '''id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_example_model,access_example_model,model_example_model,base.group_user,1,1,1,1'''
    
    with open(security_dir / "ir.model.access.csv", 'w') as f:
        f.write(security_content)
    
    # Create data directory
    data_dir = module_dir / "data"
    data_dir.mkdir(exist_ok=True)
    
    data_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="example_record_1" model="example.model">
            <field name="name">Example Record 1</field>
            <field name="description">This is an example record</field>
        </record>
    </data>
</odoo>'''
    
    with open(data_dir / "example_data.xml", 'w') as f:
        f.write(data_content)
    
    return module_dir

def create_example_modules():
    """Create example modules for testing the automation system."""
    print("Creating example modules...")
    
    # Create modules for different versions
    modules_config = [
        ("example_sales_v15", "********.0", "odoo_modules/v15_original"),
        ("example_inventory_v15", "********.0", "odoo_modules/v15_original"),
        ("example_hr_v16", "********.0", "odoo_modules/v16_original"),
        ("example_accounting_v17", "********.0", "odoo_modules/v17_original"),
    ]
    
    for module_name, version, output_dir in modules_config:
        print(f"  Creating {module_name}...")
        
        # Create module directory structure
        temp_dir = Path("temp_modules")
        temp_dir.mkdir(exist_ok=True)
        
        module_dir = create_example_module(module_name, version, temp_dir)
        
        # Create ZIP file
        zip_path = Path(output_dir) / f"{module_name}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(module_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(temp_dir)
                    zipf.write(file_path, arc_name)
        
        print(f"    ✓ Created: {zip_path}")
        
        # Clean up temp directory
        shutil.rmtree(module_dir)
    
    # Clean up temp directory
    if Path("temp_modules").exists():
        shutil.rmtree("temp_modules")

def create_requirements_file():
    """Create requirements.txt file for the automation system."""
    requirements = [
        "flask>=2.0.0",
        "flask-sqlalchemy>=3.0.0",
        "sqlalchemy>=1.4.0",
        "psycopg2-binary>=2.9.0",
        "werkzeug>=2.0.0",
        "email-validator>=1.1.0",
        "gunicorn>=20.1.0",
    ]
    
    with open("requirements.txt", 'w') as f:
        f.write('\n'.join(requirements) + '\n')
    
    print("✓ Created requirements.txt")

def create_documentation():
    """Create comprehensive documentation for the automation system."""
    readme_content = '''# Odoo Module Automation System

## Overview

This system automatically upgrades Odoo modules across versions (15.0 → 16.0 → 17.0 → 18.0) using advanced analysis, automatic fixing, and Owl 2 framework conversion.

## Directory Structure

```
odoo_modules/
├── v15_original/     # Original Odoo 15.0 modules (source)
├── v16_original/     # Original Odoo 16.0 modules
├── v17_original/     # Original Odoo 17.0 modules  
├── v18_original/     # Original Odoo 18.0 modules
├── v16_upgraded/     # Auto-upgraded to 16.0
├── v17_upgraded/     # Auto-upgraded to 17.0
├── v18_upgraded/     # Auto-upgraded to 18.0
├── backups/          # Original module backups
└── automation_logs/  # Processing logs and reports
```

## Usage

### Manual Operation

```bash
# Single automation cycle
python automation_runner.py --mode single

# Continuous automation (every hour)
python automation_runner.py --mode continuous --interval 3600

# Dry run (no changes made)
python automation_runner.py --dry-run

# Status report only
python automation_runner.py --status-only
```

### GitHub Actions

The system includes GitHub Actions workflow for automated processing:

- **Scheduled**: Runs daily at 2 AM UTC
- **Manual**: Trigger via GitHub Actions interface
- **Push-triggered**: Runs when modules are added to original folders

### Configuration

Edit `automation_config.json` to customize:

- GitHub repository settings
- Processing batch sizes and thresholds
- Directory paths
- Quality requirements
- Notification settings

## Workflow

1. **Discovery**: Scans original version folders for modules
2. **Analysis**: Analyzes compatibility for target version
3. **Auto-Fix**: Applies automated fixes for common issues
4. **Advanced Upgrade**: Performs comprehensive version migration
5. **Quality Check**: Validates compatibility score meets threshold
6. **Packaging**: Creates upgraded module packages
7. **Backup**: Preserves original modules
8. **Commit**: Updates repository with new versions

## Version Progression

The system follows this upgrade chain:

```
v15 → v16 → v17 → v18
```

Modules are automatically detected and upgraded through each version step.

## Quality Assurance

- Compatibility scoring (0-100%)
- Configurable quality thresholds
- Backup system for all originals
- Detailed logging and reporting
- Rollback capabilities

## Getting Started

1. Run setup: `python setup_automation.py`
2. Configure GitHub repository in `automation_config.json`
3. Place original modules in appropriate version folders
4. Run automation: `python automation_runner.py`

## Support

Check automation logs in `odoo_modules/automation_logs/` for detailed processing information and error reports.
'''
    
    with open("README_AUTOMATION.md", 'w') as f:
        f.write(readme_content)
    
    print("✓ Created README_AUTOMATION.md")

def main():
    """Main setup function."""
    print("🚀 Setting up Odoo Module Automation System")
    print("=" * 50)
    
    # Create directory structure
    create_directory_structure()
    print()
    
    # Create example modules
    create_example_modules()
    print()
    
    # Create requirements file
    create_requirements_file()
    print()
    
    # Create documentation
    create_documentation()
    print()
    
    print("✅ Setup completed successfully!")
    print()
    print("Next steps:")
    print("1. Configure your GitHub repository in automation_config.json")
    print("2. Place your original modules in the appropriate version folders")
    print("3. Run: python automation_runner.py --status-only")
    print("4. Start automation: python automation_runner.py")
    print()
    print("Directory structure created in: ./odoo_modules/")
    print("Example modules created for testing")
    print("Documentation available in: README_AUTOMATION.md")

if __name__ == "__main__":
    main()