#!/usr/bin/env python3
"""
Test GitHub Integration - End-to-End Testing

This script tests the GitHub integration functionality:
1. Authentication status check
2. Repository listing
3. Module pulling simulation
4. Sync back simulation
"""

import os
import sys
import requests
import json

# Test configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_REPO_URL = "https://github.com/OCA/server-tools"

def test_auth_status():
    """Test GitHub authentication status endpoint"""
    print("🔐 Testing GitHub Authentication Status...")
    
    response = requests.get(f"{BASE_URL}/api/github/auth-status")
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            auth_status = "✅ Authenticated" if data['authenticated'] else "❌ Not Authenticated"
            print(f"   Status: {auth_status}")
            print(f"   Message: {data['message']}")
            return data['authenticated']
        else:
            print(f"   ❌ Error: {data.get('error', 'Unknown error')}")
            return False
    else:
        print(f"   ❌ HTTP Error: {response.status_code}")
        return False

def test_repositories():
    """Test repository listing endpoint"""
    print("\n📁 Testing Repository Listing...")
    
    response = requests.get(f"{BASE_URL}/api/github/repositories")
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            repos = data['repositories']
            print(f"   ✅ Found {len(repos)} repositories")
            for repo in repos[:3]:  # Show first 3
                print(f"      - {repo['name']}: {repo['description']}")
            return True
        else:
            print(f"   ❌ Error: {data.get('error', 'Unknown error')}")
            return False
    else:
        print(f"   ❌ HTTP Error: {response.status_code}")
        return False

def test_pull_modules():
    """Test module pulling endpoint"""
    print(f"\n📥 Testing Module Pulling from {TEST_REPO_URL}...")
    
    payload = {
        "repository_url": TEST_REPO_URL
    }
    
    response = requests.post(
        f"{BASE_URL}/api/github/pull-modules",
        json=payload,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            modules = data['modules']
            print(f"   ✅ Successfully pulled {len(modules)} modules")
            for module in modules[:3]:  # Show first 3
                print(f"      - {module['name']} (v{module['original_version']})")
            print(f"   Next Step: {data['next_step']}")
            return True
        else:
            print(f"   ❌ Error: {data.get('error', 'Unknown error')}")
            return False
    else:
        print(f"   ❌ HTTP Error: {response.status_code}")
        return False

def test_sync_modules():
    """Test module sync back endpoint"""
    print("\n📤 Testing Module Sync Back...")
    
    # First get migration jobs to find some to sync
    response = requests.get(f"{BASE_URL}/api/migration-jobs")
    
    if response.status_code != 200:
        print("   ⚠️  No migration jobs available for sync test")
        return False
    
    jobs_data = response.json()
    if not jobs_data.get('success') or not jobs_data.get('jobs'):
        print("   ⚠️  No migration jobs found for sync test")
        return False
    
    # Find completed jobs
    completed_jobs = [job for job in jobs_data['jobs'] if job.get('status') == 'COMPLETED']
    
    if not completed_jobs:
        print("   ⚠️  No completed migrations found for sync test")
        return False
    
    # Test sync with first completed job
    module_ids = [completed_jobs[0]['id']]
    
    payload = {
        "module_ids": module_ids,
        "target_branch": "upgraded-modules"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/github/sync-upgraded",
        json=payload,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            modules = data['modules']
            print(f"   ✅ Successfully synced {len(modules)} modules")
            for module in modules:
                print(f"      - {module['name']}: {module['original_version']} → {module['upgraded_version']}")
            print(f"   GitHub Path: {data['github_path']}")
            return True
        else:
            print(f"   ❌ Error: {data.get('error', 'Unknown error')}")
            return False
    else:
        print(f"   ❌ HTTP Error: {response.status_code}")
        return False

def main():
    """Run all GitHub integration tests"""
    print("🚀 GitHub Integration End-to-End Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print("❌ Flask server not running at http://127.0.0.1:5000")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask server at http://127.0.0.1:5000")
        print("   Please start the server with: python main.py")
        return False
    
    print("✅ Flask server is running")
    
    # Run tests
    results = []
    
    results.append(("Authentication Status", test_auth_status()))
    results.append(("Repository Listing", test_repositories()))
    results.append(("Module Pulling", test_pull_modules()))
    results.append(("Module Sync Back", test_sync_modules()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! GitHub integration is fully operational.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
