{
    'name': 'Advanced Inventory Management',
    'version': '********.0',
    'category': 'Inventory/Inventory',
    'summary': 'Enhanced inventory management with multi-location support',
    'description': """
Advanced Inventory Management
=============================

This module extends Odoo's inventory functionality with:
* Multi-location stock tracking
* Advanced reporting
* Custom picking strategies
* Inventory alerts and notifications
* Integration with barcode scanning

Compatible with Odoo 18.0
    """,
    'author': 'Sample Developer',
    'website': 'https://github.com/sample/inventory-module',
    'depends': ['stock', 'sale', 'purchase'],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_location_views.xml',
        'views/stock_picking_views.xml',
        'data/stock_data.xml',
        'reports/inventory_report.xml',
    ],
    'demo': [
        'demo/stock_demo.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}