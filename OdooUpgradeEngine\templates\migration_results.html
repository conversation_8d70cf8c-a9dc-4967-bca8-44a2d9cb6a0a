{% extends "base.html" %}
{% set title = "Migration Results" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-chart-line me-3"></i>
            Migration Results
        </h1>
        <p class="lead">Detailed migration results with comparisons and analysis</p>
    </div>
</div>

<!-- Results Summary Cards -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                <h5 class="card-title">Completed Migrations</h5>
                <h3 class="text-success">{{ completed_jobs|length }}</h3>
                <p class="text-muted">Successfully completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                <h5 class="card-title">Failed Migrations</h5>
                <h3 class="text-danger">{{ failed_jobs|length }}</h3>
                <p class="text-muted">Require attention</p>
            </div>
        </div>
    </div>
</div>

<!-- Completed Migrations -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-check-double me-2"></i>
            Completed Migrations
        </h5>
    </div>
    <div class="card-body">
        {% if completed_jobs %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Module</th>
                            <th>Version Upgrade</th>
                            <th>Completed</th>
                            <th>Duration</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in completed_jobs[:10] %}
                        <tr>
                            <td>
                                <strong>{{ job.module.name }}</strong>
                                <br>
                                <small class="text-muted">{{ job.module.description or 'No description' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="badge bg-success">{{ job.target_version }}</span>
                            </td>
                            <td>
                                <small class="text-muted">{{ job.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">~2h 30m</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewMigrationDetails({{ job.id }})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-outline-success" onclick="downloadResult({{ job.id }})">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                    <button class="btn btn-outline-info" onclick="rerunWithAI({{ job.id }})">
                                        <i class="fas fa-robot"></i> AI Rerun
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No completed migrations yet</h5>
                <p class="text-muted">Completed migrations will appear here</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Failed Migrations -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Failed Migrations
        </h5>
    </div>
    <div class="card-body">
        {% if failed_jobs %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Module</th>
                            <th>Version Upgrade</th>
                            <th>Failed</th>
                            <th>Error</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for job in failed_jobs[:10] %}
                        <tr>
                            <td>
                                <strong>{{ job.module.name }}</strong>
                                <br>
                                <small class="text-muted">{{ job.module.description or 'No description' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ job.module.version or 'Unknown' }}</span>
                                <i class="fas fa-arrow-right mx-2"></i>
                                <span class="badge bg-danger">{{ job.target_version }}</span>
                            </td>
                            <td>
                                <small class="text-muted">{{ job.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                            </td>
                            <td>
                                <small class="text-danger">{{ (job.log or 'Unknown error')[:50] }}...</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-danger" onclick="viewErrorDetails({{ job.id }})">
                                        <i class="fas fa-bug"></i> View Error
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="aiAnalyzeFailure({{ job.id }})">
                                        <i class="fas fa-brain"></i> AI Analyze
                                    </button>
                                    <button class="btn btn-outline-success" onclick="retryMigration({{ job.id }})">
                                        <i class="fas fa-redo"></i> Retry
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-success">No failed migrations</h5>
                <p class="text-muted">All migrations completed successfully</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
function viewMigrationDetails(jobId) {
    window.location.href = `/migration/${jobId}/review`;
}

function downloadResult(jobId) {
    window.location.href = `/migration/${jobId}/download`;
}

function viewErrorDetails(jobId) {
    window.location.href = `/migration/${jobId}/errors`;
}

function retryMigration(jobId) {
    if (confirm('Retry this failed migration?')) {
        fetch(`/migration/${jobId}/retry`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'}
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Migration retry started successfully!');
                location.reload();
            } else {
                alert('Failed to retry migration: ' + data.error);
            }
        });
    }
}

// AI functions (will be implemented in ai-integration.js)
function rerunWithAI(jobId) {
    alert('AI Rerun functionality will be implemented in Phase 1');
}

function aiAnalyzeFailure(jobId) {
    alert('AI Failure Analysis functionality will be implemented in Phase 1');
}
</script>
{% endblock %}
