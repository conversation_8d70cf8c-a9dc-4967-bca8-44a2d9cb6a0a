# tasks.py
from extensions import celery
from true_migration_orchestrator import TrueMigrationOrchestrator
from app import create_app

@celery.task(name='tasks.start_migration')
def start_migration_task(job_id: int):
    """Celery background task that activates the first phase of the orchestrator."""
    app = create_app()
    with app.app_context():
        orchestrator = TrueMigrationOrchestrator(job_id=job_id)
        orchestrator.run_initial_phase()

@celery.task(name='tasks.continue_migration')
def continue_migration_task(job_id: int):
    """Celery background task that activates the second phase of the orchestrator."""
    app = create_app()
    with app.app_context():
        orchestrator = TrueMigrationOrchestrator(job_id=job_id)
        orchestrator.continue_after_approval()