{% extends "base.html" %}
{% block title %}Module Details: {{ module.name }}{% endblock %}
{% block content %}
<style>
.recommended-version {
    background-color: #d4edda !important;
    color: #155724 !important;
    font-weight: bold;
}
.version-info {
    background-color: #e7f3ff;
    border-left: 4px solid #007bff;
    padding: 8px 12px;
    margin-top: 8px;
    border-radius: 4px;
}
</style>
<div class="container mt-4">
    <div class="card mb-4">
        <div class="card-header bg-primary text-white"><h2 class="h4 mb-0">Module Details: <strong>{{ module.name }}</strong></h2></div>
        <div class="card-body">
            <p class="card-text"><strong>Current Manifest Version:</strong> <span class="badge bg-secondary">{{ module.version }}</span></p>
            <p class="card-text"><strong>File Path:</strong> <code>{{ module.path }}</code></p>
            <p class="card-text"><strong>Last Updated:</strong> {{ module.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-header bg-success text-white"><h3 class="h5 mb-0">Start New Migration</h3></div>
        <div class="card-body">
            {% if available_target_versions %}
                <form action="{{ url_for('main.create_migration_job') }}" method="POST">
                    <input type="hidden" name="module_id" value="{{ module.id }}">
                    <div class="row align-items-end">
                        <div class="col-md-8">
                            <label for="target_version" class="form-label">Target Odoo Version</label>
                            <select class="form-select" id="target_version" name="target_version" required>
                                <option value="" disabled selected>Select a version...</option>
                                {% for version in available_target_versions %}
                                    <option value="{{ version.value }}" {% if version.recommended %}class="recommended-version"{% endif %}>
                                        {{ version.name }}{% if version.recommended %} (Recommended){% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                            {% if current_version %}
                                <div class="version-info">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Current module version: <strong>{{ current_version }}</strong>
                                    - Only higher versions are shown to prevent downgrades
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-success w-100"><i class="fas fa-rocket me-2"></i>Launch Unified Migration</button>
                        </div>
                    </div>
                </form>
            {% else %}
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>No Migration Available</strong><br>
                    This module is already at the latest supported version
                    {% if current_version %}({{ current_version }}){% endif %}
                    or no higher versions are available for migration.
                </div>
            {% endif %}
        </div>
    </div>
    <div class="card">
        <div class="card-header bg-dark text-white"><h3 class="h5 mb-0">Migration History & Status</h3></div>
        <div class="card-body">
            {% if migration_jobs %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark"><tr><th>Job ID</th><th>Target</th><th>Status</th><th>Queued At</th><th>Actions</th></tr></thead>
                        <tbody>
                            {% for job in migration_jobs %}
                            <tr>
                                <td>{{ job.id }}</td>
                                <td>{{ job.target_version }}</td>
                                <td>
                                    <span class="badge 
                                        {% if job.status in ['COMPLETED', 'SUCCESS'] %}bg-success
                                        {% elif job.status in ['FAILED'] %}bg-danger
                                        {% elif job.status in ['QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION', 'VISUAL_DIFF', 'DB_MIGRATION', 'TESTING'] %}bg-info
                                        {% elif job.status in ['AWAITING_APPROVAL'] %}bg-warning
                                        {% elif job.status in ['DIFF_APPROVED'] %}bg-primary
                                        {% else %}bg-secondary
                                        {% endif %}">{{ job.status }}</span>
                                </td>
                                <td>{{ job.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td>
                                    {% if job.status == 'AWAITING_APPROVAL' %}
                                        <a href="{{ url_for('main.view_diff', job_id=job.id) }}" class="btn btn-sm btn-warning">Review Changes</a>
                                    {% elif job.status in ['COMPLETED', 'FAILED'] %}
                                        <a href="#" class="btn btn-sm btn-info">View Log</a>
                                    {% else %}
                                        <span class="text-muted">In Progress...</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-center text-muted">No migration jobs have been run for this module yet.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}