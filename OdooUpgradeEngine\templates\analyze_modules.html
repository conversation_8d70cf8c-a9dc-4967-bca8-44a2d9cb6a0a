{% extends "base.html" %}
{% set title = "Analyze Modules" %}

<!-- FORCE REFRESH: 2025-07-06-06:15:00 - UPDATED TEMPLATE -->

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-search me-3"></i>
            Module Analysis
        </h1>
        <p class="lead">Review uploaded modules and their compatibility analysis</p>
    </div>
</div>

<!-- Action Bar -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">
                            <i class="fas fa-cubes me-2"></i>
                            {{ modules|length }} Modules Uploaded
                        </h6>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ url_for('main.upload_modules') }}" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>Upload More
                        </a>
                        <a href="{{ url_for('main.analyze_all') }}" class="btn btn-success">
                            <i class="fas fa-cogs me-1"></i>Analyze All Pending
                        </a>

                        <!-- AI Integration Buttons -->
                        <div class="btn-group" role="group" aria-label="AI Actions">
                            <button class="btn btn-info" onclick="bulkAIAnalysis()" title="Run AI analysis on all modules">
                                <i class="fas fa-robot me-1"></i>AI Analyze All
                            </button>
                            <button class="btn btn-outline-info" onclick="getAIRecommendations()" title="Get AI recommendations for all modules">
                                <i class="fas fa-lightbulb me-1"></i>AI Recommendations
                            </button>
                        </div>

                        <button class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modules List -->
{% if modules %}
<!-- Workflow Guidance for All Modules -->
<div class="row mb-4">
    <div class="col">
        <div class="alert alert-info" role="alert">
            <h6 class="alert-heading"><i class="fas fa-robot me-1"></i>TrueMigrationOrchestrator Workflow</h6>
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-primary me-2">1</span>
                        <strong>Start Migration</strong> - Creates job in TrueMigrationOrchestrator
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-info me-2">2</span>
                        <strong>Automated Processing</strong> - Security scan, AST upgrade, XML transformation
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success me-2">3</span>
                        <strong>Review & Approve</strong> - Visual diff review and final approval
                    </div>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>Unified System:</strong> All migrations are processed through TrueMigrationOrchestrator for consistency and reliability.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>Upload Date</th>
                                <th>File Size</th>
                                <th>Analysis Status</th>
                                <th>Compatibility</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in modules_with_analysis %}
                            {% set module = item.module %}
                            {% set latest_job = item.latest_job %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-cube me-2 text-primary"></i>
                                        <div>
                                            <strong>{{ module.name }}</strong>
                                            <small class="text-muted d-block">{{ module.path.split('/')[-1] if module.path else 'Module file' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small>{{ module.timestamp.strftime('%Y-%m-%d %H:%M') if module.timestamp else 'Unknown' }}</small>
                                </td>
                                <td>
                                    {% if item.file_size %}
                                        <small>{{ (item.file_size / 1024 / 1024) | round(2) }} MB</small>
                                    {% else %}
                                        <small class="text-muted">Unknown</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.has_analysis %}
                                        <span class="badge bg-{{ 'success' if item.analysis_status == 'COMPLETED' else 'warning' if item.analysis_status in ['QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION'] else 'info' if item.analysis_status in ['VISUAL_DIFF', 'AWAITING_APPROVAL'] else 'danger' }}">
                                            {% if item.analysis_status in ['ANALYSIS', 'CODE_TRANSFORMATION'] %}
                                                <i class="fas fa-spinner fa-spin me-1"></i>
                                            {% endif %}
                                            {{ item.analysis_status.replace('_', ' ').title() }}
                                            {% if item.target_version %}
                                                → v{{ item.target_version }}
                                            {% endif %}
                                        </span>
                                        {% if item.last_analyzed %}
                                            <br><small class="text-muted">{{ item.last_analyzed.strftime('%Y-%m-%d %H:%M') }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-secondary">Not Analyzed</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.has_analysis and item.latest_job %}
                                        {% if item.analysis_status == 'COMPLETED' %}
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar bg-success" style="width: 95%"></div>
                                                </div>
                                                <small>95%</small>
                                            </div>
                                            <div class="mt-1">
                                                <small class="text-muted">
                                                    <span class="badge badge-sm bg-secondary">{{ item.module.version or 'Current' }}</span>
                                                    <i class="fas fa-arrow-right mx-1" style="font-size: 0.7em;"></i>
                                                    <span class="badge badge-sm bg-success">{{ item.target_version }}</span>
                                                </small>
                                            </div>
                                        {% elif item.analysis_status in ['QUEUED', 'ANALYSIS', 'CODE_TRANSFORMATION'] %}
                                            <div class="d-flex align-items-center">
                                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                <small>{{ item.analysis_status|title }}...</small>
                                            </div>
                                        {% elif item.analysis_status == 'FAILED' %}
                                            <span class="text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                Failed
                                            </span>
                                        {% else %}
                                            <span class="text-info">
                                                <i class="fas fa-clock me-1"></i>
                                                {{ item.analysis_status|title }}
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Not analyzed</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('main.module_details', module_id=module.id) }}"
                                           class="btn btn-outline-primary" title="View Module Details">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        {% if not item.has_analysis %}
                                            <!-- Start TrueMigrationOrchestrator -->
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}"
                                               class="btn btn-success" title="Start Migration via TrueMigrationOrchestrator">
                                                <i class="fas fa-robot me-1"></i>Start Migration
                                            </a>
                                        {% elif item.analysis_status == 'FAILED' %}
                                            <!-- Retry via TrueMigrationOrchestrator -->
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}"
                                               class="btn btn-outline-warning" title="Retry Migration via TrueMigrationOrchestrator">
                                                <i class="fas fa-redo"></i>
                                            </a>
                                        {% elif item.analysis_status == 'COMPLETED' %}
                                            <!-- New Migration via TrueMigrationOrchestrator -->
                                            <a href="{{ url_for('main.orchestrate_migration_form', module_id=module.id) }}"
                                               class="btn btn-outline-success" title="Start New Migration via TrueMigrationOrchestrator">
                                                <i class="fas fa-plus me-1"></i>New Migration
                                            </a>

                                        {% endif %}
                                        
                                        {% if item.analysis_status == 'COMPLETED' and item.latest_job %}
                                            <!-- Show additional actions for completed migrations -->
                                            {% if item.latest_job.status == 'COMPLETED' %}
                                                <div class="btn-group" role="group">
                                                    <!-- View Analysis button -->
                                                    <a href="/api/migration-analysis/{{ item.latest_job.id }}"
                                                       class="btn btn-outline-primary btn-sm" title="View Analysis Report" target="_blank">
                                                        <i class="fas fa-chart-bar"></i>
                                                    </a>
                                                    <!-- View Diff button -->
                                                    <a href="#" onclick="openDiffReport({{ item.latest_job.id }})"
                                                       class="btn btn-outline-info btn-sm" title="View Visual Diff">
                                                        <i class="fas fa-code-branch"></i>
                                                    </a>
                                                    <!-- View Full Log button -->
                                                    <a href="/api/migration-log/{{ item.latest_job.id }}"
                                                       class="btn btn-outline-secondary btn-sm" title="View Full Migration Log" target="_blank">
                                                        <i class="fas fa-file-alt"></i>
                                                    </a>
                                                </div>
                                            {% endif %}
                                        {% endif %}

                                        <!-- AI Integration Buttons -->
                                        <div class="btn-group btn-group-sm ms-1" role="group" aria-label="AI Actions">
                                            {% if item.analysis_status == 'COMPLETED' and item.latest_job and item.latest_job.status == 'COMPLETED' %}
                                                <!-- AI buttons for completed migrations -->
                                                <button class="btn btn-info btn-sm" onclick="aiImproveMigration({{ item.latest_job.id }})"
                                                        title="Use AI to improve this migration">
                                                    <i class="fas fa-robot"></i>
                                                </button>
                                                <button class="btn btn-outline-info btn-sm" onclick="viewAIRecommendations({{ item.latest_job.id }})"
                                                        title="View AI recommendations">
                                                    <i class="fas fa-lightbulb"></i>
                                                </button>
                                            {% elif item.analysis_status == 'FAILED' %}
                                                <!-- AI buttons for failed analyses -->
                                                <button class="btn btn-warning btn-sm" onclick="aiAnalyzeFailure({{ item.id }})"
                                                        title="Use AI to analyze failure">
                                                    <i class="fas fa-brain"></i>
                                                </button>
                                            {% else %}
                                                <!-- AI pre-analysis for pending modules -->
                                                <button class="btn btn-outline-info btn-sm" onclick="aiPreAnalyze({{ item.id }})"
                                                        title="Get AI pre-analysis">
                                                    <i class="fas fa-search-plus"></i>
                                                </button>
                                            {% endif %}
                                        </div>

                                        <!-- Delete Module Button -->
                                        <button class="btn btn-outline-danger btn-sm ms-1"
                                                onclick="confirmDeleteModule({{ item.id }}, '{{ item.name }}')"
                                                title="Delete Module">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h4>No Modules Uploaded</h4>
                <p class="text-muted mb-4">Start by uploading some Odoo module files for analysis.</p>
                <a href="{{ url_for('main.upload_modules') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>Upload Modules
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Legend -->
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Status Legend</h6>
                <div class="d-flex flex-wrap gap-3">
                    <div>
                        <span class="badge bg-success me-1">Completed</span>
                        Analysis finished successfully
                    </div>
                    <div>
                        <span class="badge bg-info me-1">Analyzing</span>
                        Currently being analyzed
                    </div>
                    <div>
                        <span class="badge bg-warning me-1">Pending</span>
                        Waiting for analysis
                    </div>
                    <div>
                        <span class="badge bg-danger me-1">Error</span>
                        Analysis failed
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Auto-refresh is now handled by main.js initializeAutoRefresh() -->
<script>
function confirmDeleteModule(moduleId, moduleName) {
    if (confirm(`Delete module "${moduleName}"? This action cannot be undone.`)) {
        fetch(`/api/delete-module/${moduleId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Module "${moduleName}" deleted successfully`);
                location.reload(); // Refresh the page
            } else {
                alert('Error deleting module: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error deleting module: ' + error.message);
        });
    }
}

function openDiffReport(jobId) {
    // Get the diff URL from the API and open it directly
    fetch(`/api/visual-diff/${jobId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.diff_url) {
                // Open the actual HTML diff file directly
                window.open(data.diff_url, '_blank');
            } else {
                alert('Error: ' + (data.error || 'Could not load diff report'));
            }
        })
        .catch(error => {
            alert('Error loading diff report: ' + error.message);
        });
}

// ===== AI INTEGRATION FUNCTIONS =====

// Bulk AI functions
function bulkAIAnalysis() {
    if (confirm('Run AI analysis on all modules? This may take several minutes.')) {
        fetch('/api/bulk-ai-analysis-modules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`AI analysis started for ${data.count} modules`);
                location.reload();
            } else {
                alert('Failed to start AI analysis: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

function getAIRecommendations() {
    fetch('/api/ai-recommendations-all')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIRecommendationsModal(data.recommendations);
        } else {
            alert('No AI recommendations available: ' + data.error);
        }
    })
    .catch(error => alert('Error: ' + error.message));
}

// Individual module AI functions
function aiImproveMigration(jobId) {
    if (confirm('Use AI to improve this migration?')) {
        fetch(`/api/ai-improve-migration/${jobId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('AI improvement analysis started!');
                location.reload();
            } else {
                alert('Failed to start AI improvement: ' + data.error);
            }
        })
        .catch(error => alert('Error: ' + error.message));
    }
}

function viewAIRecommendations(jobId) {
    fetch(`/api/ai-recommendations/${jobId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIRecommendationsModal(data.recommendations);
        } else {
            alert('No AI recommendations available: ' + data.error);
        }
    })
    .catch(error => alert('Error: ' + error.message));
}

function aiAnalyzeFailure(moduleId) {
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`/api/ai-analyze-module-failure/${moduleId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        button.innerHTML = originalContent;
        button.disabled = false;

        if (data.success) {
            showAIAnalysisModal(data.analysis);
        } else {
            alert('Failed to analyze failure: ' + data.error);
        }
    })
    .catch(error => {
        button.innerHTML = originalContent;
        button.disabled = false;
        alert('Error: ' + error.message);
    });
}

function aiPreAnalyze(moduleId) {
    fetch(`/api/ai-pre-analyze/${moduleId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAIPreAnalysisModal(data.analysis);
        } else {
            alert('AI pre-analysis not available: ' + data.error);
        }
    })
    .catch(error => alert('Error: ' + error.message));
}

// Modal display functions
function showAIRecommendationsModal(recommendations) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        AI Recommendations
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-robot me-2"></i>
                        AI recommendations for improving your migrations:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${recommendations}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="implementRecommendations()">Implement</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function showAIAnalysisModal(analysis) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-brain text-info me-2"></i>
                        AI Failure Analysis
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-robot me-2"></i>
                        AI has analyzed the failure and provided insights:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${analysis}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="applyAIFixes()">Apply Fixes</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

function showAIPreAnalysisModal(analysis) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-search-plus text-success me-2"></i>
                        AI Pre-Analysis
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-robot me-2"></i>
                        AI pre-analysis of migration complexity:
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-wrap">${analysis}</pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" onclick="startOptimizedAnalysis()">Start Optimized Analysis</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    new bootstrap.Modal(modal).show();
    modal.addEventListener('hidden.bs.modal', () => modal.remove());
}

// Placeholder functions for future implementation
function implementRecommendations() {
    alert('Implement recommendations functionality will be added in backend');
}

function applyAIFixes() {
    alert('Apply AI fixes functionality will be added in backend');
}

function startOptimizedAnalysis() {
    alert('Start optimized analysis functionality will be added in backend');
}
</script>
{% endblock %}
