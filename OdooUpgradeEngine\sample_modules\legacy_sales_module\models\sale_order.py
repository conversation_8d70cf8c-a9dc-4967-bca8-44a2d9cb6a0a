# Legacy Odoo module with old API patterns - demonstrates compatibility issues
from openerp import models, fields, api
from openerp.tools import _
from openerp.tools.translate import _
import openerp.addons.decimal_precision as dp


class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    # Old field definitions
    legacy_field = fields.Char('Legacy Field', size=64)  # size parameter deprecated
    discount_amount = fields.Float('Discount Amount', digits=dp.get_precision('Account'))
    
    # Old API decorators - deprecated in Odoo 12+
    @api.one
    def _compute_legacy_total(self):
        self.legacy_total = sum(line.price_subtotal for line in self.order_line)
    
    legacy_total = fields.Float('Legacy Total', compute='_compute_legacy_total')
    
    @api.multi
    def action_legacy_confirm(self):
        for order in self:
            order.write({'state': 'sale'})
        return True
    
    @api.one
    @api.depends('order_line.price_total')
    def _amount_all(self):
        # Old style computation
        amount_untaxed = amount_tax = 0.0
        for line in self.order_line:
            amount_untaxed += line.price_subtotal
            amount_tax += line.price_tax
        self.update({
            'amount_untaxed': self.pricelist_id.currency_id.round(amount_untaxed),
            'amount_tax': self.pricelist_id.currency_id.round(amount_tax),
            'amount_total': amount_untaxed + amount_tax,
        })
    
    # Using old sudo() pattern
    def legacy_method(self):
        admin_user = self.sudo()
        return admin_user.name
    
    # Old context usage
    def with_legacy_context(self):
        return self.with_context(legacy_mode=True)


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    # Old field definition with deprecated attributes
    legacy_notes = fields.Text('Legacy Notes', translate=True, size=256)
    
    @api.multi
    def _prepare_invoice_line(self, qty):
        # Old method override pattern
        res = super(SaleOrderLine, self)._prepare_invoice_line(qty)
        res.update({
            'legacy_notes': self.legacy_notes,
        })
        return res