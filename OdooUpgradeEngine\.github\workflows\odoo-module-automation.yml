name: Odoo Module Automation

on:
  schedule:
    # Run every day at 2 AM UTC
    - cron: '0 2 * * *'
  
  push:
    paths:
      - 'v*_original/**'
      - 'automation_config.json'
      - 'automation_system.py'
  
  workflow_dispatch:
    inputs:
      batch_size:
        description: 'Number of modules to process in batch'
        required: false
        default: '5'
      target_version:
        description: 'Specific target version (optional)'
        required: false
        default: ''
      dry_run:
        description: 'Run without making changes'
        required: false
        default: 'false'

jobs:
  module-automation:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y git python3-pip
    
    - name: Install Python dependencies
      run: |
        pip install -r requirements.txt
    
    - name: Configure Git
      run: |
        git config --global user.name "Odoo Module Automation"
        git config --global user.email "<EMAIL>"
    
    - name: Create directory structure
      run: |
        mkdir -p odoo_modules/{v15_original,v16_original,v17_original,v18_original}
        mkdir -p odoo_modules/{v16_upgraded,v17_upgraded,v18_upgraded}
        mkdir -p odoo_modules/{backups,automation_logs}
    
    - name: Run automation system
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        BATCH_SIZE: ${{ github.event.inputs.batch_size || '5' }}
        TARGET_VERSION: ${{ github.event.inputs.target_version || '' }}
        DRY_RUN: ${{ github.event.inputs.dry_run || 'false' }}
      run: |
        python automation_runner.py
    
    - name: Generate status report
      run: |
        python -c "
from automation_system import OdooModuleAutomationSystem
import json
automation = OdooModuleAutomationSystem()
report = automation.generate_status_report()
with open('automation_report.json', 'w') as f:
    json.dump(report, f, indent=2)
"
    
    - name: Upload automation report
      uses: actions/upload-artifact@v3
      with:
        name: automation-report
        path: automation_report.json
        retention-days: 30
    
    - name: Upload logs
      uses: actions/upload-artifact@v3
      with:
        name: automation-logs
        path: odoo_modules/automation_logs/
        retention-days: 30
    
    - name: Commit and push changes
      if: env.DRY_RUN != 'true'
      run: |
        git add .
        if ! git diff --cached --quiet; then
          git commit -m "Automated module upgrades - $(date '+%Y-%m-%d %H:%M:%S')"
          git push origin main
        else
          echo "No changes to commit"
        fi
    
    - name: Create release on major batch completion
      if: env.DRY_RUN != 'true'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: automation-${{ github.run_number }}
        release_name: Automated Module Upgrades ${{ github.run_number }}
        body: |
          Automated module upgrades completed successfully.
          
          Check the automation report for details:
          - Processed modules count
          - Success/failure rates
          - Compatibility scores
          
          This release includes upgraded modules for Odoo versions 16.0, 17.0, and 18.0.
        draft: false
        prerelease: false
      continue-on-error: true

  notify-completion:
    needs: module-automation
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify completion
      uses: 8398a7/action-slack@v3
      if: env.SLACK_WEBHOOK_URL != ''
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      with:
        status: ${{ needs.module-automation.result }}
        text: |
          Odoo Module Automation completed with status: ${{ needs.module-automation.result }}
          
          Check the GitHub Actions log for details.