/**
 * Real-Time Updates JavaScript
 * Provides live updates for the Odoo Upgrade Engine dashboard
 * 
 * Features:
 * - WebSocket integration for live updates
 * - Progress bars for active migrations
 * - Notification system for completed tasks
 * - Auto-refresh for dashboard components
 */

// ===== CONFIGURATION =====
const REFRESH_INTERVALS = {
    dashboard: 30000,    // 30 seconds
    migrations: 15000,   // 15 seconds
    attention: 60000     // 60 seconds
};

let refreshTimers = {};
let isPageVisible = true;

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeRealTimeUpdates();
    setupVisibilityHandling();
    setupNotificationSystem();
});

/**
 * Initialize real-time updates system
 */
function initializeRealTimeUpdates() {
    // Start auto-refresh timers based on current page
    const currentPage = getCurrentPage();
    
    if (currentPage === 'dashboard') {
        startDashboardUpdates();
    } else if (currentPage === 'migrations') {
        startMigrationUpdates();
    }
    
    // Setup manual refresh buttons
    setupRefreshButtons();
}

/**
 * Get current page identifier
 */
function getCurrentPage() {
    const path = window.location.pathname;
    if (path === '/' || path === '/index') return 'dashboard';
    if (path.includes('migration')) return 'migrations';
    if (path.includes('jobs')) return 'migrations';
    return 'other';
}

// ===== DASHBOARD UPDATES =====

/**
 * Start dashboard auto-updates
 */
function startDashboardUpdates() {
    // Initial update
    refreshDashboard();
    
    // Set up periodic updates
    refreshTimers.dashboard = setInterval(() => {
        if (isPageVisible) {
            refreshDashboard();
        }
    }, REFRESH_INTERVALS.dashboard);
}

/**
 * Refresh dashboard data
 */
async function refreshDashboard() {
    try {
        showLoadingIndicator('dashboard');
        
        // Fetch updated dashboard data
        const response = await fetch('/api/dashboard-data');
        if (!response.ok) throw new Error('Failed to fetch dashboard data');
        
        const data = await response.json();
        
        // Update dashboard components
        updateActivemigrations(data.active_migrations);
        updateAttentionItems(data.items_needing_attention);
        updateStatistics(data.statistics);
        updateRecommendedPath(data.recommended_path);
        
        hideLoadingIndicator('dashboard');
        
    } catch (error) {
        console.error('Error refreshing dashboard:', error);
        hideLoadingIndicator('dashboard');
        showNotification('Failed to refresh dashboard data', 'error');
    }
}

/**
 * Update active migrations widget
 */
function updateActivemigrations(migrations) {
    const container = document.getElementById('active-migrations-container');
    const countBadge = document.getElementById('active-count');
    
    if (!container || !countBadge) return;
    
    countBadge.textContent = migrations.length;
    
    if (migrations.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <p class="text-muted">No active migrations</p>
                <a href="/upload_modules" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>Start New Migration
                </a>
            </div>
        `;
        return;
    }
    
    let html = '<div class="list-group list-group-flush">';
    
    migrations.slice(0, 5).forEach(job => {
        const statusClass = getStatusBadgeClass(job.status);
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${job.module_name}</h6>
                    <p class="mb-1 text-muted small">${job.description || 'No description'}</p>
                    <small class="text-muted">
                        Status: <span class="badge bg-${statusClass}">${job.status}</span>
                        ${job.target_version ? `| Target: v${job.target_version}` : ''}
                    </small>
                    ${job.progress ? `
                        <div class="progress mt-1" style="height: 4px;">
                            <div class="progress-bar" role="progressbar" style="width: ${job.progress}%"></div>
                        </div>
                    ` : ''}
                </div>
                <div class="btn-group btn-group-sm">
                    <a href="/migration_jobs#job-${job.id}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i>
                    </a>
                    ${job.status === 'FAILED' || job.status === 'ERROR' ? `
                        <button class="btn btn-outline-warning btn-sm" onclick="rerunWithAI(${job.id})">
                            <i class="fas fa-robot"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    });
    
    if (migrations.length > 5) {
        html += `
            <div class="list-group-item text-center">
                <a href="/migration_jobs" class="btn btn-outline-primary btn-sm">
                    View All ${migrations.length} Active Migrations
                </a>
            </div>
        `;
    }
    
    html += '</div>';
    container.innerHTML = html;
}

/**
 * Update items needing attention
 */
function updateAttentionItems(items) {
    const container = document.getElementById('attention-items-container');
    const countBadge = document.getElementById('attention-count');
    
    if (!container || !countBadge) return;
    
    countBadge.textContent = items.length;
    
    if (items.length === 0) {
        container.innerHTML = `
            <div class="text-center py-3">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <p class="text-muted small">All good!</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="list-group list-group-flush">';
    
    items.slice(0, 3).forEach(item => {
        const priorityClass = item.priority === 'HIGH' ? 'text-danger' : 
                             item.priority === 'MEDIUM' ? 'text-warning' : 'text-info';
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${item.title}</h6>
                    <small class="${priorityClass}">${item.priority}</small>
                </div>
                <p class="mb-1 small">${item.description}</p>
                <small class="text-muted">${formatDate(item.created_at)}</small>
            </div>
        `;
    });
    
    if (items.length > 3) {
        html += `
            <div class="list-group-item text-center">
                <a href="/manual_interventions" class="btn btn-outline-warning btn-sm">
                    View All ${items.length} Items
                </a>
            </div>
        `;
    }
    
    html += '</div>';
    container.innerHTML = html;
}

/**
 * Update statistics
 */
function updateStatistics(stats) {
    const elements = {
        'total_modules': stats.total_modules,
        'analyzed_modules': stats.analyzed_modules,
        'pending_modules': stats.pending_modules,
        'error_modules': stats.error_modules
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.querySelector(`h4:contains("${id}")`);
        if (element) {
            element.textContent = value;
        }
    });
}

// ===== MIGRATION UPDATES =====

/**
 * Start migration page updates
 */
function startMigrationUpdates() {
    refreshTimers.migrations = setInterval(() => {
        if (isPageVisible) {
            refreshMigrationStatus();
        }
    }, REFRESH_INTERVALS.migrations);
}

/**
 * Refresh migration status
 */
async function refreshMigrationStatus() {
    try {
        const response = await fetch('/api/migration-status');
        if (!response.ok) throw new Error('Failed to fetch migration status');
        
        const data = await response.json();
        updateMigrationProgress(data.jobs);
        
    } catch (error) {
        console.error('Error refreshing migration status:', error);
    }
}

// ===== UTILITY FUNCTIONS =====

/**
 * Get status badge class
 */
function getStatusBadgeClass(status) {
    const statusClasses = {
        'QUEUED': 'secondary',
        'ANALYSIS': 'info',
        'CODE_TRANSFORMATION': 'primary',
        'VERSION_UPDATE': 'primary',
        'VISUAL_DIFF': 'warning',
        'AWAITING_APPROVAL': 'warning',
        'DIFF_APPROVED': 'info',
        'DB_MIGRATION': 'primary',
        'TESTING': 'info',
        'COMPLETED': 'success',
        'FAILED': 'danger',
        'ERROR': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    if (!dateString) return 'Recently';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

/**
 * Show loading indicator
 */
function showLoadingIndicator(component) {
    const indicator = document.getElementById(`${component}-loading`);
    if (indicator) {
        indicator.style.display = 'block';
    }
}

/**
 * Hide loading indicator
 */
function hideLoadingIndicator(component) {
    const indicator = document.getElementById(`${component}-loading`);
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// ===== PAGE VISIBILITY HANDLING =====

/**
 * Setup page visibility handling
 */
function setupVisibilityHandling() {
    document.addEventListener('visibilitychange', function() {
        isPageVisible = !document.hidden;
        
        if (isPageVisible) {
            // Resume updates when page becomes visible
            const currentPage = getCurrentPage();
            if (currentPage === 'dashboard' && !refreshTimers.dashboard) {
                startDashboardUpdates();
            }
        } else {
            // Pause updates when page is hidden
            Object.values(refreshTimers).forEach(timer => {
                if (timer) clearInterval(timer);
            });
            refreshTimers = {};
        }
    });
}

// ===== MANUAL REFRESH BUTTONS =====

/**
 * Setup refresh buttons
 */
function setupRefreshButtons() {
    // Dashboard refresh button
    window.refreshDashboard = function() {
        refreshDashboard();
    };
    
    // Migration refresh button
    window.refreshMigrations = function() {
        refreshMigrationStatus();
    };
}

// ===== NOTIFICATION SYSTEM =====

/**
 * Setup notification system
 */
function setupNotificationSystem() {
    // Create notification container if it doesn't exist
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'success' ? 'alert-success' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    container.appendChild(notification);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// ===== CLEANUP =====

/**
 * Cleanup timers when page unloads
 */
window.addEventListener('beforeunload', function() {
    Object.values(refreshTimers).forEach(timer => {
        if (timer) clearInterval(timer);
    });
});
