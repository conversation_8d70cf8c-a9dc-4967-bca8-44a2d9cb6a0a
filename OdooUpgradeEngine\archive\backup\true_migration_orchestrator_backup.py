# true_migration_orchestrator.py
from extensions import db
from models import Mi<PERSON><PERSON><PERSON>, Diff<PERSON><PERSON><PERSON>
from security_scanner import SecurityScanner
from ast_based_upgrader import ProfessionalASTUpgrader
from xml_safe_upgrader import XMLSafeUpgrader
from visual_diff_viewer import VisualDiffViewer
from database_migration_executor import DatabaseMigrationExecutor
from module_testing_engine import ModuleTestingEngine
from smart_version_updater import SmartVersionUpdater
import os
import shutil
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TrueMigrationOrchestrator:
    def __init__(self, job_id: int):
        self.job = MigrationJob.query.get(job_id)
        if not self.job:
            raise ValueError(f"MigrationJob with ID {job_id} not found.")
        self.work_dir = f"temp/migration_{self.job.id}"
        # Correctly determine module subdirectory name after unpacking
        self.original_module_root = None
        self.upgraded_module_root = None
        self.log = []
        if self.job.log:
            self.log = self.job.log.split('\n')

    def _setup_workspace(self):
        if os.path.exists(self.work_dir):
            shutil.rmtree(self.work_dir)
        os.makedirs(self.work_dir)
        original_unzip_path = os.path.join(self.work_dir, 'original')
        upgraded_unzip_path = os.path.join(self.work_dir, 'upgraded')
        
        shutil.unpack_archive(self.job.module.path, original_unzip_path)
        shutil.unpack_archive(self.job.module.path, upgraded_unzip_path)
        
        # Dynamically find the subdirectory name
        self.original_module_root = os.path.join(original_unzip_path, os.listdir(original_unzip_path)[0])
        self.upgraded_module_root = os.path.join(upgraded_unzip_path, os.listdir(upgraded_unzip_path)[0])
        self.job.upgraded_module_path = self.upgraded_module_root
        self._add_log(f"Workspace created at {self.work_dir}")

    def _add_log(self, message):
        logging.info(f"Job {self.job.id}: {message}")
        self.log.append(message)

    def _commit_log_and_status(self):
        self.job.log = "\n".join(self.log)
        db.session.commit()

    def run_initial_phase(self):
        self._add_log(f"Orchestration started for module '{self.job.module.name}' to v{self.job.target_version}.")
        try:
            self._setup_workspace()
            self.job.status = 'ANALYSIS'
            self._commit_log_and_status()
            
            scanner = SecurityScanner(self.upgraded_module_root)
            security_issues = scanner.run_scan()
            self.job.security_report = "\n".join(security_issues) if security_issues else "No security issues found."
            self._add_log(f"Security scan complete. Found {len(security_issues)} issues.")

            self.job.status = 'CODE_TRANSFORMATION'
            self._commit_log_and_status()

            python_upgrader = ProfessionalASTUpgrader()
            python_upgrade_results = python_upgrader.upgrade_module(self.upgraded_module_root)
            self._add_log("AST-based Python transformation complete.")
            for change in python_upgrade_results.get('changes_summary', []):
                self._add_log(f"  - {change}")

            xml_upgrader = XMLSafeUpgrader(self.upgraded_module_root, self.job.target_version)
            xml_upgrader.upgrade_module()
            self._add_log("LXML-based XML transformation complete.")

            # Smart Version Update Phase
            self.job.status = 'VERSION_UPDATE'
            self._commit_log_and_status()

            version_updater = SmartVersionUpdater()
            manifest_path = os.path.join(self.upgraded_module_root, '__manifest__.py')
            version_result = version_updater.detect_and_update_version(manifest_path, self.job.target_version)

            if version_result['success']:
                self._add_log(f"Version updated: {version_result['original_version']} → {version_result['updated_version']}")
                self._add_log(f"Quote style: {version_result['quote_style']}, Pattern: {version_result['pattern_used']}")
            else:
                self._add_log(f"Version update failed: {version_result['error']}")
                # Continue anyway - version update failure shouldn't stop migration

            self.job.status = 'VISUAL_DIFF'
            self._commit_log_and_status()

            diff_viewer = VisualDiffViewer(self.original_module_root, self.upgraded_module_root)
            diff_html, report_path = diff_viewer.generate_diff_report(output_dir='uploads/diff_reports', prefix=f"job_{self.job.id}")
            diff_report = DiffReport(migration_job_id=self.job.id, report_path=report_path, content=diff_html)
            db.session.add(diff_report)
            self._add_log(f"Visual diff report generated at {report_path}.")

            self.job.status = 'AWAITING_APPROVAL'
            self._add_log("Migration is now paused, awaiting user approval.")
            self._commit_log_and_status()
        except Exception as e:
            logging.error(f"Job {self.job.id}: FAILED during initial phase. Error: {e}", exc_info=True)
            self.job.status = 'FAILED'
            self._add_log(f"Orchestration failed catastrophically: {str(e)}")
            self._commit_log_and_status()

    def continue_after_approval(self):
        self._add_log("User approval received. Resuming migration.")
        try:
            if self.job.status != 'DIFF_APPROVED':
                raise Exception("Job is not in the required 'DIFF_APPROVED' state to continue.")

            self.job.status = 'DB_MIGRATION'
            self._add_log("Beginning database migration phase.")
            self._commit_log_and_status()
            
            db_executor = DatabaseMigrationExecutor(self.job)
            db_success, db_log = db_executor.run_migration()
            self.log.extend(db_log.split('\n'))

            if not db_success:
                raise Exception("Database migration failed. See log for details.")

            self.job.status = 'TESTING'
            self._add_log("Database migration successful. Beginning automated testing phase.")
            self._commit_log_and_status()

            testing_engine = ModuleTestingEngine(self.job, db_executor.db_name)
            test_success, test_log = testing_engine.run_tests()
            self.log.extend(test_log.split('\n'))

            if not test_success:
                raise Exception("Automated testing failed. See log for details.")

            self.job.status = 'COMPLETED'
            self._add_log("All phases completed successfully. Migration is complete.")
            self._commit_log_and_status()
        except Exception as e:
            logging.error(f"Job {self.job.id}: FAILED during continuation phase. Error: {e}", exc_info=True)
            self.job.status = 'FAILED'
            self._add_log(f"Orchestration failed after approval: {str(e)}")
            self._commit_log_and_status()