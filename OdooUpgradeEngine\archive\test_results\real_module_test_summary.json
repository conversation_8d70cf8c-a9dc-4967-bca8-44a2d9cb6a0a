{"test_execution": {"timestamp": "2025-07-13T15:25:35.059776", "test_type": "Real Module Data Validation", "environment": "Test Branch", "port": 5003}, "results": {"total_tests": 10, "passed": 8, "failed": 1, "skipped": 1, "success_rate": "80.0%", "duration_seconds": 31.497833}, "detailed_results": [{"test": "Scan server-tools", "status": "PASS", "details": "Found 32 modules", "timestamp": "2025-07-13T15:25:04.038639"}, {"test": "Scan web", "status": "PASS", "details": "Found 34 modules", "timestamp": "2025-07-13T15:25:04.566062"}, {"test": "Scan account-financial-tools", "status": "PASS", "details": "Found 22 modules", "timestamp": "2025-07-13T15:25:04.984588"}, {"test": "<PERSON><PERSON><PERSON>", "status": "FAIL", "details": "Exception: HTTPConnectionPool(host='localhost', port=5003): Read timed out. (read timeout=30)", "timestamp": "2025-07-13T15:25:35.015237"}, {"test": "AI Analysis", "status": "SKIP", "details": "No migration jobs to analyze", "timestamp": "2025-07-13T15:25:35.015259"}, {"test": "Migration Status", "status": "PASS", "details": "Active: 0, Completed: 0", "timestamp": "2025-07-13T15:25:35.020156"}, {"test": "Dashboard Data", "status": "PASS", "details": "Modules: 0, <PERSON><PERSON>: 0", "timestamp": "2025-07-13T15:25:35.030092"}, {"test": "GitHub Sync Endpoint", "status": "PASS", "details": "Endpoint exists and validates input", "timestamp": "2025-07-13T15:25:35.032529"}, {"test": "GitHub Authentication Check", "status": "PASS", "details": "Validates module existence", "timestamp": "2025-07-13T15:25:35.036703"}, {"test": "GitHub Integration Page", "status": "PASS", "details": "Page loads with sync functionality", "timestamp": "2025-07-13T15:25:35.045005"}], "recommendations": [{"type": "WARNING", "message": "1 tests failed. Review required before merge.", "action": "REVIEW_FAILURES"}]}