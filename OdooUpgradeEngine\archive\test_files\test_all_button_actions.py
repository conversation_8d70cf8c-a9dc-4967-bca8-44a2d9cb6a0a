#!/usr/bin/env python3
"""
Test all button actions in the application by checking routes and testing functionality
"""
import requests
import re
import os
from urllib.parse import urlparse

BASE_URL = "http://localhost:5000"

def test_route(path, method="GET", description=""):
    """Test if a route is accessible"""
    try:
        url = f"{BASE_URL}{path}"
        if method == "GET":
            response = requests.get(url, timeout=5)
        elif method == "POST":
            response = requests.post(url, timeout=5)
        
        return {
            'path': path,
            'method': method,
            'status_code': response.status_code,
            'working': response.status_code in [200, 302, 404],  # 404 means route exists but resource not found
            'description': description,
            'response_size': len(response.content) if response.content else 0
        }
    except Exception as e:
        return {
            'path': path,
            'method': method,
            'status_code': 'ERROR',
            'working': False,
            'description': description,
            'error': str(e)
        }

def extract_routes_from_template(template_path):
    """Extract route patterns from template files"""
    routes = []
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Look for url_for patterns
    url_for_pattern = r"url_for\(['\"]([^'\"]+)['\"](?:,\s*[^)]+)?\)"
    matches = re.findall(url_for_pattern, content)
    
    for match in matches:
        if '.' in match:
            # Extract the route name after the blueprint
            route_name = match.split('.', 1)[1]
            routes.append(route_name)
    
    return routes

def test_main_navigation_routes():
    """Test main navigation routes"""
    nav_routes = [
        ('/', 'Dashboard'),
        ('/upload_modules', 'Upload Modules'),
        ('/analyze_modules', 'Analyze Modules'),
        ('/odoo_status', 'Odoo Status'),
        ('/bulk-migration', 'Bulk Migration'),
        ('/migration-jobs', 'Migration Jobs'),
        ('/manual-interventions', 'Manual Interventions'),
        ('/docker-environments', 'Docker Environments'),
        ('/github-integration', 'GitHub Integration'),
        ('/automation/', 'Automation'),
        ('/testing/', 'Testing'),
        ('/contribute', 'Contribute'),
        ('/ai_providers', 'AI Providers'),
    ]
    
    results = []
    print("=== TESTING MAIN NAVIGATION ROUTES ===")
    
    for route, description in nav_routes:
        result = test_route(route, "GET", description)
        results.append(result)
        status = "✅ WORKING" if result['working'] else "❌ BROKEN"
        print(f"{status} {route} ({description}) - Status: {result['status_code']}")
    
    return results

def test_form_routes():
    """Test form submission routes found in templates"""
    form_routes = [
        # From analyze_modules.html
        ('/analyze_module/', 'Analyze Module'),
        ('/fix_module/', 'Fix Module'),
        ('/advanced_upgrade/', 'Advanced Upgrade'),
        ('/reset_analysis/', 'Reset Analysis'),
        
        # From automation_dashboard.html
        ('/automation/initialize', 'Initialize Automation'),
        ('/automation/run_cycle', 'Run Automation Cycle'),
        ('/automation/sync_modules', 'Sync Modules'),
        
        # From module_details.html
        ('/recalculate_score/', 'Recalculate Score'),
        ('/delete_module/', 'Delete Module'),
        
        # From odoo_status.html
        ('/install_odoo', 'Install Odoo'),
        ('/update_odoo_config', 'Update Odoo Config'),
    ]
    
    results = []
    print("\n=== TESTING FORM SUBMISSION ROUTES ===")
    
    for route, description in form_routes:
        # Test GET first (some forms may show form)
        result = test_route(route, "GET", f"{description} (GET)")
        results.append(result)
        status = "✅ WORKING" if result['working'] else "❌ BROKEN"
        print(f"{status} {route} ({description}) - GET Status: {result['status_code']}")
    
    return results

def test_api_routes():
    """Test API routes"""
    api_routes = [
        ('/api/migration-jobs', 'Migration Jobs API'),
        ('/ai_providers/status', 'AI Providers Status'),
        ('/automation/status', 'Automation Status'),
        ('/testing/status', 'Testing Status'),
    ]
    
    results = []
    print("\n=== TESTING API ROUTES ===")
    
    for route, description in api_routes:
        result = test_route(route, "GET", description)
        results.append(result)
        status = "✅ WORKING" if result['working'] else "❌ BROKEN"
        print(f"{status} {route} ({description}) - Status: {result['status_code']}")
    
    return results

def main():
    print("COMPREHENSIVE BUTTON ACTION TESTING")
    print("===================================")
    
    all_results = []
    
    # Test main navigation
    nav_results = test_main_navigation_routes()
    all_results.extend(nav_results)
    
    # Test form routes
    form_results = test_form_routes()
    all_results.extend(form_results)
    
    # Test API routes
    api_results = test_api_routes()
    all_results.extend(api_results)
    
    # Summary
    print("\n=== SUMMARY ===")
    working_count = sum(1 for r in all_results if r['working'])
    total_count = len(all_results)
    
    print(f"Total routes tested: {total_count}")
    print(f"Working routes: {working_count}")
    print(f"Broken routes: {total_count - working_count}")
    print(f"Success rate: {working_count/total_count*100:.1f}%")
    
    # Show broken routes
    broken_routes = [r for r in all_results if not r['working']]
    if broken_routes:
        print("\n=== BROKEN ROUTES ===")
        for route in broken_routes:
            print(f"❌ {route['path']} ({route['description']}) - {route['status_code']}")
            if 'error' in route:
                print(f"   Error: {route['error']}")

if __name__ == '__main__':
    main()