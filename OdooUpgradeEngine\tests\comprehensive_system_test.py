#!/usr/bin/env python3
"""
Comprehensive System Test for Odoo Upgrade Engine
Tests all major functionality and validates system readiness
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any

class SystemTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.start_time = datetime.now()
        
    def log_test(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """Log test result"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if not success and details:
            print(f"   Details: {details}")
    
    def test_basic_connectivity(self):
        """Test basic server connectivity"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("Basic Connectivity", True, "Server is responding")
                return True
            else:
                self.log_test("Basic Connectivity", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Basic Connectivity", False, "Connection failed", str(e))
            return False
    
    def test_main_pages(self):
        """Test all main pages load correctly"""
        pages = [
            ('/', 'Dashboard'),
            ('/upload_modules', 'Upload Modules'),
            ('/github_integration', 'GitHub Integration'),
            ('/migration_jobs', 'Migration Jobs'),
            ('/migration_orchestrator', 'Migration Orchestrator'),
            ('/manual_interventions', 'Manual Interventions'),
            ('/analyze_modules', 'Analyze Modules'),
            ('/migration_results', 'Migration Results'),
            ('/review_queue', 'Review Queue'),
            ('/completed_migrations', 'Completed Migrations'),
            ('/success_reports', 'Success Reports'),
            ('/performance_analytics', 'Performance Analytics'),
            ('/migration_history', 'Migration History'),
            ('/testing_dashboard', 'Testing Dashboard'),
            ('/docker_environments', 'Docker Environments'),
            ('/test_results', 'Test Results'),
            ('/ai_providers', 'AI Providers'),
            ('/ai_learning_dashboard', 'AI Learning Dashboard'),
            ('/health_dashboard', 'Health Monitor'),
            ('/system_settings', 'System Settings')
        ]
        
        success_count = 0
        for path, name in pages:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200:
                    # Check if page contains expected content
                    if name.lower() in response.text.lower():
                        self.log_test(f"Page Load: {name}", True, f"Page loads correctly")
                        success_count += 1
                    else:
                        self.log_test(f"Page Load: {name}", False, f"Page content missing")
                else:
                    self.log_test(f"Page Load: {name}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"Page Load: {name}", False, "Request failed", str(e))
        
        overall_success = success_count == len(pages)
        self.log_test("All Pages Load", overall_success, f"{success_count}/{len(pages)} pages loaded successfully")
        return overall_success
    
    def test_api_endpoints(self):
        """Test API endpoints"""
        api_tests = [
            ('/api/dashboard-data', 'GET', None, 'Dashboard Data API'),
            ('/api/migration-status', 'GET', None, 'Migration Status API'),
            ('/api/migration-jobs', 'GET', None, 'Migration Jobs API'),
            ('/api/ai-learning-insights', 'GET', None, 'AI Learning Insights API'),
        ]
        
        success_count = 0
        for endpoint, method, data, name in api_tests:
            try:
                if method == 'GET':
                    response = self.session.get(f"{self.base_url}{endpoint}")
                else:
                    response = self.session.post(f"{self.base_url}{endpoint}", json=data)
                
                if response.status_code == 200:
                    try:
                        json_data = response.json()
                        if 'success' in json_data:
                            self.log_test(f"API: {name}", True, "API responds correctly")
                            success_count += 1
                        else:
                            self.log_test(f"API: {name}", False, "Invalid JSON response format")
                    except json.JSONDecodeError:
                        self.log_test(f"API: {name}", False, "Invalid JSON response")
                else:
                    self.log_test(f"API: {name}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"API: {name}", False, "Request failed", str(e))
        
        overall_success = success_count == len(api_tests)
        self.log_test("All APIs Work", overall_success, f"{success_count}/{len(api_tests)} APIs working correctly")
        return overall_success
    
    def test_ai_provider_testing(self):
        """Test AI provider testing functionality"""
        test_configs = [
            {
                'provider': 'deepseek',
                'api_key': 'test_key_invalid',
                'model': 'deepseek-chat'
            },
            {
                'provider': 'ollama',
                'url': 'http://localhost:11434',
                'model': 'llama2'
            }
        ]
        
        success_count = 0
        for config in test_configs:
            try:
                response = self.session.post(
                    f"{self.base_url}/api/test-ai-provider",
                    json=config
                )
                
                if response.status_code == 200:
                    result = response.json()
                    # We expect these to fail with invalid credentials, but the endpoint should work
                    if 'success' in result:
                        self.log_test(f"AI Provider Test: {config['provider']}", True, "Test endpoint working")
                        success_count += 1
                    else:
                        self.log_test(f"AI Provider Test: {config['provider']}", False, "Invalid response format")
                else:
                    self.log_test(f"AI Provider Test: {config['provider']}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"AI Provider Test: {config['provider']}", False, "Request failed", str(e))
        
        overall_success = success_count == len(test_configs)
        self.log_test("AI Provider Testing", overall_success, f"{success_count}/{len(test_configs)} provider tests working")
        return overall_success
    
    def test_javascript_functionality(self):
        """Test that JavaScript files are accessible"""
        js_files = [
            '/static/js/main.js',
            '/static/js/ai-integration.js',
            '/static/js/real-time-updates.js',
            '/static/js/search-filter.js'
        ]
        
        success_count = 0
        for js_file in js_files:
            try:
                response = self.session.get(f"{self.base_url}{js_file}")
                if response.status_code == 200 and 'javascript' in response.headers.get('content-type', '').lower():
                    self.log_test(f"JavaScript: {js_file}", True, "File accessible")
                    success_count += 1
                else:
                    self.log_test(f"JavaScript: {js_file}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"JavaScript: {js_file}", False, "Request failed", str(e))
        
        overall_success = success_count == len(js_files)
        self.log_test("JavaScript Files", overall_success, f"{success_count}/{len(js_files)} JS files accessible")
        return overall_success
    
    def test_database_connectivity(self):
        """Test database operations"""
        try:
            # Test by checking if migration jobs API works (requires DB)
            response = self.session.get(f"{self.base_url}/api/migration-jobs")
            if response.status_code == 200:
                data = response.json()
                if 'success' in data:
                    self.log_test("Database Connectivity", True, "Database operations working")
                    return True
                else:
                    self.log_test("Database Connectivity", False, "Database query failed")
                    return False
            else:
                self.log_test("Database Connectivity", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Database Connectivity", False, "Database test failed", str(e))
            return False
    
    def test_menu_navigation(self):
        """Test menu navigation structure"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                content = response.text.lower()
                
                # Check for main menu sections
                menu_sections = [
                    'start migration',
                    'process & monitor',
                    'review & approve',
                    'completed & history',
                    'testing & validation',
                    'configure & settings'
                ]
                
                found_sections = 0
                for section in menu_sections:
                    if section in content:
                        found_sections += 1
                
                if found_sections >= 4:  # At least 4 out of 6 sections should be present
                    self.log_test("Menu Navigation", True, f"Menu structure present ({found_sections}/6 sections)")
                    return True
                else:
                    self.log_test("Menu Navigation", False, f"Menu structure incomplete ({found_sections}/6 sections)")
                    return False
            else:
                self.log_test("Menu Navigation", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Menu Navigation", False, "Menu test failed", str(e))
            return False
    
    def test_responsive_design(self):
        """Test responsive design elements"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                content = response.text.lower()
                
                # Check for Bootstrap classes and responsive elements
                responsive_elements = [
                    'col-md-',
                    'col-lg-',
                    'container-fluid',
                    'row',
                    'card',
                    'btn',
                    'navbar'
                ]
                
                found_elements = 0
                for element in responsive_elements:
                    if element in content:
                        found_elements += 1
                
                if found_elements >= 5:
                    self.log_test("Responsive Design", True, f"Bootstrap elements present ({found_elements}/7)")
                    return True
                else:
                    self.log_test("Responsive Design", False, f"Missing responsive elements ({found_elements}/7)")
                    return False
            else:
                self.log_test("Responsive Design", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Responsive Design", False, "Responsive test failed", str(e))
            return False
    
    def run_comprehensive_test(self):
        """Run all tests"""
        print("🚀 Starting Comprehensive System Test")
        print("=" * 50)
        
        # Run all test categories
        tests = [
            self.test_basic_connectivity,
            self.test_main_pages,
            self.test_api_endpoints,
            self.test_ai_provider_testing,
            self.test_javascript_functionality,
            self.test_database_connectivity,
            self.test_menu_navigation,
            self.test_responsive_design
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            if test():
                passed_tests += 1
            print()  # Add spacing between test categories
        
        # Generate summary report
        self.generate_summary_report(passed_tests, total_tests)
        
        return passed_tests == total_tests
    
    def generate_summary_report(self, passed_tests: int, total_tests: int):
        """Generate comprehensive test summary"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("=" * 50)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 50)
        
        print(f"🕒 Test Duration: {duration.total_seconds():.2f} seconds")
        print(f"📈 Overall Success Rate: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
        
        # Count individual test results
        individual_passed = sum(1 for result in self.test_results if result['success'])
        individual_total = len(self.test_results)
        
        print(f"🔍 Individual Tests: {individual_passed}/{individual_total} ({(individual_passed/individual_total)*100:.1f}%)")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['success']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"   - {test['test_name']}: {test['message']}")
        
        # Overall assessment
        if passed_tests == total_tests:
            print("\n🎉 SYSTEM STATUS: READY FOR PRODUCTION")
            print("✅ All major components are working correctly")
        elif passed_tests >= total_tests * 0.8:
            print("\n⚠️  SYSTEM STATUS: MOSTLY READY")
            print("🔧 Minor issues detected, but system is functional")
        else:
            print("\n🚨 SYSTEM STATUS: NEEDS ATTENTION")
            print("❌ Major issues detected, system may not be ready")
        
        # Save detailed report
        self.save_detailed_report()
    
    def save_detailed_report(self):
        """Save detailed test report to file"""
        report = {
            'test_summary': {
                'start_time': self.start_time.isoformat(),
                'end_time': datetime.now().isoformat(),
                'total_tests': len(self.test_results),
                'passed_tests': sum(1 for r in self.test_results if r['success']),
                'failed_tests': sum(1 for r in self.test_results if not r['success'])
            },
            'detailed_results': self.test_results
        }
        
        with open('comprehensive_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: comprehensive_test_report.json")

def main():
    """Main test execution"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"🎯 Testing Odoo Upgrade Engine at: {base_url}")
    print("⏳ Please ensure the application is running...")
    print()
    
    tester = SystemTester(base_url)
    success = tester.run_comprehensive_test()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
