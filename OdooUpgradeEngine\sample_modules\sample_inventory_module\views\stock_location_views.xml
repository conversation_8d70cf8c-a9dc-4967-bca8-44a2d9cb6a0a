<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Stock Location Form View -->
        <record id="view_location_form_enhanced" model="ir.ui.view">
            <field name="name">stock.location.form.enhanced</field>
            <field name="model">stock.location</field>
            <field name="inherit_id" ref="stock.view_location_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='usage']" position="after">
                    <field name="location_type"/>
                    <field name="capacity"/>
                    <field name="current_usage" readonly="1"/>
                </xpath>
                <xpath expr="//group[last()]" position="after">
                    <group string="Temperature Control" attrs="{'invisible': [('temperature_controlled', '=', False)]}">
                        <field name="temperature_controlled"/>
                        <field name="temperature_min" attrs="{'required': [('temperature_controlled', '=', True)]}"/>
                        <field name="temperature_max" attrs="{'required': [('temperature_controlled', '=', True)]}"/>
                    </group>
                    <group string="Inventory Management">
                        <field name="last_inventory_date"/>
                        <field name="inventory_frequency"/>
                        <field name="next_inventory_date" readonly="1"/>
                    </group>
                </xpath>
                <xpath expr="//header" position="inside">
                    <button name="action_schedule_inventory" type="object" string="Schedule Inventory" 
                            class="btn-primary" attrs="{'invisible': [('usage', '!=', 'internal')]}"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Stock Location Tree View -->
        <record id="view_location_tree_enhanced" model="ir.ui.view">
            <field name="name">stock.location.tree.enhanced</field>
            <field name="model">stock.location</field>
            <field name="inherit_id" ref="stock.view_location_tree2"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='usage']" position="after">
                    <field name="location_type"/>
                    <field name="current_usage"/>
                    <field name="next_inventory_date"/>
                </xpath>
            </field>
        </record>

        <!-- Location Capacity Analysis View -->
        <record id="view_location_capacity_graph" model="ir.ui.view">
            <field name="name">stock.location.capacity.graph</field>
            <field name="model">stock.location</field>
            <field name="arch" type="xml">
                <graph string="Location Capacity Analysis" type="bar">
                    <field name="name"/>
                    <field name="current_usage" type="measure"/>
                </graph>
            </field>
        </record>

        <!-- Location Menu Actions -->
        <record id="action_location_capacity_analysis" model="ir.actions.act_window">
            <field name="name">Location Capacity Analysis</field>
            <field name="res_model">stock.location</field>
            <field name="view_mode">graph,tree,form</field>
            <field name="domain">[('usage', '=', 'internal'), ('capacity', '>', 0)]</field>
            <field name="context">{'search_default_filter_internal': 1}</field>
        </record>
    </data>
</odoo>