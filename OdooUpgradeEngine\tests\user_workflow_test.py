#!/usr/bin/env python3
"""
User Workflow Test for Odoo Upgrade Engine
Tests complete user workflows and scenarios
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any

class WorkflowTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.workflow_results = []
        
    def log_workflow_step(self, workflow: str, step: str, success: bool, message: str = "", details: Any = None):
        """Log workflow step result"""
        result = {
            'workflow': workflow,
            'step': step,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.workflow_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"  {status} {step}: {message}")
        
        if not success and details:
            print(f"     Details: {details}")
    
    def test_new_user_onboarding_workflow(self):
        """Test complete new user onboarding workflow"""
        print("🚀 Testing: New User Onboarding Workflow")
        workflow_name = "New User Onboarding"
        
        # Step 1: Access dashboard
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200 and "dashboard" in response.text.lower():
                self.log_workflow_step(workflow_name, "Access Dashboard", True, "Dashboard loads successfully")
            else:
                self.log_workflow_step(workflow_name, "Access Dashboard", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Access Dashboard", False, "Connection failed", str(e))
            return False
        
        # Step 2: Navigate to AI Providers setup
        try:
            response = self.session.get(f"{self.base_url}/ai_providers")
            if response.status_code == 200 and "ai provider" in response.text.lower():
                self.log_workflow_step(workflow_name, "Access AI Providers", True, "AI providers page accessible")
            else:
                self.log_workflow_step(workflow_name, "Access AI Providers", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Access AI Providers", False, "Request failed", str(e))
            return False
        
        # Step 3: Check setup wizard availability
        try:
            if "setup wizard" in response.text.lower() or "setup-wizard" in response.text.lower():
                self.log_workflow_step(workflow_name, "Setup Wizard Available", True, "Setup wizard found on page")
            else:
                self.log_workflow_step(workflow_name, "Setup Wizard Available", False, "Setup wizard not found")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Setup Wizard Available", False, "Check failed", str(e))
            return False
        
        # Step 4: Navigate to upload modules
        try:
            response = self.session.get(f"{self.base_url}/upload_modules")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Access Upload Page", True, "Upload page accessible")
            else:
                self.log_workflow_step(workflow_name, "Access Upload Page", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Access Upload Page", False, "Request failed", str(e))
            return False
        
        # Step 5: Check GitHub integration
        try:
            response = self.session.get(f"{self.base_url}/github_integration")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "GitHub Integration", True, "GitHub integration accessible")
            else:
                self.log_workflow_step(workflow_name, "GitHub Integration", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "GitHub Integration", False, "Request failed", str(e))
            return False
        
        return True
    
    def test_migration_workflow(self):
        """Test complete migration workflow"""
        print("🔄 Testing: Migration Workflow")
        workflow_name = "Migration Workflow"
        
        # Step 1: Access migration orchestrator
        try:
            response = self.session.get(f"{self.base_url}/migration_orchestrator")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Migration Orchestrator", True, "Orchestrator accessible")
            else:
                self.log_workflow_step(workflow_name, "Migration Orchestrator", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Migration Orchestrator", False, "Request failed", str(e))
            return False
        
        # Step 2: Check migration jobs
        try:
            response = self.session.get(f"{self.base_url}/migration_jobs")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Migration Jobs", True, "Jobs page accessible")
            else:
                self.log_workflow_step(workflow_name, "Migration Jobs", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Migration Jobs", False, "Request failed", str(e))
            return False
        
        # Step 3: Test API for migration jobs
        try:
            response = self.session.get(f"{self.base_url}/api/migration-jobs")
            if response.status_code == 200:
                data = response.json()
                if 'success' in data:
                    self.log_workflow_step(workflow_name, "Migration Jobs API", True, "API working correctly")
                else:
                    self.log_workflow_step(workflow_name, "Migration Jobs API", False, "Invalid API response")
                    return False
            else:
                self.log_workflow_step(workflow_name, "Migration Jobs API", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Migration Jobs API", False, "API request failed", str(e))
            return False
        
        # Step 4: Check manual interventions
        try:
            response = self.session.get(f"{self.base_url}/manual_interventions")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Manual Interventions", True, "Manual interventions accessible")
            else:
                self.log_workflow_step(workflow_name, "Manual Interventions", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Manual Interventions", False, "Request failed", str(e))
            return False
        
        # Step 5: Check results pages
        try:
            response = self.session.get(f"{self.base_url}/migration_results")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Migration Results", True, "Results page accessible")
            else:
                self.log_workflow_step(workflow_name, "Migration Results", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Migration Results", False, "Request failed", str(e))
            return False
        
        return True
    
    def test_ai_integration_workflow(self):
        """Test AI integration workflow"""
        print("🤖 Testing: AI Integration Workflow")
        workflow_name = "AI Integration Workflow"
        
        # Step 1: Access AI learning dashboard
        try:
            response = self.session.get(f"{self.base_url}/ai_learning_dashboard")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "AI Learning Dashboard", True, "Dashboard accessible")
            else:
                self.log_workflow_step(workflow_name, "AI Learning Dashboard", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "AI Learning Dashboard", False, "Request failed", str(e))
            return False
        
        # Step 2: Test AI learning insights API
        try:
            response = self.session.get(f"{self.base_url}/api/ai-learning-insights")
            if response.status_code == 200:
                data = response.json()
                if 'success' in data:
                    self.log_workflow_step(workflow_name, "AI Learning API", True, "Learning API working")
                else:
                    self.log_workflow_step(workflow_name, "AI Learning API", False, "Invalid API response")
                    return False
            else:
                self.log_workflow_step(workflow_name, "AI Learning API", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "AI Learning API", False, "API request failed", str(e))
            return False
        
        # Step 3: Test AI provider testing
        test_config = {
            'provider': 'deepseek',
            'api_key': 'test_invalid_key',
            'model': 'deepseek-chat'
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/test-ai-provider", json=test_config)
            if response.status_code == 200:
                data = response.json()
                if 'success' in data:
                    self.log_workflow_step(workflow_name, "AI Provider Testing", True, "Provider testing API working")
                else:
                    self.log_workflow_step(workflow_name, "AI Provider Testing", False, "Invalid API response")
                    return False
            else:
                self.log_workflow_step(workflow_name, "AI Provider Testing", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "AI Provider Testing", False, "API request failed", str(e))
            return False
        
        return True
    
    def test_monitoring_workflow(self):
        """Test monitoring and analytics workflow"""
        print("📊 Testing: Monitoring Workflow")
        workflow_name = "Monitoring Workflow"
        
        # Step 1: Health dashboard
        try:
            response = self.session.get(f"{self.base_url}/health_dashboard")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Health Dashboard", True, "Health dashboard accessible")
            else:
                self.log_workflow_step(workflow_name, "Health Dashboard", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Health Dashboard", False, "Request failed", str(e))
            return False
        
        # Step 2: Performance analytics
        try:
            response = self.session.get(f"{self.base_url}/performance_analytics")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Performance Analytics", True, "Analytics accessible")
            else:
                self.log_workflow_step(workflow_name, "Performance Analytics", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Performance Analytics", False, "Request failed", str(e))
            return False
        
        # Step 3: Test real-time dashboard data
        try:
            response = self.session.get(f"{self.base_url}/api/dashboard-data")
            if response.status_code == 200:
                data = response.json()
                if 'success' in data and 'statistics' in data:
                    self.log_workflow_step(workflow_name, "Real-time Dashboard", True, "Dashboard data API working")
                else:
                    self.log_workflow_step(workflow_name, "Real-time Dashboard", False, "Invalid dashboard data")
                    return False
            else:
                self.log_workflow_step(workflow_name, "Real-time Dashboard", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Real-time Dashboard", False, "API request failed", str(e))
            return False
        
        return True
    
    def test_testing_workflow(self):
        """Test testing and validation workflow"""
        print("🧪 Testing: Testing Workflow")
        workflow_name = "Testing Workflow"
        
        # Step 1: Testing dashboard
        try:
            response = self.session.get(f"{self.base_url}/testing_dashboard")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Testing Dashboard", True, "Testing dashboard accessible")
            else:
                self.log_workflow_step(workflow_name, "Testing Dashboard", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Testing Dashboard", False, "Request failed", str(e))
            return False
        
        # Step 2: Docker environments
        try:
            response = self.session.get(f"{self.base_url}/docker_environments")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Docker Environments", True, "Docker environments accessible")
            else:
                self.log_workflow_step(workflow_name, "Docker Environments", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Docker Environments", False, "Request failed", str(e))
            return False
        
        # Step 3: Test results
        try:
            response = self.session.get(f"{self.base_url}/test_results")
            if response.status_code == 200:
                self.log_workflow_step(workflow_name, "Test Results", True, "Test results accessible")
            else:
                self.log_workflow_step(workflow_name, "Test Results", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_workflow_step(workflow_name, "Test Results", False, "Request failed", str(e))
            return False
        
        return True
    
    def run_all_workflow_tests(self):
        """Run all workflow tests"""
        print("🎯 Starting User Workflow Tests")
        print("=" * 50)
        
        workflows = [
            ("New User Onboarding", self.test_new_user_onboarding_workflow),
            ("Migration Process", self.test_migration_workflow),
            ("AI Integration", self.test_ai_integration_workflow),
            ("Monitoring & Analytics", self.test_monitoring_workflow),
            ("Testing & Validation", self.test_testing_workflow)
        ]
        
        passed_workflows = 0
        total_workflows = len(workflows)
        
        for workflow_name, workflow_test in workflows:
            print(f"\n🔍 {workflow_name}:")
            if workflow_test():
                passed_workflows += 1
                print(f"✅ {workflow_name} workflow completed successfully")
            else:
                print(f"❌ {workflow_name} workflow failed")
        
        # Generate workflow summary
        self.generate_workflow_summary(passed_workflows, total_workflows)
        
        return passed_workflows == total_workflows
    
    def generate_workflow_summary(self, passed_workflows: int, total_workflows: int):
        """Generate workflow test summary"""
        print("\n" + "=" * 50)
        print("📊 WORKFLOW TEST SUMMARY")
        print("=" * 50)
        
        success_rate = (passed_workflows / total_workflows) * 100
        print(f"🎯 Workflow Success Rate: {passed_workflows}/{total_workflows} ({success_rate:.1f}%)")
        
        # Count individual steps
        total_steps = len(self.workflow_results)
        passed_steps = sum(1 for result in self.workflow_results if result['success'])
        step_success_rate = (passed_steps / total_steps) * 100 if total_steps > 0 else 0
        
        print(f"📋 Individual Steps: {passed_steps}/{total_steps} ({step_success_rate:.1f}%)")
        
        # Show failed steps
        failed_steps = [result for result in self.workflow_results if not result['success']]
        if failed_steps:
            print("\n❌ FAILED WORKFLOW STEPS:")
            for step in failed_steps:
                print(f"   - {step['workflow']} → {step['step']}: {step['message']}")
        
        # Overall assessment
        if passed_workflows == total_workflows:
            print("\n🎉 USER EXPERIENCE: EXCELLENT")
            print("✅ All user workflows are working correctly")
        elif passed_workflows >= total_workflows * 0.8:
            print("\n⚠️  USER EXPERIENCE: GOOD")
            print("🔧 Minor workflow issues, but system is usable")
        else:
            print("\n🚨 USER EXPERIENCE: NEEDS IMPROVEMENT")
            print("❌ Major workflow issues detected")
        
        # Save workflow report
        self.save_workflow_report()
    
    def save_workflow_report(self):
        """Save workflow test report"""
        report = {
            'test_summary': {
                'timestamp': datetime.now().isoformat(),
                'total_workflows': len(set(r['workflow'] for r in self.workflow_results)),
                'total_steps': len(self.workflow_results),
                'passed_steps': sum(1 for r in self.workflow_results if r['success']),
                'failed_steps': sum(1 for r in self.workflow_results if not r['success'])
            },
            'workflow_results': self.workflow_results
        }
        
        with open('workflow_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Workflow report saved to: workflow_test_report.json")

def main():
    """Main workflow test execution"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"🎯 Testing User Workflows at: {base_url}")
    print("⏳ Please ensure the application is running...")
    print()
    
    tester = WorkflowTester(base_url)
    success = tester.run_all_workflow_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
